{"version": 3, "sources": ["../src/dropdown-menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst DROPDOWN_MENU_NAME = 'DropdownMenu';\n\ntype ScopedProps<P> = P & { __scopeDropdownMenu?: Scope };\nconst [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nconst useMenuScope = createMenuScope();\n\ntype DropdownMenuContextValue = {\n  triggerId: string;\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DropdownMenuProvider, useDropdownMenuContext] =\n  createDropdownMenuContext<DropdownMenuContextValue>(DROPDOWN_MENU_NAME);\n\ninterface DropdownMenuProps {\n  children?: React.ReactNode;\n  dir?: Direction;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst DropdownMenu: React.FC<DropdownMenuProps> = (props: ScopedProps<DropdownMenuProps>) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DROPDOWN_MENU_NAME,\n  });\n\n  return (\n    <DropdownMenuProvider\n      scope={__scopeDropdownMenu}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      <MenuPrimitive.Root {...menuScope} open={open} onOpenChange={setOpen} dir={dir} modal={modal}>\n        {children}\n      </MenuPrimitive.Root>\n    </DropdownMenuProvider>\n  );\n};\n\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DropdownMenuTrigger';\n\ntype DropdownMenuTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DropdownMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst DropdownMenuTrigger = React.forwardRef<DropdownMenuTriggerElement, DropdownMenuTriggerProps>(\n  (props: ScopedProps<DropdownMenuTriggerProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return (\n      <MenuPrimitive.Anchor asChild {...menuScope}>\n        <Primitive.button\n          type=\"button\"\n          id={context.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={context.open ? context.contentId : undefined}\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          {...triggerProps}\n          ref={composeRefs(forwardedRef, context.triggerRef)}\n          onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onOpenToggle();\n              // prevent trigger focusing when opening\n              // this allows the content to be given focus without competition\n              if (!context.open) event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (disabled) return;\n            if (['Enter', ' '].includes(event.key)) context.onOpenToggle();\n            if (event.key === 'ArrowDown') context.onOpenChange(true);\n            // prevent keydown from scrolling window / first focused item to execute\n            // that keydown (inadvertently closing the menu)\n            if (['Enter', ' ', 'ArrowDown'].includes(event.key)) event.preventDefault();\n          })}\n        />\n      </MenuPrimitive.Anchor>\n    );\n  }\n);\n\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DropdownMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface DropdownMenuPortalProps extends MenuPortalProps {}\n\nconst DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = (\n  props: ScopedProps<DropdownMenuPortalProps>\n) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nDropdownMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DropdownMenuContent';\n\ntype DropdownMenuContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface DropdownMenuContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst DropdownMenuContent = React.forwardRef<DropdownMenuContentElement, DropdownMenuContentProps>(\n  (props: ScopedProps<DropdownMenuContentProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={context.contentId}\n        aria-labelledby={context.triggerId}\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent as PointerEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        })}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-dropdown-menu-content-transform-origin':\n              'var(--radix-popper-transform-origin)',\n            '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-dropdown-menu-content-available-height':\n              'var(--radix-popper-available-height)',\n            '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nDropdownMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'DropdownMenuGroup';\n\ntype DropdownMenuGroupElement = React.ComponentRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface DropdownMenuGroupProps extends MenuGroupProps {}\n\nconst DropdownMenuGroup = React.forwardRef<DropdownMenuGroupElement, DropdownMenuGroupProps>(\n  (props: ScopedProps<DropdownMenuGroupProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'DropdownMenuLabel';\n\ntype DropdownMenuLabelElement = React.ComponentRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface DropdownMenuLabelProps extends MenuLabelProps {}\n\nconst DropdownMenuLabel = React.forwardRef<DropdownMenuLabelElement, DropdownMenuLabelProps>(\n  (props: ScopedProps<DropdownMenuLabelProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'DropdownMenuItem';\n\ntype DropdownMenuItemElement = React.ComponentRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface DropdownMenuItemProps extends MenuItemProps {}\n\nconst DropdownMenuItem = React.forwardRef<DropdownMenuItemElement, DropdownMenuItemProps>(\n  (props: ScopedProps<DropdownMenuItemProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'DropdownMenuCheckboxItem';\n\ntype DropdownMenuCheckboxItemElement = React.ComponentRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface DropdownMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  DropdownMenuCheckboxItemElement,\n  DropdownMenuCheckboxItemProps\n>((props: ScopedProps<DropdownMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'DropdownMenuRadioGroup';\n\ntype DropdownMenuRadioGroupElement = React.ComponentRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface DropdownMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst DropdownMenuRadioGroup = React.forwardRef<\n  DropdownMenuRadioGroupElement,\n  DropdownMenuRadioGroupProps\n>((props: ScopedProps<DropdownMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'DropdownMenuRadioItem';\n\ntype DropdownMenuRadioItemElement = React.ComponentRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface DropdownMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  DropdownMenuRadioItemElement,\n  DropdownMenuRadioItemProps\n>((props: ScopedProps<DropdownMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'DropdownMenuItemIndicator';\n\ntype DropdownMenuItemIndicatorElement = React.ComponentRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface DropdownMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst DropdownMenuItemIndicator = React.forwardRef<\n  DropdownMenuItemIndicatorElement,\n  DropdownMenuItemIndicatorProps\n>((props: ScopedProps<DropdownMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'DropdownMenuSeparator';\n\ntype DropdownMenuSeparatorElement = React.ComponentRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface DropdownMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst DropdownMenuSeparator = React.forwardRef<\n  DropdownMenuSeparatorElement,\n  DropdownMenuSeparatorProps\n>((props: ScopedProps<DropdownMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'DropdownMenuArrow';\n\ntype DropdownMenuArrowElement = React.ComponentRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface DropdownMenuArrowProps extends MenuArrowProps {}\n\nconst DropdownMenuArrow = React.forwardRef<DropdownMenuArrowElement, DropdownMenuArrowProps>(\n  (props: ScopedProps<DropdownMenuArrowProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DropdownMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst DropdownMenuSub: React.FC<DropdownMenuSubProps> = (\n  props: ScopedProps<DropdownMenuSubProps>\n) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: 'DropdownMenuSub',\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'DropdownMenuSubTrigger';\n\ntype DropdownMenuSubTriggerElement = React.ComponentRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface DropdownMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  DropdownMenuSubTriggerElement,\n  DropdownMenuSubTriggerProps\n>((props: ScopedProps<DropdownMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...subTriggerProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'DropdownMenuSubContent';\n\ntype DropdownMenuSubContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface DropdownMenuSubContentProps extends MenuSubContentProps {}\n\nconst DropdownMenuSubContent = React.forwardRef<\n  DropdownMenuSubContentElement,\n  DropdownMenuSubContentProps\n>((props: ScopedProps<DropdownMenuSubContentProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-dropdown-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-dropdown-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = DropdownMenu;\nconst Trigger = DropdownMenuTrigger;\nconst Portal = DropdownMenuPortal;\nconst Content = DropdownMenuContent;\nconst Group = DropdownMenuGroup;\nconst Label = DropdownMenuLabel;\nconst Item = DropdownMenuItem;\nconst CheckboxItem = DropdownMenuCheckboxItem;\nconst RadioGroup = DropdownMenuRadioGroup;\nconst RadioItem = DropdownMenuRadioItem;\nconst ItemIndicator = DropdownMenuItemIndicator;\nconst Separator = DropdownMenuSeparator;\nconst Arrow = DropdownMenuArrow;\nconst Sub = DropdownMenuSub;\nconst SubTrigger = DropdownMenuSubTrigger;\nconst SubContent = DropdownMenuSubContent;\n\nexport {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n};\n"], "mappings": ";;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,mBAAmB;AAC5B,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AACrC,SAAS,iBAAiB;AAC1B,YAAY,mBAAmB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,aAAa;AAuEhB;AA7DN,IAAM,qBAAqB;AAG3B,IAAM,CAAC,2BAA2B,uBAAuB,IAAI;AAAA,EAC3D;AAAA,EACA,CAAC,eAAe;AAClB;AACA,IAAM,eAAe,gBAAgB;AAYrC,IAAM,CAAC,sBAAsB,sBAAsB,IACjD,0BAAoD,kBAAkB;AAWxE,IAAM,eAA4C,CAAC,UAA0C;AAC3F,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACV,IAAI;AACJ,QAAM,YAAY,aAAa,mBAAmB;AAClD,QAAM,aAAmB,aAA0B,IAAI;AACvD,QAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;AAAA,IAC3C,MAAM;AAAA,IACN,aAAa,eAAe;AAAA,IAC5B,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AAED,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,MACP,WAAW,MAAM;AAAA,MACjB;AAAA,MACA,WAAW,MAAM;AAAA,MACjB;AAAA,MACA,cAAc;AAAA,MACd,cAAoB,kBAAY,MAAM,QAAQ,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC;AAAA,MACjF;AAAA,MAEA,8BAAe,oBAAd,EAAoB,GAAG,WAAW,MAAY,cAAc,SAAS,KAAU,OAC7E,UACH;AAAA;AAAA,EACF;AAEJ;AAEA,aAAa,cAAc;AAM3B,IAAM,eAAe;AAMrB,IAAM,sBAA4B;AAAA,EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,qBAAqB,WAAW,OAAO,GAAG,aAAa,IAAI;AACnE,UAAM,UAAU,uBAAuB,cAAc,mBAAmB;AACxE,UAAM,YAAY,aAAa,mBAAmB;AAClD,WACE,oBAAe,sBAAd,EAAqB,SAAO,MAAE,GAAG,WAChC;AAAA,MAAC,UAAU;AAAA,MAAV;AAAA,QACC,MAAK;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,iBAAc;AAAA,QACd,iBAAe,QAAQ;AAAA,QACvB,iBAAe,QAAQ,OAAO,QAAQ,YAAY;AAAA,QAClD,cAAY,QAAQ,OAAO,SAAS;AAAA,QACpC,iBAAe,WAAW,KAAK;AAAA,QAC/B;AAAA,QACC,GAAG;AAAA,QACJ,KAAK,YAAY,cAAc,QAAQ,UAAU;AAAA,QACjD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAGlE,cAAI,CAAC,YAAY,MAAM,WAAW,KAAK,MAAM,YAAY,OAAO;AAC9D,oBAAQ,aAAa;AAGrB,gBAAI,CAAC,QAAQ,KAAM,OAAM,eAAe;AAAA,UAC1C;AAAA,QACF,CAAC;AAAA,QACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,cAAI,SAAU;AACd,cAAI,CAAC,SAAS,GAAG,EAAE,SAAS,MAAM,GAAG,EAAG,SAAQ,aAAa;AAC7D,cAAI,MAAM,QAAQ,YAAa,SAAQ,aAAa,IAAI;AAGxD,cAAI,CAAC,SAAS,KAAK,WAAW,EAAE,SAAS,MAAM,GAAG,EAAG,OAAM,eAAe;AAAA,QAC5E,CAAC;AAAA;AAAA,IACH,GACF;AAAA,EAEJ;AACF;AAEA,oBAAoB,cAAc;AAMlC,IAAM,cAAc;AAKpB,IAAM,qBAAwD,CAC5D,UACG;AACH,QAAM,EAAE,qBAAqB,GAAG,YAAY,IAAI;AAChD,QAAM,YAAY,aAAa,mBAAmB;AAClD,SAAO,oBAAe,sBAAd,EAAsB,GAAG,WAAY,GAAG,aAAa;AAC/D;AAEA,mBAAmB,cAAc;AAMjC,IAAM,eAAe;AAMrB,IAAM,sBAA4B;AAAA,EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,qBAAqB,GAAG,aAAa,IAAI;AACjD,UAAM,UAAU,uBAAuB,cAAc,mBAAmB;AACxE,UAAM,YAAY,aAAa,mBAAmB;AAClD,UAAM,0BAAgC,aAAO,KAAK;AAElD,WACE;AAAA,MAAe;AAAA,MAAd;AAAA,QACC,IAAI,QAAQ;AAAA,QACZ,mBAAiB,QAAQ;AAAA,QACxB,GAAG;AAAA,QACH,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,kBAAkB,qBAAqB,MAAM,kBAAkB,CAAC,UAAU;AACxE,cAAI,CAAC,wBAAwB,QAAS,SAAQ,WAAW,SAAS,MAAM;AACxE,kCAAwB,UAAU;AAElC,gBAAM,eAAe;AAAA,QACvB,CAAC;AAAA,QACD,mBAAmB,qBAAqB,MAAM,mBAAmB,CAAC,UAAU;AAC1E,gBAAM,gBAAgB,MAAM,OAAO;AACnC,gBAAM,gBAAgB,cAAc,WAAW,KAAK,cAAc,YAAY;AAC9E,gBAAM,eAAe,cAAc,WAAW,KAAK;AACnD,cAAI,CAAC,QAAQ,SAAS,aAAc,yBAAwB,UAAU;AAAA,QACxE,CAAC;AAAA,QACD,OAAO;AAAA,UACL,GAAG,MAAM;AAAA;AAAA,UAET,GAAG;AAAA,YACD,kDACE;AAAA,YACF,iDAAiD;AAAA,YACjD,kDACE;AAAA,YACF,uCAAuC;AAAA,YACvC,wCAAwC;AAAA,UAC1C;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,oBAAoB,cAAc;AAMlC,IAAM,aAAa;AAMnB,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,qBAAqB,GAAG,WAAW,IAAI;AAC/C,UAAM,YAAY,aAAa,mBAAmB;AAClD,WAAO,oBAAe,qBAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,aAAa;AAMnB,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,qBAAqB,GAAG,WAAW,IAAI;AAC/C,UAAM,YAAY,aAAa,mBAAmB;AAClD,WAAO,oBAAe,qBAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,YAAY;AAMlB,IAAM,mBAAyB;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,qBAAqB,GAAG,UAAU,IAAI;AAC9C,UAAM,YAAY,aAAa,mBAAmB;AAClD,WAAO,oBAAe,oBAAd,EAAoB,GAAG,WAAY,GAAG,WAAW,KAAK,cAAc;AAAA,EAC9E;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,qBAAqB;AAM3B,IAAM,2BAAiC,iBAGrC,CAAC,OAAmD,iBAAiB;AACrE,QAAM,EAAE,qBAAqB,GAAG,kBAAkB,IAAI;AACtD,QAAM,YAAY,aAAa,mBAAmB;AAClD,SAAO,oBAAe,4BAAd,EAA4B,GAAG,WAAY,GAAG,mBAAmB,KAAK,cAAc;AAC9F,CAAC;AAED,yBAAyB,cAAc;AAMvC,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,iBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,qBAAqB,GAAG,gBAAgB,IAAI;AACpD,QAAM,YAAY,aAAa,mBAAmB;AAClD,SAAO,oBAAe,0BAAd,EAA0B,GAAG,WAAY,GAAG,iBAAiB,KAAK,cAAc;AAC1F,CAAC;AAED,uBAAuB,cAAc;AAMrC,IAAM,kBAAkB;AAMxB,IAAM,wBAA8B,iBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,qBAAqB,GAAG,eAAe,IAAI;AACnD,QAAM,YAAY,aAAa,mBAAmB;AAClD,SAAO,oBAAe,yBAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,cAAc;AACxF,CAAC;AAED,sBAAsB,cAAc;AAMpC,IAAM,iBAAiB;AAMvB,IAAM,4BAAkC,iBAGtC,CAAC,OAAoD,iBAAiB;AACtE,QAAM,EAAE,qBAAqB,GAAG,mBAAmB,IAAI;AACvD,QAAM,YAAY,aAAa,mBAAmB;AAClD,SAAO,oBAAe,6BAAd,EAA6B,GAAG,WAAY,GAAG,oBAAoB,KAAK,cAAc;AAChG,CAAC;AAED,0BAA0B,cAAc;AAMxC,IAAM,iBAAiB;AAMvB,IAAM,wBAA8B,iBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,qBAAqB,GAAG,eAAe,IAAI;AACnD,QAAM,YAAY,aAAa,mBAAmB;AAClD,SAAO,oBAAe,yBAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,cAAc;AACxF,CAAC;AAED,sBAAsB,cAAc;AAMpC,IAAM,aAAa;AAMnB,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,qBAAqB,GAAG,WAAW,IAAI;AAC/C,UAAM,YAAY,aAAa,mBAAmB;AAClD,WAAO,oBAAe,qBAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,kBAAkB,cAAc;AAahC,IAAM,kBAAkD,CACtD,UACG;AACH,QAAM,EAAE,qBAAqB,UAAU,MAAM,UAAU,cAAc,YAAY,IAAI;AACrF,QAAM,YAAY,aAAa,mBAAmB;AAClD,QAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;AAAA,IAC3C,MAAM;AAAA,IACN,aAAa,eAAe;AAAA,IAC5B,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AAED,SACE,oBAAe,mBAAd,EAAmB,GAAG,WAAW,MAAY,cAAc,SACzD,UACH;AAEJ;AAMA,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,iBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,qBAAqB,GAAG,gBAAgB,IAAI;AACpD,QAAM,YAAY,aAAa,mBAAmB;AAClD,SAAO,oBAAe,0BAAd,EAA0B,GAAG,WAAY,GAAG,iBAAiB,KAAK,cAAc;AAC1F,CAAC;AAED,uBAAuB,cAAc;AAMrC,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,iBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,qBAAqB,GAAG,gBAAgB,IAAI;AACpD,QAAM,YAAY,aAAa,mBAAmB;AAElD,SACE;AAAA,IAAe;AAAA,IAAd;AAAA,MACE,GAAG;AAAA,MACH,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,OAAO;AAAA,QACL,GAAG,MAAM;AAAA;AAAA,QAET,GAAG;AAAA,UACD,kDAAkD;AAAA,UAClD,iDAAiD;AAAA,UACjD,kDAAkD;AAAA,UAClD,uCAAuC;AAAA,UACvC,wCAAwC;AAAA,QAC1C;AAAA,MACF;AAAA;AAAA,EACF;AAEJ,CAAC;AAED,uBAAuB,cAAc;AAIrC,IAAMA,QAAO;AACb,IAAM,UAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,SAAQ;AACd,IAAMC,SAAQ;AACd,IAAMC,QAAO;AACb,IAAMC,gBAAe;AACrB,IAAMC,cAAa;AACnB,IAAMC,aAAY;AAClB,IAAMC,iBAAgB;AACtB,IAAMC,aAAY;AAClB,IAAMC,SAAQ;AACd,IAAMC,OAAM;AACZ,IAAMC,cAAa;AACnB,IAAMC,cAAa;", "names": ["Root", "Portal", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent"]}