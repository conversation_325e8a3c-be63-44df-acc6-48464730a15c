(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2799:(e,t,a)=>{Promise.resolve().then(a.bind(a,6473))},6473:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>_});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}var l=a(4624);let d=(0,a(2085).F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function m(e){let{className:t,variant:a,asChild:n=!1,...i}=e,c=n?l.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,r.cn)(d({variant:a}),t),...i})}function u(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...a})})}function x(e){let{className:t,...a}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...a})}function b(e){let{className:t,...a}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}var f=a(3540),j=a(2832),v=a(4754),y=a(6025),N=a(2071),w=a(3778),k=a(5989),A=a(7580),D=a(7434),S=a(3109),I=a(1243),P=a(9397),C=a(4186),W=a(646);function _(){let e=(0,r._8)(),t=(0,r.tK)(),a=[{title:"Total Patients",value:e.totalPatients.toLocaleString(),icon:A.A,color:"bg-blue-100 border-blue-600 text-blue-800",change:"+12% from last month"},{title:"Active Cases",value:e.activeCases.toString(),icon:D.A,color:"bg-yellow-100 border-yellow-600 text-yellow-800",change:"3 critical cases"},{title:"Diagnoses This Week",value:e.diagnosesThisWeek.toString(),icon:S.A,color:"bg-green-100 border-green-600 text-green-800",change:"+8% from last week"},{title:"Alerts",value:e.alerts.toString(),icon:I.A,color:"bg-red-100 border-red-600 text-red-800",change:"2 require immediate attention"}];return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"heading-brutal",children:"Dashboard Overview"}),(0,s.jsx)("p",{className:"text-medical mt-2",children:"Welcome to the VetAI Diagnostic System. Monitor your practice's key metrics and recent activity."})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-green-100 border-2 border-green-600 rounded-xl",children:[(0,s.jsx)(P.A,{className:"h-5 w-5 text-green-600"}),(0,s.jsx)("span",{className:"font-bold text-green-800",children:"AI System Active"})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:a.map((e,t)=>{let a=e.icon;return(0,s.jsxs)(n,{className:"card-brutal",children:[(0,s.jsx)(i,{className:"pb-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(c,{className:"text-lg font-bold text-black",children:e.title}),(0,s.jsx)("div",{className:"p-2 rounded-xl border-2 ".concat(e.color),children:(0,s.jsx)(a,{className:"h-5 w-5"})})]})}),(0,s.jsxs)(o,{children:[(0,s.jsx)("div",{className:"text-3xl font-black text-black mb-2",children:e.value}),(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.change})]})]},t)})}),(0,s.jsxs)(n,{className:"card-brutal",children:[(0,s.jsxs)(i,{children:[(0,s.jsx)(c,{className:"subheading-brutal",children:"Disease Frequency Over Time"}),(0,s.jsx)("p",{className:"text-medical",children:"Track the most common conditions diagnosed in your practice"})]}),(0,s.jsx)(o,{children:(0,s.jsx)("div",{className:"h-80",children:(0,s.jsx)(f.u,{width:"100%",height:"100%",children:(0,s.jsxs)(j.b,{data:e.diseaseFrequency,children:[(0,s.jsx)(v.d,{strokeDasharray:"3 3",stroke:"#e5e7eb"}),(0,s.jsx)(y.W,{dataKey:"date",stroke:"#374151",fontSize:12,fontWeight:"600"}),(0,s.jsx)(N.h,{stroke:"#374151",fontSize:12,fontWeight:"600"}),(0,s.jsx)(w.m,{contentStyle:{backgroundColor:"white",border:"4px solid black",borderRadius:"12px",boxShadow:"4px 4px 0px 0px rgba(0,0,0,1)",fontWeight:"bold"}}),(0,s.jsx)(k.N,{type:"monotone",dataKey:"count",stroke:"#3B82F6",strokeWidth:4,dot:{fill:"#3B82F6",strokeWidth:2,r:6},activeDot:{r:8,stroke:"#1D4ED8",strokeWidth:2}})]})})})})]}),(0,s.jsxs)(n,{className:"card-brutal",children:[(0,s.jsxs)(i,{children:[(0,s.jsx)(c,{className:"subheading-brutal",children:"Recent Cases"}),(0,s.jsx)("p",{className:"text-medical",children:"Latest patient cases and their diagnostic status"})]}),(0,s.jsx)(o,{children:(0,s.jsxs)(u,{children:[(0,s.jsx)(x,{children:(0,s.jsxs)(b,{className:"border-b-2 border-black",children:[(0,s.jsx)(g,{className:"font-black text-black",children:"Animal ID"}),(0,s.jsx)(g,{className:"font-black text-black",children:"Species"}),(0,s.jsx)(g,{className:"font-black text-black",children:"Age"}),(0,s.jsx)(g,{className:"font-black text-black",children:"Symptoms"}),(0,s.jsx)(g,{className:"font-black text-black",children:"Diagnosis"}),(0,s.jsx)(g,{className:"font-black text-black",children:"Status"}),(0,s.jsx)(g,{className:"font-black text-black",children:"Date"})]})}),(0,s.jsx)(h,{children:t.map(e=>(0,s.jsxs)(b,{className:"border-b border-gray-200",children:[(0,s.jsx)(p,{className:"font-bold text-black",children:e.animalId}),(0,s.jsx)(p,{className:"font-medium",children:e.species}),(0,s.jsxs)(p,{className:"font-medium",children:[e.age," years"]}),(0,s.jsx)(p,{children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.symptoms.slice(0,2).map((e,t)=>(0,s.jsx)(m,{variant:"outline",className:"text-xs border-2 border-gray-400",children:e},t)),e.symptoms.length>2&&(0,s.jsxs)(m,{variant:"outline",className:"text-xs border-2 border-gray-400",children:["+",e.symptoms.length-2," more"]})]})}),(0,s.jsx)(p,{children:e.diagnosis?(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-black",children:e.diagnosis}),e.confidence&&(0,s.jsxs)("div",{className:"text-xs text-gray-600",children:[e.confidence,"% confidence"]})]}):(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"Pending"})}),(0,s.jsx)(p,{children:(0,s.jsxs)(m,{className:"".concat((0,r.qY)(e.status)," border-2 font-bold"),children:["pending"===e.status&&(0,s.jsx)(C.A,{className:"h-3 w-3 mr-1"}),"diagnosed"===e.status&&(0,s.jsx)(W.A,{className:"h-3 w-3 mr-1"}),"treated"===e.status&&(0,s.jsx)(W.A,{className:"h-3 w-3 mr-1"}),"follow-up"===e.status&&(0,s.jsx)(P.A,{className:"h-3 w-3 mr-1"}),e.status.charAt(0).toUpperCase()+e.status.slice(1)]})}),(0,s.jsx)(p,{className:"font-medium text-gray-700",children:(0,r.Yq)(e.createdAt)})]},e.id))})]})})]})]})}},9434:(e,t,a)=>{"use strict";a.d(t,{Yq:()=>i,_8:()=>o,cn:()=>n,qY:()=>c,tK:()=>l});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function i(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(e)}function c(e){switch(e.toLowerCase()){case"critical":return"status-critical";case"warning":case"pending":return"status-warning";case"stable":case"treated":return"status-stable";default:return"status-info"}}function o(){return{totalPatients:1247,activeCases:23,diagnosesThisWeek:89,alerts:3,diseaseFrequency:[{name:"Skin Allergies",count:15,date:"2024-01-01"},{name:"Respiratory Issues",count:12,date:"2024-01-02"},{name:"Digestive Problems",count:8,date:"2024-01-03"},{name:"Parasites",count:6,date:"2024-01-04"},{name:"Dental Issues",count:4,date:"2024-01-05"}],commonDiagnoses:[{name:"Skin Allergies",count:45},{name:"Upper Respiratory Infection",count:32},{name:"Gastroenteritis",count:28},{name:"Ear Infection",count:24},{name:"Dental Disease",count:18}],outcomeRates:[{name:"Recovery",value:78,color:"#10B981"},{name:"Ongoing Treatment",value:18,color:"#F59E0B"},{name:"Complications",value:4,color:"#EF4444"}]}}function l(){return[{id:"1",patientId:"p1",animalId:"A001",species:"Dog",breed:"Golden Retriever",age:3,symptoms:["Lethargy","Loss of appetite","Vomiting"],temperature:102.5,diagnosis:"Gastroenteritis",confidence:85,status:"diagnosed",notes:"Patient showing signs of mild gastroenteritis. Prescribed medication and dietary changes.",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-08"),veterinarian:"Dr. Smith"},{id:"2",patientId:"p2",animalId:"A002",species:"Cat",breed:"Persian",age:5,symptoms:["Coughing","Sneezing","Discharge from eyes"],temperature:101.8,diagnosis:"Upper Respiratory Infection",confidence:92,status:"treated",notes:"Typical URI symptoms. Antibiotics prescribed. Follow-up in 1 week.",createdAt:new Date("2024-01-07"),updatedAt:new Date("2024-01-08"),veterinarian:"Dr. Johnson"},{id:"3",patientId:"p3",animalId:"A003",species:"Dog",breed:"Labrador",age:2,symptoms:["Limping","Pain","Swelling"],status:"pending",notes:"Possible injury to front left paw. X-rays ordered.",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-08"),veterinarian:"Dr. Williams"}]}}},e=>{var t=t=>e(e.s=t);e.O(0,[709,493,441,684,358],()=>t(2799)),_N_E=e.O()}]);