[{"C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\sidebar.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\alert-dialog.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\avatar.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\badge.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\button.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\card.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\dropdown-menu.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\form.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\input.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\label.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\progress.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\select.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\separator.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\table.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\tabs.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\textarea.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\lib\\utils.ts": "19"}, {"size": 940, "mtime": 1751978409830, "results": "20", "hashOfConfig": "21"}, {"size": 8567, "mtime": 1751978510451, "results": "22", "hashOfConfig": "21"}, {"size": 4371, "mtime": 1751978393335, "results": "23", "hashOfConfig": "21"}, {"size": 3864, "mtime": 1751978132567, "results": "24", "hashOfConfig": "21"}, {"size": 1097, "mtime": 1751978132753, "results": "25", "hashOfConfig": "21"}, {"size": 1631, "mtime": 1751978132529, "results": "26", "hashOfConfig": "21"}, {"size": 2123, "mtime": 1751978132416, "results": "27", "hashOfConfig": "21"}, {"size": 1989, "mtime": 1751978132453, "results": "28", "hashOfConfig": "21"}, {"size": 8284, "mtime": 1751978132723, "results": "29", "hashOfConfig": "21"}, {"size": 3759, "mtime": 1751978132646, "results": "30", "hashOfConfig": "21"}, {"size": 967, "mtime": 1751978132459, "results": "31", "hashOfConfig": "21"}, {"size": 611, "mtime": 1751978132469, "results": "32", "hashOfConfig": "21"}, {"size": 740, "mtime": 1751978132538, "results": "33", "hashOfConfig": "21"}, {"size": 6253, "mtime": 1751978132497, "results": "34", "hashOfConfig": "21"}, {"size": 699, "mtime": 1751978132765, "results": "35", "hashOfConfig": "21"}, {"size": 2448, "mtime": 1751978132516, "results": "36", "hashOfConfig": "21"}, {"size": 1969, "mtime": 1751978132579, "results": "37", "hashOfConfig": "21"}, {"size": 759, "mtime": 1751978132502, "results": "38", "hashOfConfig": "21"}, {"size": 5158, "mtime": 1751978347781, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dz202g", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\app\\page.tsx", ["97", "98"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\sidebar.tsx", ["99"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\lib\\utils.ts", [], [], {"ruleId": "100", "severity": 2, "message": "101", "line": 15, "column": 3, "nodeType": null, "messageId": "102", "endLine": 15, "endColumn": 10}, {"ruleId": "103", "severity": 2, "message": "104", "line": 61, "column": 74, "nodeType": "105", "messageId": "106", "suggestions": "107"}, {"ruleId": "103", "severity": 2, "message": "104", "line": 117, "column": 70, "nodeType": "105", "messageId": "106", "suggestions": "108"}, "@typescript-eslint/no-unused-vars", "'XCircle' is defined but never used.", "unusedVar", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["109", "110", "111", "112"], ["113", "114", "115", "116"], {"messageId": "117", "data": "118", "fix": "119", "desc": "120"}, {"messageId": "117", "data": "121", "fix": "122", "desc": "123"}, {"messageId": "117", "data": "124", "fix": "125", "desc": "126"}, {"messageId": "117", "data": "127", "fix": "128", "desc": "129"}, {"messageId": "117", "data": "130", "fix": "131", "desc": "120"}, {"messageId": "117", "data": "132", "fix": "133", "desc": "123"}, {"messageId": "117", "data": "134", "fix": "135", "desc": "126"}, {"messageId": "117", "data": "136", "fix": "137", "desc": "129"}, "replaceWithAlt", {"alt": "138"}, {"range": "139", "text": "140"}, "Replace with `&apos;`.", {"alt": "141"}, {"range": "142", "text": "143"}, "Replace with `&lsquo;`.", {"alt": "144"}, {"range": "145", "text": "146"}, "Replace with `&#39;`.", {"alt": "147"}, {"range": "148", "text": "149"}, "Replace with `&rsquo;`.", {"alt": "138"}, {"range": "150", "text": "151"}, {"alt": "141"}, {"range": "152", "text": "153"}, {"alt": "144"}, {"range": "154", "text": "155"}, {"alt": "147"}, {"range": "156", "text": "157"}, "&apos;", [1785, 1905], "\n            Welcome to the VetAI Diagnostic System. Monitor your practice&apos;s key metrics and recent activity.\n          ", "&lsquo;", [1785, 1905], "\n            Welcome to the VetAI Diagnostic System. Monitor your practice&lsquo;s key metrics and recent activity.\n          ", "&#39;", [1785, 1905], "\n            Welcome to the VetAI Diagnostic System. Monitor your practice&#39;s key metrics and recent activity.\n          ", "&rsquo;", [1785, 1905], "\n            Welcome to the VetAI Diagnostic System. Monitor your practice&rsquo;s key metrics and recent activity.\n          ", [3418, 3435], "Today&apos;s Diagnoses", [3418, 3435], "Today&lsquo;s Diagnoses", [3418, 3435], "Today&#39;s Diagnoses", [3418, 3435], "Today&rsquo;s Diagnoses"]