'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import {
  Users,
  FileText,
  TrendingUp,
  AlertTriangle,
  Activity,
  Clock,
  CheckCircle
} from 'lucide-react'
import { generateMockData, generateMockCases, formatDate, getStatusColor } from '@/lib/utils'

export default function Dashboard() {
  const data = generateMockData()
  const recentCases = generateMockCases()

  const summaryCards = [
    {
      title: 'Total Patients',
      value: data.totalPatients.toLocaleString(),
      icon: Users,
      color: 'bg-blue-100 border-blue-600 text-blue-800',
      change: '+12% from last month'
    },
    {
      title: 'Active Cases',
      value: data.activeCases.toString(),
      icon: FileText,
      color: 'bg-yellow-100 border-yellow-600 text-yellow-800',
      change: '3 critical cases'
    },
    {
      title: 'Diagnoses This Week',
      value: data.diagnosesThisWeek.toString(),
      icon: TrendingUp,
      color: 'bg-green-100 border-green-600 text-green-800',
      change: '+8% from last week'
    },
    {
      title: 'Alerts',
      value: data.alerts.toString(),
      icon: AlertTriangle,
      color: 'bg-red-100 border-red-600 text-red-800',
      change: '2 require immediate attention'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="heading-brutal">Dashboard Overview</h1>
          <p className="text-medical mt-2">
            Welcome to the VetAI Diagnostic System. Monitor your practice&apos;s key metrics and recent activity.
          </p>
        </div>
        <div className="flex items-center gap-2 p-3 bg-green-100 border-2 border-green-600 rounded-xl">
          <Activity className="h-5 w-5 text-green-600" />
          <span className="font-bold text-green-800">AI System Active</span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {summaryCards.map((card, index) => {
          const Icon = card.icon
          return (
            <Card key={index} className="card-brutal">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-bold text-black">
                    {card.title}
                  </CardTitle>
                  <div className={`p-2 rounded-xl border-2 ${card.color}`}>
                    <Icon className="h-5 w-5" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-black text-black mb-2">
                  {card.value}
                </div>
                <p className="text-sm font-medium text-gray-600">
                  {card.change}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Disease Frequency Chart */}
      <Card className="card-brutal">
        <CardHeader>
          <CardTitle className="subheading-brutal">Disease Frequency Over Time</CardTitle>
          <p className="text-medical">Track the most common conditions diagnosed in your practice</p>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data.diseaseFrequency}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis
                  dataKey="date"
                  stroke="#374151"
                  fontSize={12}
                  fontWeight="600"
                />
                <YAxis
                  stroke="#374151"
                  fontSize={12}
                  fontWeight="600"
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '4px solid black',
                    borderRadius: '12px',
                    boxShadow: '4px 4px 0px 0px rgba(0,0,0,1)',
                    fontWeight: 'bold'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="count"
                  stroke="#3B82F6"
                  strokeWidth={4}
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 6 }}
                  activeDot={{ r: 8, stroke: '#1D4ED8', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Recent Cases Table */}
      <Card className="card-brutal">
        <CardHeader>
          <CardTitle className="subheading-brutal">Recent Cases</CardTitle>
          <p className="text-medical">Latest patient cases and their diagnostic status</p>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow className="border-b-2 border-black">
                <TableHead className="font-black text-black">Animal ID</TableHead>
                <TableHead className="font-black text-black">Species</TableHead>
                <TableHead className="font-black text-black">Age</TableHead>
                <TableHead className="font-black text-black">Symptoms</TableHead>
                <TableHead className="font-black text-black">Diagnosis</TableHead>
                <TableHead className="font-black text-black">Status</TableHead>
                <TableHead className="font-black text-black">Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentCases.map((case_) => (
                <TableRow key={case_.id} className="border-b border-gray-200">
                  <TableCell className="font-bold text-black">{case_.animalId}</TableCell>
                  <TableCell className="font-medium">{case_.species}</TableCell>
                  <TableCell className="font-medium">{case_.age} years</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {case_.symptoms.slice(0, 2).map((symptom, index) => (
                        <Badge key={index} variant="outline" className="text-xs border-2 border-gray-400">
                          {symptom}
                        </Badge>
                      ))}
                      {case_.symptoms.length > 2 && (
                        <Badge variant="outline" className="text-xs border-2 border-gray-400">
                          +{case_.symptoms.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {case_.diagnosis ? (
                      <div>
                        <div className="font-medium text-black">{case_.diagnosis}</div>
                        {case_.confidence && (
                          <div className="text-xs text-gray-600">
                            {case_.confidence}% confidence
                          </div>
                        )}
                      </div>
                    ) : (
                      <span className="text-gray-500 font-medium">Pending</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge className={`${getStatusColor(case_.status)} border-2 font-bold`}>
                      {case_.status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
                      {case_.status === 'diagnosed' && <CheckCircle className="h-3 w-3 mr-1" />}
                      {case_.status === 'treated' && <CheckCircle className="h-3 w-3 mr-1" />}
                      {case_.status === 'follow-up' && <Activity className="h-3 w-3 mr-1" />}
                      {case_.status.charAt(0).toUpperCase() + case_.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-medium text-gray-700">
                    {formatDate(case_.createdAt)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
