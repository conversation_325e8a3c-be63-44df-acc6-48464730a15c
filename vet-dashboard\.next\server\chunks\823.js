exports.id=823,exports.ids=[823],exports.modules={1135:()=>{},1453:(e,t,s)=>{Promise.resolve().then(s.bind(s,7426))},1520:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>n});let n=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\components\\sidebar.tsx","Sidebar")},3639:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},3837:(e,t,s)=>{Promise.resolve().then(s.bind(s,1520))},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>o});var n=s(7413),a=s(4292),i=s.n(a);s(1135);var r=s(1520);let o={title:"VetAI - Veterinary AI Diagnostic System",description:"Advanced AI-powered diagnostic system for veterinary practices with voice recognition and computer vision capabilities."};function c({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${i().variable} font-sans antialiased bg-white`,children:(0,n.jsxs)("div",{className:"flex min-h-screen",children:[(0,n.jsx)(r.Sidebar,{}),(0,n.jsx)("main",{className:"flex-1 p-8 bg-gray-50",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto",children:e})})]})})})}},4780:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>r,_8:()=>c,cn:()=>i,qY:()=>o,tK:()=>d});var n=s(9384),a=s(2348);function i(...e){return(0,a.QP)((0,n.$)(e))}function r(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(e)}function o(e){switch(e.toLowerCase()){case"critical":return"status-critical";case"warning":case"pending":return"status-warning";case"stable":case"treated":return"status-stable";default:return"status-info"}}function c(){return{totalPatients:1247,activeCases:23,diagnosesThisWeek:89,alerts:3,diseaseFrequency:[{name:"Skin Allergies",count:15,date:"2024-01-01"},{name:"Respiratory Issues",count:12,date:"2024-01-02"},{name:"Digestive Problems",count:8,date:"2024-01-03"},{name:"Parasites",count:6,date:"2024-01-04"},{name:"Dental Issues",count:4,date:"2024-01-05"}],commonDiagnoses:[{name:"Skin Allergies",count:45},{name:"Upper Respiratory Infection",count:32},{name:"Gastroenteritis",count:28},{name:"Ear Infection",count:24},{name:"Dental Disease",count:18}],outcomeRates:[{name:"Recovery",value:78,color:"#10B981"},{name:"Ongoing Treatment",value:18,color:"#F59E0B"},{name:"Complications",value:4,color:"#EF4444"}]}}function d(){return[{id:"1",patientId:"p1",animalId:"A001",species:"Dog",breed:"Golden Retriever",age:3,symptoms:["Lethargy","Loss of appetite","Vomiting"],temperature:102.5,diagnosis:"Gastroenteritis",confidence:85,status:"diagnosed",notes:"Patient showing signs of mild gastroenteritis. Prescribed medication and dietary changes.",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-08"),veterinarian:"Dr. Smith"},{id:"2",patientId:"p2",animalId:"A002",species:"Cat",breed:"Persian",age:5,symptoms:["Coughing","Sneezing","Discharge from eyes"],temperature:101.8,diagnosis:"Upper Respiratory Infection",confidence:92,status:"treated",notes:"Typical URI symptoms. Antibiotics prescribed. Follow-up in 1 week.",createdAt:new Date("2024-01-07"),updatedAt:new Date("2024-01-08"),veterinarian:"Dr. Johnson"},{id:"3",patientId:"p3",animalId:"A003",species:"Dog",breed:"Labrador",age:2,symptoms:["Limping","Pain","Swelling"],status:"pending",notes:"Possible injury to front left paw. X-rays ordered.",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-08"),veterinarian:"Dr. Williams"}]}},6687:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},7426:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>g});var n=s(687),a=s(5814),i=s.n(a),r=s(6189),o=s(4780),c=s(9625),d=s(22),l=s(1312),m=s(8200),p=s(3411),u=s(4027),h=s(137),b=s(8559);let x=[{name:"Dashboard Overview",href:"/",icon:c.A,description:"Main dashboard with key metrics"},{name:"New Case Entry",href:"/cases/new",icon:d.A,description:"Create new patient case"},{name:"Patient Records",href:"/patients",icon:l.A,description:"View and manage patient records"},{name:"AI Diagnostics",href:"/diagnostics",icon:m.A,description:"AI-powered diagnostic assistance"},{name:"Analytics & Reports",href:"/analytics",icon:p.A,description:"View analytics and generate reports"},{name:"Settings",href:"/settings",icon:u.A,description:"System configuration and preferences"}];function g(){let e=(0,r.usePathname)();return(0,n.jsxs)("div",{className:"sidebar-brutal w-80 min-h-screen p-6",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,n.jsx)("div",{className:"p-3 bg-blue-100 border-4 border-black rounded-2xl shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]",children:(0,n.jsx)(h.A,{className:"h-8 w-8 text-blue-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-black text-black",children:"VetAI"}),(0,n.jsx)("p",{className:"text-sm font-bold text-gray-600",children:"Diagnostic System"})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-4 p-3 bg-green-100 border-2 border-green-600 rounded-xl",children:[(0,n.jsx)(b.A,{className:"h-4 w-4 text-green-600"}),(0,n.jsx)("span",{className:"text-sm font-bold text-green-800",children:"System Online"})]})]}),(0,n.jsxs)("nav",{className:"space-y-3",children:[(0,n.jsx)("h2",{className:"text-sm font-black text-gray-500 uppercase tracking-wider mb-4",children:"Navigation"}),x.map(t=>{let s=e===t.href,a=t.icon;return(0,n.jsxs)(i(),{href:t.href,className:(0,o.cn)("nav-item-brutal",s&&"active"),children:[(0,n.jsx)(a,{className:"h-6 w-6"}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("div",{className:"font-bold text-black",children:t.name}),(0,n.jsx)("div",{className:"text-xs text-gray-600 font-medium",children:t.description})]})]},t.name)})]}),(0,n.jsxs)("div",{className:"mt-8 p-4 card-brutal bg-blue-50",children:[(0,n.jsx)("h3",{className:"font-black text-black mb-3",children:"Quick Stats"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active Cases"}),(0,n.jsx)("span",{className:"font-bold text-blue-600",children:"23"})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Today's Diagnoses"}),(0,n.jsx)("span",{className:"font-bold text-green-600",children:"12"})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Alerts"}),(0,n.jsx)("span",{className:"font-bold text-red-600",children:"3"})]})]})]}),(0,n.jsxs)("div",{className:"mt-6 p-4 card-brutal bg-purple-50",children:[(0,n.jsxs)("h3",{className:"font-black text-black mb-3 flex items-center gap-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 bg-purple-500 rounded-full animate-pulse"}),"Voice AI"]}),(0,n.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Ready to listen for voice commands and transcribe consultations."}),(0,n.jsx)("button",{className:"mt-3 btn-brutal bg-purple-100 text-purple-800 text-sm py-2 px-4",children:"Start Listening"})]})]})}}};