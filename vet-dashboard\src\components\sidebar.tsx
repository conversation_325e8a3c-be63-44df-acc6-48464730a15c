'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import {
  LayoutDashboard,
  FileText,
  Users,
  Brain,
  BarChart3,
  Settings,
  Stethoscope,
  Activity
} from 'lucide-react'

const navigation = [
  {
    name: 'Dashboard Overview',
    href: '/',
    icon: LayoutDashboard,
    description: 'Main dashboard with key metrics'
  },
  {
    name: 'New Case Entry',
    href: '/cases/new',
    icon: FileText,
    description: 'Create new patient case'
  },
  {
    name: 'Patient Records',
    href: '/patients',
    icon: Users,
    description: 'View and manage patient records'
  },
  {
    name: 'AI Diagnostics',
    href: '/diagnostics',
    icon: Brain,
    description: 'AI-powered diagnostic assistance'
  },
  {
    name: 'Analytics & Reports',
    href: '/analytics',
    icon: BarChart3,
    description: 'View analytics and generate reports'
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
    description: 'System configuration and preferences'
  }
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="sidebar-brutal w-80 min-h-screen p-6">
      {/* Logo and Title */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-3 bg-blue-100 border-4 border-black rounded-2xl shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
            <Stethoscope className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-black text-black">VetAI</h1>
            <p className="text-sm font-bold text-gray-600">Diagnostic System</p>
          </div>
        </div>
        <div className="flex items-center gap-2 mt-4 p-3 bg-green-100 border-2 border-green-600 rounded-xl">
          <Activity className="h-4 w-4 text-green-600" />
          <span className="text-sm font-bold text-green-800">System Online</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="space-y-3">
        <h2 className="text-sm font-black text-gray-500 uppercase tracking-wider mb-4">
          Navigation
        </h2>
        {navigation.map((item) => {
          const isActive = pathname === item.href
          const Icon = item.icon

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'nav-item-brutal',
                isActive && 'active'
              )}
            >
              <Icon className="h-6 w-6" />
              <div className="flex-1">
                <div className="font-bold text-black">{item.name}</div>
                <div className="text-xs text-gray-600 font-medium">
                  {item.description}
                </div>
              </div>
            </Link>
          )
        })}
      </nav>

      {/* Quick Stats */}
      <div className="mt-8 p-4 card-brutal bg-blue-50">
        <h3 className="font-black text-black mb-3">Quick Stats</h3>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Active Cases</span>
            <span className="font-bold text-blue-600">23</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Today&apos;s Diagnoses</span>
            <span className="font-bold text-green-600">12</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Alerts</span>
            <span className="font-bold text-red-600">3</span>
          </div>
        </div>
      </div>

      {/* Voice Control Status */}
      <div className="mt-6 p-4 card-brutal bg-purple-50">
        <h3 className="font-black text-black mb-3 flex items-center gap-2">
          <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
          Voice AI
        </h3>
        <p className="text-sm font-medium text-gray-700">
          Ready to listen for voice commands and transcribe consultations.
        </p>
        <button className="mt-3 btn-brutal bg-purple-100 text-purple-800 text-sm py-2 px-4">
          Start Listening
        </button>
      </div>
    </div>
  )
}
