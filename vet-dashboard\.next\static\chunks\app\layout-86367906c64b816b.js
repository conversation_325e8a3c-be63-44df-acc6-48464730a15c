(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},2664:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return s}});let r=n(9991),a=n(7102);function s(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,a.hasBasePath)(n.pathname)}catch(e){return!1}}},2757:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return s},formatWithValidation:function(){return o},urlObjectKeys:function(){return i}});let r=n(6966)._(n(8859)),a=/https?|ftp|gopher|file/;function s(e){let{auth:t,hostname:n}=e,s=e.protocol||"",i=e.pathname||"",o=e.hash||"",c=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:n&&(l=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(l+=":"+e.port)),c&&"object"==typeof c&&(c=String(r.urlQueryToSearchParams(c)));let u=e.search||c&&"?"+c||"";return s&&!s.endsWith(":")&&(s+=":"),e.slashes||(!s||a.test(s))&&!1!==l?(l="//"+(l||""),i&&"/"!==i[0]&&(i="/"+i)):l||(l=""),o&&"#"!==o[0]&&(o="#"+o),u&&"?"!==u[0]&&(u="?"+u),""+s+l+(i=i.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+o}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return s(e)}},3180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},4881:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},6654:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return a}});let r=n(2115);function a(e,t){let n=(0,r.useRef)(null),a=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=a.current;t&&(a.current=null,t())}else e&&(n.current=s(e,r)),t&&(a.current=s(t,r))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return y},useLinkStatus:function(){return b}});let r=n(6966),a=n(5155),s=r._(n(2115)),i=n(2757),o=n(5227),c=n(9818),l=n(6654),u=n(9991),d=n(5929);n(3230);let f=n(4930),p=n(2664),m=n(6634);function h(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function y(e){let t,n,r,[i,y]=(0,s.useOptimistic)(f.IDLE_LINK_STATUS),b=(0,s.useRef)(null),{href:x,as:v,children:j,prefetch:N=null,passHref:k,replace:w,shallow:P,scroll:A,onClick:_,onMouseEnter:M,onTouchStart:E,legacyBehavior:O=!1,onNavigate:S,ref:I,unstable_dynamicOnHover:C,...D}=e;t=j,O&&("string"==typeof t||"number"==typeof t)&&(t=(0,a.jsx)("a",{children:t}));let T=s.default.useContext(o.AppRouterContext),R=!1!==N,L=null===N?c.PrefetchKind.AUTO:c.PrefetchKind.FULL,{href:U,as:F}=s.default.useMemo(()=>{let e=h(x);return{href:e,as:v?h(v):e}},[x,v]);O&&(n=s.default.Children.only(t));let q=O?n&&"object"==typeof n&&n.ref:I,V=s.default.useCallback(e=>(null!==T&&(b.current=(0,f.mountLinkInstance)(e,U,T,L,R,y)),()=>{b.current&&((0,f.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,f.unmountPrefetchableInstance)(e)}),[R,U,T,L,y]),K={ref:(0,l.useMergedRef)(V,q),onClick(e){O||"function"!=typeof _||_(e),O&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),T&&(e.defaultPrevented||function(e,t,n,r,a,i,o){let{nodeName:c}=e.currentTarget;if(!("A"===c.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){a&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),s.default.startTransition(()=>{if(o){let e=!1;if(o({preventDefault:()=>{e=!0}}),e)return}(0,m.dispatchNavigateAction)(n||t,a?"replace":"push",null==i||i,r.current)})}}(e,U,F,b,w,A,S))},onMouseEnter(e){O||"function"!=typeof M||M(e),O&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),T&&R&&(0,f.onNavigationIntent)(e.currentTarget,!0===C)},onTouchStart:function(e){O||"function"!=typeof E||E(e),O&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),T&&R&&(0,f.onNavigationIntent)(e.currentTarget,!0===C)}};return(0,u.isAbsoluteUrl)(F)?K.href=F:O&&!k&&("a"!==n.type||"href"in n.props)||(K.href=(0,d.addBasePath)(F)),r=O?s.default.cloneElement(n,K):(0,a.jsx)("a",{...D,...K,children:t}),(0,a.jsx)(g.Provider,{value:i,children:r})}n(3180);let g=(0,s.createContext)(f.IDLE_LINK_STATUS),b=()=>(0,s.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7202:(e,t,n)=>{"use strict";n.d(t,{Sidebar:()=>b});var r=n(5155),a=n(6874),s=n.n(a),i=n(8999),o=n(9434),c=n(9946);let l=(0,c.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var u=n(7434),d=n(7580);let f=(0,c.A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),p=(0,c.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),m=(0,c.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),h=(0,c.A)("stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]);var y=n(9397);let g=[{name:"Dashboard Overview",href:"/",icon:l,description:"Main dashboard with key metrics"},{name:"New Case Entry",href:"/cases/new",icon:u.A,description:"Create new patient case"},{name:"Patient Records",href:"/patients",icon:d.A,description:"View and manage patient records"},{name:"AI Diagnostics",href:"/diagnostics",icon:f,description:"AI-powered diagnostic assistance"},{name:"Analytics & Reports",href:"/analytics",icon:p,description:"View analytics and generate reports"},{name:"Settings",href:"/settings",icon:m,description:"System configuration and preferences"}];function b(){let e=(0,i.usePathname)();return(0,r.jsxs)("div",{className:"sidebar-brutal w-80 min-h-screen p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-3 bg-blue-100 border-4 border-black rounded-2xl shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]",children:(0,r.jsx)(h,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-black text-black",children:"VetAI"}),(0,r.jsx)("p",{className:"text-sm font-bold text-gray-600",children:"Diagnostic System"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-4 p-3 bg-green-100 border-2 border-green-600 rounded-xl",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-bold text-green-800",children:"System Online"})]})]}),(0,r.jsxs)("nav",{className:"space-y-3",children:[(0,r.jsx)("h2",{className:"text-sm font-black text-gray-500 uppercase tracking-wider mb-4",children:"Navigation"}),g.map(t=>{let n=e===t.href,a=t.icon;return(0,r.jsxs)(s(),{href:t.href,className:(0,o.cn)("nav-item-brutal",n&&"active"),children:[(0,r.jsx)(a,{className:"h-6 w-6"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-bold text-black",children:t.name}),(0,r.jsx)("div",{className:"text-xs text-gray-600 font-medium",children:t.description})]})]},t.name)})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 card-brutal bg-blue-50",children:[(0,r.jsx)("h3",{className:"font-black text-black mb-3",children:"Quick Stats"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active Cases"}),(0,r.jsx)("span",{className:"font-bold text-blue-600",children:"23"})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Today's Diagnoses"}),(0,r.jsx)("span",{className:"font-bold text-green-600",children:"12"})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Alerts"}),(0,r.jsx)("span",{className:"font-bold text-red-600",children:"3"})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 card-brutal bg-purple-50",children:[(0,r.jsxs)("h3",{className:"font-black text-black mb-3 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-purple-500 rounded-full animate-pulse"}),"Voice AI"]}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Ready to listen for voice commands and transcribe consultations."}),(0,r.jsx)("button",{className:"mt-3 btn-brutal bg-purple-100 text-purple-800 text-sm py-2 px-4",children:"Start Listening"})]})]})}},8859:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[n,a]of Object.entries(e))if(Array.isArray(a))for(let e of a)t.append(n,r(e));else t.set(n,r(a));return t}function s(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return a}})},9434:(e,t,n)=>{"use strict";n.d(t,{Yq:()=>i,_8:()=>c,cn:()=>s,qY:()=>o,tK:()=>l});var r=n(2596),a=n(9688);function s(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.QP)((0,r.$)(t))}function i(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(e)}function o(e){switch(e.toLowerCase()){case"critical":return"status-critical";case"warning":case"pending":return"status-warning";case"stable":case"treated":return"status-stable";default:return"status-info"}}function c(){return{totalPatients:1247,activeCases:23,diagnosesThisWeek:89,alerts:3,diseaseFrequency:[{name:"Skin Allergies",count:15,date:"2024-01-01"},{name:"Respiratory Issues",count:12,date:"2024-01-02"},{name:"Digestive Problems",count:8,date:"2024-01-03"},{name:"Parasites",count:6,date:"2024-01-04"},{name:"Dental Issues",count:4,date:"2024-01-05"}],commonDiagnoses:[{name:"Skin Allergies",count:45},{name:"Upper Respiratory Infection",count:32},{name:"Gastroenteritis",count:28},{name:"Ear Infection",count:24},{name:"Dental Disease",count:18}],outcomeRates:[{name:"Recovery",value:78,color:"#10B981"},{name:"Ongoing Treatment",value:18,color:"#F59E0B"},{name:"Complications",value:4,color:"#EF4444"}]}}function l(){return[{id:"1",patientId:"p1",animalId:"A001",species:"Dog",breed:"Golden Retriever",age:3,symptoms:["Lethargy","Loss of appetite","Vomiting"],temperature:102.5,diagnosis:"Gastroenteritis",confidence:85,status:"diagnosed",notes:"Patient showing signs of mild gastroenteritis. Prescribed medication and dietary changes.",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-08"),veterinarian:"Dr. Smith"},{id:"2",patientId:"p2",animalId:"A002",species:"Cat",breed:"Persian",age:5,symptoms:["Coughing","Sneezing","Discharge from eyes"],temperature:101.8,diagnosis:"Upper Respiratory Infection",confidence:92,status:"treated",notes:"Typical URI symptoms. Antibiotics prescribed. Follow-up in 1 week.",createdAt:new Date("2024-01-07"),updatedAt:new Date("2024-01-08"),veterinarian:"Dr. Johnson"},{id:"3",patientId:"p3",animalId:"A003",species:"Dog",breed:"Labrador",age:2,symptoms:["Limping","Pain","Swelling"],status:"pending",notes:"Possible injury to front left paw. X-rays ordered.",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-08"),veterinarian:"Dr. Williams"}]}},9467:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,4881,23)),Promise.resolve().then(n.t.bind(n,347,23)),Promise.resolve().then(n.bind(n,7202))},9991:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return g},NormalizeError:function(){return h},PageNotFoundError:function(){return y},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return c},getLocationOrigin:function(){return i},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return l},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return x}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,a=Array(r),s=0;s<r;s++)a[s]=arguments[s];return n||(n=!0,t=e(...a)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>a.test(e);function i(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function o(){let{href:e}=window.location,t=i();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&l(n))return r;if(!r)throw Object.defineProperty(Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}}},e=>{var t=t=>e(e.s=t);e.O(0,[879,709,441,684,358],()=>t(9467)),_N_E=e.O()}]);