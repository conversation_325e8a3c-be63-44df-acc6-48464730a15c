(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[493],{52:(t,e,r)=>{"use strict";function n(t){return`Minified Redux error #${t}; visit https://redux.js.org/Errors?code=${t} for the full message or use the non-minified dev environment for full errors. `}r.d(e,{HY:()=>c,Qd:()=>l,Tw:()=>f,Zz:()=>s,ve:()=>h,y$:()=>u});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",a=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function l(t){if("object"!=typeof t||null===t)return!1;let e=t;for(;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e||null===Object.getPrototypeOf(t)}function u(t,e,r){if("function"!=typeof t)throw Error(n(2));if("function"==typeof e&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof e&&void 0===r&&(r=e,e=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(u)(t,e)}let a=t,c=e,s=new Map,f=s,h=0,d=!1;function p(){f===s&&(f=new Map,s.forEach((t,e)=>{f.set(e,t)}))}function y(){if(d)throw Error(n(3));return c}function v(t){if("function"!=typeof t)throw Error(n(4));if(d)throw Error(n(5));let e=!0;p();let r=h++;return f.set(r,t),function(){if(e){if(d)throw Error(n(6));e=!1,p(),f.delete(r),s=null}}}function g(t){if(!l(t))throw Error(n(7));if(void 0===t.type)throw Error(n(8));if("string"!=typeof t.type)throw Error(n(17));if(d)throw Error(n(9));try{d=!0,c=a(c,t)}finally{d=!1}return(s=f).forEach(t=>{t()}),t}return g({type:o.INIT}),{dispatch:g,subscribe:v,getState:y,replaceReducer:function(t){if("function"!=typeof t)throw Error(n(10));a=t,g({type:o.REPLACE})},[i]:function(){return{subscribe(t){if("object"!=typeof t||null===t)throw Error(n(11));function e(){t.next&&t.next(y())}return e(),{unsubscribe:v(e)}},[i](){return this}}}}}function c(t){let e,r=Object.keys(t),i={};for(let e=0;e<r.length;e++){let n=r[e];"function"==typeof t[n]&&(i[n]=t[n])}let a=Object.keys(i);try{Object.keys(i).forEach(t=>{let e=i[t];if(void 0===e(void 0,{type:o.INIT}))throw Error(n(12));if(void 0===e(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(t){e=t}return function(t={},r){if(e)throw e;let o=!1,l={};for(let e=0;e<a.length;e++){let u=a[e],c=i[u],s=t[u],f=c(s,r);if(void 0===f)throw r&&r.type,Error(n(14));l[u]=f,o=o||f!==s}return(o=o||a.length!==Object.keys(t).length)?l:t}}function s(...t){return 0===t.length?t=>t:1===t.length?t[0]:t.reduce((t,e)=>(...r)=>t(e(...r)))}function f(...t){return e=>(r,i)=>{let a=e(r,i),o=()=>{throw Error(n(15))},l={getState:a.getState,dispatch:(t,...e)=>o(t,...e)};return o=s(...t.map(t=>t(l)))(a.dispatch),{...a,dispatch:o}}}function h(t){return l(t)&&"type"in t&&"string"==typeof t.type}},177:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(5160);e.isArguments=function(t){return null!==t&&"object"==typeof t&&"[object Arguments]"===n.getTag(t)}},215:(t,e,r)=>{"use strict";r.d(e,{BZ:()=>ti,eE:()=>tu,Xb:()=>ta,A2:()=>tn,yn:()=>tc,Dn:()=>j,gL:()=>q,fl:()=>Z,R4:()=>J,Re:()=>w,n4:()=>_});var n=r(8924),i=r(2183),a=r(7238),o=r(9827),l=r(356),u=r(8478),c=r(6377),s=r(8190),f=r(6523),h=r(530),d=r(1928),p=r(841),y=r(4968),v=r(2589),g=r(9449),m=r(5146),b=r(6670),x=r(5714),w=t=>{var e=(0,a.fz)(t);return"horizontal"===e?"xAxis":"vertical"===e?"yAxis":"centric"===e?"angleAxis":"radiusAxis"},O=t=>t.tooltip.settings.axisId,j=t=>{var e=w(t),r=O(t);return(0,i.Hd)(t,e,r)},P=(0,n.Mz)([j,a.fz,i.um,u.iO,w],i.sr),E=(0,n.Mz)([t=>t.graphicalItems.cartesianItems,t=>t.graphicalItems.polarItems],(t,e)=>[...t,...e]),A=(0,n.Mz)([w,O],i.eo),M=(0,n.Mz)([E,j,A],i.ec),S=(0,n.Mz)([M],i.rj),_=(0,n.Mz)([S,l.LF],i.Nk),k=(0,n.Mz)([_,j,M],i.fb),C=(0,n.Mz)([j],i.S5),T=(0,n.Mz)([_,M,u.eC],i.MK),D=(0,n.Mz)([T,l.LF,w],i.pM),N=(0,n.Mz)([M],i.IO),I=(0,n.Mz)([_,j,N,w],i.kz),z=(0,n.Mz)([i.Kr,w,O],i.P9),L=(0,n.Mz)([z,w],i.Oz),B=(0,n.Mz)([i.gT,w,O],i.P9),U=(0,n.Mz)([B,w],i.q),R=(0,n.Mz)([i.$X,w,O],i.P9),$=(0,n.Mz)([R,w],i.bb),F=(0,n.Mz)([L,$,U],i.yi),K=(0,n.Mz)([j,C,D,I,F],i.wL),G=(0,n.Mz)([j,a.fz,_,k,u.eC,w,K],i.tP),H=(0,n.Mz)([G,j,P],i.xp),W=(0,n.Mz)([j,G,H,w],i.g1),V=t=>{var e=w(t),r=O(t);return(0,i.D5)(t,e,r,!1)},q=(0,n.Mz)([j,V],s.I),Z=(0,n.Mz)([j,P,W,q],i.Qn),Y=(0,n.Mz)([a.fz,k,j,w],i.tF),X=(0,n.Mz)([a.fz,k,j,w],i.iv),J=(0,n.Mz)([a.fz,j,P,Z,V,Y,X,w],(t,e,r,n,i,a,l,u)=>{if(e){var{type:s}=e,f=(0,o._L)(t,u);if(n){var h="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,d="category"===s&&n.bandwidth?n.bandwidth()/h:0;return(d="angleAxis"===u&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,c.sA)(i[0]-i[1])*d:d,f&&l)?l.map((t,e)=>({coordinate:n(t)+d,value:t,index:e,offset:d})):n.domain().map((t,e)=>({coordinate:n(t)+d,value:a?a[t]:t,index:e,offset:d}))}}}),Q=(0,n.Mz)([f.xH,f.Hw,t=>t.tooltip.settings],(t,e,r)=>(0,f.$g)(r.shared,t,e)),tt=t=>t.tooltip.settings.trigger,te=t=>t.tooltip.settings.defaultIndex,tr=(0,n.Mz)([x.J,Q,tt,te],d.i),tn=(0,n.Mz)([tr,_],p.P),ti=(0,n.Mz)([J,tn],h.E),ta=(0,n.Mz)([tr],t=>{if(t)return t.dataKey}),to=(0,n.Mz)([x.J,Q,tt,te],m.q),tl=(0,n.Mz)([v.Lp,v.A$,a.fz,g.GO,J,te,to,b.x],y.o),tu=(0,n.Mz)([tr,tl],(t,e)=>null!=t&&t.coordinate?t.coordinate:e),tc=(0,n.Mz)([tr],t=>t.active)},220:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.last=function(t){return t[t.length-1]}},241:(t,e,r)=>{t.exports=r(2434).sortBy},294:(t,e)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,a=r?Symbol.for("react.fragment"):60107,o=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,u=r?Symbol.for("react.provider"):60109,c=r?Symbol.for("react.context"):60110,s=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,h=r?Symbol.for("react.forward_ref"):60112,d=r?Symbol.for("react.suspense"):60113,p=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),y=r?Symbol.for("react.lazy"):60116;function v(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case s:case f:case a:case l:case o:case d:return t;default:switch(t=t&&t.$$typeof){case c:case h:case y:case p:case u:return t;default:return e}}case i:return e}}}r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope");e.isFragment=function(t){return v(t)===a}},330:(t,e,r)=>{"use strict";t.exports=r(294)},356:(t,e,r)=>{"use strict";r.d(e,{HS:()=>o,LF:()=>i});var n=r(8924),i=t=>t.chartData,a=(0,n.Mz)([i],t=>{var e=null!=t.chartData?t.chartData.length-1:0;return{chartData:t.chartData,computedData:t.computedData,dataEndIndex:e,dataStartIndex:0}}),o=(t,e,r,n)=>n?a(t):i(t)},379:(t,e,r)=>{"use strict";r.d(e,{J:()=>j,Z:()=>g});var n=r(2115),i=r(2596),a=r(9095),o=r(788),l=r(6377),u=r(5641),c=r(7238),s=["offset"],f=["labelRef"];function h(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(){return(y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var v=t=>{var{value:e,formatter:r}=t,n=(0,l.uy)(t.children)?e:t.children;return"function"==typeof r?r(n):n},g=t=>null!=t&&"function"==typeof t,m=(t,e)=>(0,l.sA)(e-t)*Math.min(Math.abs(e-t),360),b=(t,e,r)=>{var a,o,{position:c,viewBox:s,offset:f,className:h}=t,{cx:d,cy:p,innerRadius:v,outerRadius:g,startAngle:b,endAngle:x,clockWise:w}=s,O=(v+g)/2,j=m(b,x),P=j>=0?1:-1;"insideStart"===c?(a=b+P*f,o=w):"insideEnd"===c?(a=x-P*f,o=!w):"end"===c&&(a=x+P*f,o=w),o=j<=0?o:!o;var E=(0,u.IZ)(d,p,O,a),A=(0,u.IZ)(d,p,O,a+(o?1:-1)*359),M="M".concat(E.x,",").concat(E.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(+!o,",\n    ").concat(A.x,",").concat(A.y),S=(0,l.uy)(t.id)?(0,l.NF)("recharts-radial-line-"):t.id;return n.createElement("text",y({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",h)}),n.createElement("defs",null,n.createElement("path",{id:S,d:M})),n.createElement("textPath",{xlinkHref:"#".concat(S)},e))},x=t=>{var{viewBox:e,offset:r,position:n}=t,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:c,endAngle:s}=e,f=(c+s)/2;if("outside"===n){var{x:h,y:d}=(0,u.IZ)(i,a,l+r,f);return{x:h,y:d,textAnchor:h>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:p,y}=(0,u.IZ)(i,a,(o+l)/2,f);return{x:p,y,textAnchor:"middle",verticalAnchor:"middle"}},w=(t,e)=>{var{parentViewBox:r,offset:n,position:i}=t,{x:a,y:o,width:u,height:c}=e,s=c>=0?1:-1,f=s*n,h=s>0?"end":"start",d=s>0?"start":"end",y=u>=0?1:-1,v=y*n,g=y>0?"end":"start",m=y>0?"start":"end";if("top"===i)return p(p({},{x:a+u/2,y:o-s*n,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(o-r.y,0),width:u}:{});if("bottom"===i)return p(p({},{x:a+u/2,y:o+c+f,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(o+c),0),width:u}:{});if("left"===i){var b={x:a-v,y:o+c/2,textAnchor:g,verticalAnchor:"middle"};return p(p({},b),r?{width:Math.max(b.x-r.x,0),height:c}:{})}if("right"===i){var x={x:a+u+v,y:o+c/2,textAnchor:m,verticalAnchor:"middle"};return p(p({},x),r?{width:Math.max(r.x+r.width-x.x,0),height:c}:{})}var w=r?{width:u,height:c}:{};return"insideLeft"===i?p({x:a+v,y:o+c/2,textAnchor:m,verticalAnchor:"middle"},w):"insideRight"===i?p({x:a+u-v,y:o+c/2,textAnchor:g,verticalAnchor:"middle"},w):"insideTop"===i?p({x:a+u/2,y:o+f,textAnchor:"middle",verticalAnchor:d},w):"insideBottom"===i?p({x:a+u/2,y:o+c-f,textAnchor:"middle",verticalAnchor:h},w):"insideTopLeft"===i?p({x:a+v,y:o+f,textAnchor:m,verticalAnchor:d},w):"insideTopRight"===i?p({x:a+u-v,y:o+f,textAnchor:g,verticalAnchor:d},w):"insideBottomLeft"===i?p({x:a+v,y:o+c-f,textAnchor:m,verticalAnchor:h},w):"insideBottomRight"===i?p({x:a+u-v,y:o+c-f,textAnchor:g,verticalAnchor:h},w):i&&"object"==typeof i&&((0,l.Et)(i.x)||(0,l._3)(i.x))&&((0,l.Et)(i.y)||(0,l._3)(i.y))?p({x:a+(0,l.F4)(i.x,u),y:o+(0,l.F4)(i.y,c),textAnchor:"end",verticalAnchor:"end"},w):p({x:a+u/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},w)},O=t=>"cx"in t&&(0,l.Et)(t.cx);function j(t){var e,{offset:r=5}=t,u=p({offset:r},h(t,s)),{viewBox:d,position:g,value:m,children:j,content:P,className:E="",textBreakAll:A,labelRef:M}=u,S=(0,c.sk)(),_=d||S;if(!_||(0,l.uy)(m)&&(0,l.uy)(j)&&!(0,n.isValidElement)(P)&&"function"!=typeof P)return null;if((0,n.isValidElement)(P)){var{labelRef:k}=u,C=h(u,f);return(0,n.cloneElement)(P,C)}if("function"==typeof P){if(e=(0,n.createElement)(P,u),(0,n.isValidElement)(e))return e}else e=v(u);var T=O(_),D=(0,o.J9)(u,!0);if(T&&("insideStart"===g||"insideEnd"===g||"end"===g))return b(u,e,D);var N=T?x(u):w(u,_);return n.createElement(a.E,y({ref:M,className:(0,i.$)("recharts-label",E)},D,N,{breakAll:A}),e)}j.displayName="Label";var P=t=>{var{cx:e,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:u,innerRadius:c,outerRadius:s,x:f,y:h,top:d,left:p,width:y,height:v,clockWise:g,labelViewBox:m}=t;if(m)return m;if((0,l.Et)(y)&&(0,l.Et)(v)){if((0,l.Et)(f)&&(0,l.Et)(h))return{x:f,y:h,width:y,height:v};if((0,l.Et)(d)&&(0,l.Et)(p))return{x:d,y:p,width:y,height:v}}return(0,l.Et)(f)&&(0,l.Et)(h)?{x:f,y:h,width:0,height:0}:(0,l.Et)(e)&&(0,l.Et)(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:s||u||o||0,clockWise:g}:t.viewBox?t.viewBox:void 0},E=(t,e,r)=>{if(!t)return null;var i={viewBox:e,labelRef:r};return!0===t?n.createElement(j,y({key:"label-implicit"},i)):(0,l.vh)(t)?n.createElement(j,y({key:"label-implicit",value:t},i)):(0,n.isValidElement)(t)?t.type===j?(0,n.cloneElement)(t,p({key:"label-implicit"},i)):n.createElement(j,y({key:"label-implicit",content:t},i)):g(t)?n.createElement(j,y({key:"label-implicit",content:t},i)):t&&"object"==typeof t?n.createElement(j,y({},t,{key:"label-implicit"},i)):null};j.parseViewBox=P,j.renderCallByParent=function(t,e){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var{children:i,labelRef:a}=t,l=P(t),u=(0,o.aS)(i,j).map((t,r)=>(0,n.cloneElement)(t,{viewBox:e||l,key:"label-".concat(r)}));return r?[E(t.label,e||l,a),...u]:u}},400:(t,e,r)=>{t.exports=r(2962).throttle},512:(t,e,r)=>{t.exports=r(7547).uniqBy},530:(t,e,r)=>{"use strict";r.d(e,{E:()=>i});var n=r(6377),i=(t,e)=>{var r,i=Number(e);if(!(0,n.M8)(i)&&null!=e)return i>=0?null==t||null==(r=t[i])?void 0:r.value:void 0}},564:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.debounce=function(t,e,{signal:r,edges:n}={}){let i,a=null,o=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),u=()=>{null!==a&&(t.apply(i,a),i=void 0,a=null)},c=()=>{l&&u(),d()},s=null,f=()=>{null!=s&&clearTimeout(s),s=setTimeout(()=>{s=null,c()},e)},h=()=>{null!==s&&(clearTimeout(s),s=null)},d=()=>{h(),i=void 0,a=null},p=function(...t){if(r?.aborted)return;i=this,a=t;let e=null==s;f(),o&&e&&u()};return p.schedule=f,p.cancel=d,p.flush=()=>{h(),u()},r?.addEventListener("abort",d,{once:!0}),p}},646:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},656:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(8179),i=r(9279);e.isArrayLikeObject=function(t){return i.isObjectLike(t)&&n.isArrayLike(t)}},668:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isSymbol=function(t){return"symbol"==typeof t||t instanceof Symbol}},675:(t,e,r)=>{"use strict";r.d(e,{R:()=>n});var n=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},788:(t,e,r)=>{"use strict";r.d(e,{J9:()=>v,aS:()=>d,y$:()=>p});var n=r(5672),i=r.n(n),a=r(2115),o=r(330),l=r(6377),u=r(3597),c=t=>"string"==typeof t?t:t?t.displayName||t.name||"Component":"",s=null,f=null,h=t=>{if(t===s&&Array.isArray(f))return f;var e=[];return a.Children.forEach(t,t=>{(0,l.uy)(t)||((0,o.isFragment)(t)?e=e.concat(h(t.props.children)):e.push(t))}),f=e,s=t,e};function d(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(t=>c(t)):[c(e)],h(t).forEach(t=>{var e=i()(t,"type.displayName")||i()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}var p=t=>!t||"object"!=typeof t||!("clipDot"in t)||!!t.clipDot,y=(t,e,r,n)=>{var i,a=null!=(i=n&&(null===u.VU||void 0===u.VU?void 0:u.VU[n]))?i:[];return e.startsWith("data-")||"function"!=typeof t&&(n&&a.includes(e)||u.QQ.includes(e))||r&&u.j2.includes(e)},v=(t,e,r)=>{if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,a.isValidElement)(t)&&(n=t.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(t=>{var a;y(null==(a=n)?void 0:a[t],t,e,r)&&(i[t]=n[t])}),i}},841:(t,e,r)=>{"use strict";r.d(e,{P:()=>i});var n=r(8892),i=(t,e)=>{var r=null==t?void 0:t.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var a=Infinity;return e.length>0&&(a=e.length-1),String(Math.max(0,Math.min(i,a)))}},885:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isTypedArray=function(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}},921:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(7040),i=r(4545),a=r(6200),o=r(4072);e.get=function t(e,r,l){if(null==e)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=e[r];if(void 0===a)if(i.isDeepKey(r))return t(e,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let t=e[r];if(void 0===t)return l;return t}default:{if(Array.isArray(r)){var u=e,c=r,s=l;if(0===c.length)return s;let t=u;for(let e=0;e<c.length;e++){if(null==t||n.isUnsafeProperty(c[e]))return s;t=t[c[e]]}return void 0===t?s:t}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let t=e[r];if(void 0===t)return l;return t}}}},972:(t,e,r)=>{"use strict";r.d(e,{C:()=>l,U:()=>u});var n=r(8924),i=r(9449),a=r(2589),o=r(6377),l=t=>t.brush,u=(0,n.Mz)([l,i.GO,a.HK],(t,e,r)=>({height:t.height,x:(0,o.Et)(t.x)?t.x:e.left,y:(0,o.Et)(t.y)?t.y:e.top+e.height+e.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(t.width)?t.width:e.width}))},1147:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPlainObject=function(t){if(!t||"object"!=typeof t)return!1;let e=Object.getPrototypeOf(t);return(null===e||e===Object.prototype||null===Object.getPrototypeOf(e))&&"[object Object]"===Object.prototype.toString.call(t)}},1243:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1420:(t,e,r)=>{"use strict";r.d(e,{E:()=>u,O:()=>c});var n=r(1971),i=r(9827),a=r(215);function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var u=()=>(0,n.G)(a.Dn),c=()=>{var t=u(),e=(0,n.G)(a.R4),r=(0,n.G)(a.fl);return(0,i.Hj)(l(l({},t),{},{scale:r}),e)}},1551:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(668),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.isKey=function(t,e){return!Array.isArray(t)&&(!!("number"==typeof t||"boolean"==typeof t||null==t||n.isSymbol(t))||"string"==typeof t&&(a.test(t)||!i.test(t))||null!=e&&Object.hasOwn(e,t))}},1571:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(2465),i=r(2194),a=r(4804),o=r(4517);e.iteratee=function(t){if(null==t)return n.identity;switch(typeof t){case"function":return t;case"object":if(Array.isArray(t)&&2===t.length)return o.matchesProperty(t[0],t[1]);return a.matches(t);case"string":case"symbol":case"number":return i.property(t)}}},1643:(t,e,r)=>{"use strict";r.d(e,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},1807:(t,e,r)=>{"use strict";r.d(e,{r:()=>a});var n=r(2115),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},1928:(t,e,r)=>{"use strict";r.d(e,{i:()=>o});var n=r(4890);function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var o=(t,e,r,i)=>{if(null==e)return n.k_;var o=function(t,e,r){return"axis"===e?"click"===r?t.axisInteraction.click:t.axisInteraction.hover:"click"===r?t.itemInteraction.click:t.itemInteraction.hover}(t,e,r);if(null==o)return n.k_;if(o.active)return o;if(t.keyboardInteraction.active)return t.keyboardInteraction;if(t.syncInteraction.active&&null!=t.syncInteraction.index)return t.syncInteraction;var l=!0===t.settings.active;if(null!=o.index){if(l)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.k_),{},{coordinate:o.coordinate})}},1971:(t,e,r)=>{"use strict";r.d(e,{G:()=>f,j:()=>l});var n=r(5643),i=r(2115),a=r(5064),o=t=>t,l=()=>{var t=(0,i.useContext)(a.E);return t?t.store.dispatch:o},u=()=>{},c=()=>u,s=(t,e)=>t===e;function f(t){var e=(0,i.useContext)(a.E);return(0,n.useSyncExternalStoreWithSelector)(e?e.subscription.addNestedSub:c,e?e.store.getState:u,e?e.store.getState:u,e?t:u,s)}},1992:(t,e,r)=>{"use strict";r(4993)},2071:(t,e,r)=>{"use strict";r.d(e,{h:()=>x});var n=r(2115),i=r(2596),a=r(9584),o=r(5306),l=r(1971),u=r(2183),c=r(9449),s=r(1807),f=t=>{var{ticks:e,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=t,o=0;if(e){e.forEach(t=>{if(t){var e=t.getBoundingClientRect();e.width>o&&(o=e.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0},h=r(379),d=["dangerouslySetInnerHTML","ticks"];function p(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(){return(y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function v(t){var e=(0,l.j)();return(0,n.useEffect)(()=>(e((0,o.cU)(t)),()=>{e((0,o.fR)(t))}),[t,e]),null}var g=t=>{var e,{yAxisId:r,className:p,width:v,label:g}=t,m=(0,n.useRef)(null),b=(0,n.useRef)(null),x=(0,l.G)(c.c2),w=(0,s.r)(),O=(0,l.j)(),j="yAxis",P=(0,l.G)(t=>(0,u.iV)(t,j,r,w)),E=(0,l.G)(t=>(0,u.wP)(t,r)),A=(0,l.G)(t=>(0,u.KR)(t,r)),M=(0,l.G)(t=>(0,u.Zi)(t,j,r,w));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==v||!E||(0,h.Z)(g)||(0,n.isValidElement)(g))){var t,e=m.current,i=null==e||null==(t=e.tickRefs)?void 0:t.current,{tickSize:a,tickMargin:l}=e.props,u=f({ticks:i,label:b.current,labelGapWithTick:5,tickSize:a,tickMargin:l});Math.round(E.width)!==Math.round(u)&&O((0,o.QG)({id:r,width:u}))}},[m,null==m||null==(e=m.current)||null==(e=e.tickRefs)?void 0:e.current,null==E?void 0:E.width,E,O,g,r,v]),null==E||null==A)return null;var{dangerouslySetInnerHTML:S,ticks:_}=t,k=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,d);return n.createElement(a.u,y({},k,{ref:m,labelRef:b,scale:P,x:A.x,y:A.y,width:E.width,height:E.height,className:(0,i.$)("recharts-".concat(j," ").concat(j),p),viewBox:x,ticks:M}))},m=t=>{var e,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(v,{interval:null!=(e=t.interval)?e:"preserveEnd",id:t.yAxisId,scale:t.scale,type:t.type,domain:t.domain,allowDataOverflow:t.allowDataOverflow,dataKey:t.dataKey,allowDuplicatedCategory:t.allowDuplicatedCategory,allowDecimals:t.allowDecimals,tickCount:t.tickCount,padding:t.padding,includeHidden:null!=(r=t.includeHidden)&&r,reversed:t.reversed,ticks:t.ticks,width:t.width,orientation:t.orientation,mirror:t.mirror,hide:t.hide,unit:t.unit,name:t.name,angle:null!=(i=t.angle)?i:0,minTickGap:null!=(a=t.minTickGap)?a:5,tick:null==(o=t.tick)||o,tickFormatter:t.tickFormatter}),n.createElement(g,t))},b={allowDataOverflow:u.cd.allowDataOverflow,allowDecimals:u.cd.allowDecimals,allowDuplicatedCategory:u.cd.allowDuplicatedCategory,hide:!1,mirror:u.cd.mirror,orientation:u.cd.orientation,padding:u.cd.padding,reversed:u.cd.reversed,scale:u.cd.scale,tickCount:u.cd.tickCount,type:u.cd.type,width:u.cd.width,yAxisId:0};class x extends n.Component{render(){return n.createElement(m,this.props)}}p(x,"displayName","YAxis"),p(x,"defaultProps",b)},2085:(t,e,r)=>{"use strict";r.d(e,{F:()=>o});var n=r(2596);let i=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,a=n.$,o=(t,e)=>r=>{var n;if((null==e?void 0:e.variants)==null)return a(t,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:l}=e,u=Object.keys(o).map(t=>{let e=null==r?void 0:r[t],n=null==l?void 0:l[t];if(null===e)return null;let a=i(e)||i(n);return o[t][a]}),c=r&&Object.entries(r).reduce((t,e)=>{let[r,n]=e;return void 0===n||(t[r]=n),t},{});return a(t,u,null==e||null==(n=e.compoundVariants)?void 0:n.reduce((t,e)=>{let{class:r,className:n,...i}=e;return Object.entries(i).every(t=>{let[e,r]=t;return Array.isArray(r)?r.includes({...l,...c}[e]):({...l,...c})[e]===r})?[...t,r,n]:t},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2183:(t,e,r)=>{"use strict";r.d(e,{kz:()=>ia,fb:()=>n3,q:()=>iO,tP:()=>iC,g1:()=>iB,iv:()=>ar,Nk:()=>n6,pM:()=>ir,Oz:()=>ix,tF:()=>at,rj:()=>n2,ec:()=>nJ,bb:()=>iP,xp:()=>iz,wL:()=>iS,sr:()=>iD,Qn:()=>iI,MK:()=>it,IO:()=>n0,P9:()=>ip,S5:()=>is,PU:()=>nR,cd:()=>nF,eo:()=>nZ,yi:()=>ih,ZB:()=>ai,D5:()=>iW,iV:()=>iq,Hd:()=>nV,Gx:()=>al,_y:()=>ac,um:()=>nq,gT:()=>iv,Kr:()=>id,$X:()=>im,Zi:()=>aa,CR:()=>ao,ld:()=>nY,L$:()=>i8,Rl:()=>n$,Lw:()=>i2,KR:()=>i7,sf:()=>nK,wP:()=>i9});var n,i,a,o,l,u,c,s={};r.r(s),r.d(s,{scaleBand:()=>w,scaleDiverging:()=>function t(){var e=tH(r3()(tM));return e.copy=function(){return r5(e,t())},y.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=tQ(r3()).domain([.1,1,10]);return e.copy=function(){return r5(e,t()).base(e.base())},y.apply(e,arguments)},scaleDivergingPow:()=>r8,scaleDivergingSqrt:()=>r7,scaleDivergingSymlog:()=>function t(){var e=t2(r3());return e.copy=function(){return r5(e,t()).constant(e.constant())},y.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,tE),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,tE):[0,1],tH(n)},scaleImplicit:()=>b,scaleLinear:()=>function t(){var e=tD();return e.copy=function(){return tC(e,t())},p.apply(e,arguments),tH(e)},scaleLog:()=>function t(){let e=tQ(tT()).domain([1,10]);return e.copy=()=>tC(e,t()).base(e.base()),p.apply(e,arguments),e},scaleOrdinal:()=>x,scalePoint:()=>O,scalePow:()=>t8,scaleQuantile:()=>function t(){var e,r=[],n=[],i=[];function a(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=N){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,a=Math.floor(i),o=+r(t[a],a,t);return o+(r(t[a+1],a+1,t)-o)*(i-a)}}(r,t/e);return o}function o(t){return null==t||isNaN(t*=1)?e:n[z(i,t)]}return o.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},o.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(k),a()},o.range=function(t){return arguments.length?(n=Array.from(t),a()):n.slice()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.quantiles=function(){return i.slice()},o.copy=function(){return t().domain(r).range(n).unknown(e)},p.apply(o,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,i=1,a=[.5],o=[0,1];function l(t){return null!=t&&t<=t?o[z(a,t,0,i)]:e}function u(){var t=-1;for(a=Array(i);++t<i;)a[t]=((t+1)*n-(t-i)*r)/(i+1);return l}return l.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},l.range=function(t){return arguments.length?(i=(o=Array.from(t)).length-1,u()):o.slice()},l.invertExtent=function(t){var e=o.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,a[0]]:e>=i?[a[i-1],n]:[a[e-1],a[e]]},l.unknown=function(t){return arguments.length&&(e=t),l},l.thresholds=function(){return a.slice()},l.copy=function(){return t().domain([r,n]).range(o).unknown(e)},p.apply(tH(l),arguments)},scaleRadial:()=>function t(){var e,r=tD(),n=[0,1],i=!1;function a(t){var n,a=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(a)?e:i?Math.round(a):a}return a.invert=function(t){return r.invert(t9(t))},a.domain=function(t){return arguments.length?(r.domain(t),a):r.domain()},a.range=function(t){return arguments.length?(r.range((n=Array.from(t,tE)).map(t9)),a):n.slice()},a.rangeRound=function(t){return a.range(t).round(!0)},a.round=function(t){return arguments.length?(i=!!t,a):i},a.clamp=function(t){return arguments.length?(r.clamp(t),a):r.clamp()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},p.apply(a,arguments),tH(a)},scaleSequential:()=>function t(){var e=tH(r2()(tM));return e.copy=function(){return r5(e,t())},y.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=tQ(r2()).domain([1,10]);return e.copy=function(){return r5(e,t()).base(e.base())},y.apply(e,arguments)},scaleSequentialPow:()=>r6,scaleSequentialQuantile:()=>function t(){var e=[],r=tM;function n(t){if(null!=t&&!isNaN(t*=1))return r((z(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(k),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return ee(t);if(e>=1)return et(t);var n,i=(n-1)*e,a=Math.floor(i),o=et((function t(e,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(a=void 0===a?er:function(t=k){if(t===k)return er;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,u=Math.log(o),c=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*c*(o-c)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*c/o+s)),h=Math.min(i,Math.floor(r+(o-l)*c/o+s));t(e,r,f,h,a)}let o=e[r],l=n,u=i;for(en(e,n,r),a(e[i],o)>0&&en(e,n,i);l<u;){for(en(e,l,u),++l,--u;0>a(e[l],o);)++l;for(;a(e[u],o)>0;)--u}0===a(e[n],o)?en(e,n,u):en(e,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return e})(t,a).subarray(0,a+1));return o+(ee(t.subarray(a+1))-o)*(i-a)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},y.apply(n,arguments)},scaleSequentialSqrt:()=>r4,scaleSequentialSymlog:()=>function t(){var e=t2(r2());return e.copy=function(){return r5(e,t()).constant(e.constant())},y.apply(e,arguments)},scaleSqrt:()=>t7,scaleSymlog:()=>function t(){var e=t2(tT());return e.copy=function(){return tC(e,t()).constant(e.constant())},p.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],i=1;function a(t){return null!=t&&t<=t?n[z(r,t,0,i)]:e}return a.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),a):r.slice()},a.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},a.unknown=function(t){return arguments.length?(e=t,a):e},a.copy=function(){return t().domain(r).range(n).unknown(e)},p.apply(a,arguments)},scaleTime:()=>r0,scaleUtc:()=>r1,tickFormat:()=>tG});var f=r(8924),h=r(3949),d=r.n(h);function p(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function y(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class v extends Map{constructor(t,e=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(g(this,t))}has(t){return super.has(g(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function g({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function m(t){return null!==t&&"object"==typeof t?t.valueOf():t}let b=Symbol("implicit");function x(){var t=new v,e=[],r=[],n=b;function i(i){let a=t.get(i);if(void 0===a){if(n!==b)return n;t.set(i,a=e.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new v,r))t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(n=t,i):n},i.copy=function(){return x(e,r).unknown(n)},p.apply(i,arguments),i}function w(){var t,e,r=x().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,u=0,c=0,s=.5;function f(){var r=n().length,f=o<a,h=f?o:a,d=f?a:o;t=(d-h)/Math.max(1,r-u+2*c),l&&(t=Math.floor(t)),h+=(d-h-t*(r-u))*s,e=t*(1-u),l&&(h=Math.round(h),e=Math.round(e));var p=(function(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),a=Array(i);++n<i;)a[n]=t+n*r;return a})(r).map(function(e){return h+t*e});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([a,o]=t,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(t){return[a,o]=t,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(l=!!t,f()):l},r.padding=function(t){return arguments.length?(u=Math.min(1,c=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(c=+t,f()):c},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return w(n(),[a,o]).round(l).paddingInner(u).paddingOuter(c).align(s)},p.apply(f(),arguments)}function O(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(w.apply(null,arguments).paddingInner(1))}let j=Math.sqrt(50),P=Math.sqrt(10),E=Math.sqrt(2);function A(t,e,r){let n,i,a,o=(e-t)/Math.max(0,r),l=Math.floor(Math.log10(o)),u=o/Math.pow(10,l),c=u>=j?10:u>=P?5:u>=E?2:1;return(l<0?(n=Math.round(t*(a=Math.pow(10,-l)/c)),i=Math.round(e*a),n/a<t&&++n,i/a>e&&--i,a=-a):(n=Math.round(t/(a=Math.pow(10,l)*c)),i=Math.round(e/a),n*a<t&&++n,i*a>e&&--i),i<n&&.5<=r&&r<2)?A(t,e,2*r):[n,i,a]}function M(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,a,o]=n?A(e,t,r):A(t,e,r);if(!(a>=i))return[];let l=a-i+1,u=Array(l);if(n)if(o<0)for(let t=0;t<l;++t)u[t]=-((a-t)/o);else for(let t=0;t<l;++t)u[t]=(a-t)*o;else if(o<0)for(let t=0;t<l;++t)u[t]=-((i+t)/o);else for(let t=0;t<l;++t)u[t]=(i+t)*o;return u}function S(t,e,r){return A(t*=1,e*=1,r*=1)[2]}function _(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?S(e,t,r):S(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function k(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function C(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function T(t){let e,r,n;function i(t,n,a=0,o=t.length){if(a<o){if(0!==e(n,n))return o;do{let e=a+o>>>1;0>r(t[e],n)?a=e+1:o=e}while(a<o)}return a}return 2!==t.length?(e=k,r=(e,r)=>k(t(e),r),n=(e,r)=>t(e)-r):(e=t===k||t===C?t:D,r=t,n=t),{left:i,center:function(t,e,r=0,a=t.length){let o=i(t,e,r,a-1);return o>r&&n(t[o-1],e)>-n(t[o],e)?o-1:o},right:function(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>=r(t[e],n)?i=e+1:a=e}while(i<a)}return i}}}function D(){return 0}function N(t){return null===t?NaN:+t}let I=T(k),z=I.right;function L(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function B(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function U(){}I.left,T(N).center;var R="\\s*([+-]?\\d+)\\s*",$="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",F="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",K=/^#([0-9a-f]{3,8})$/,G=RegExp(`^rgb\\(${R},${R},${R}\\)$`),H=RegExp(`^rgb\\(${F},${F},${F}\\)$`),W=RegExp(`^rgba\\(${R},${R},${R},${$}\\)$`),V=RegExp(`^rgba\\(${F},${F},${F},${$}\\)$`),q=RegExp(`^hsl\\(${$},${F},${F}\\)$`),Z=RegExp(`^hsla\\(${$},${F},${F},${$}\\)$`),Y={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function X(){return this.rgb().formatHex()}function J(){return this.rgb().formatRgb()}function Q(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=K.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?tt(e):3===r?new tn(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?te(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?te(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=G.exec(t))?new tn(e[1],e[2],e[3],1):(e=H.exec(t))?new tn(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=W.exec(t))?te(e[1],e[2],e[3],e[4]):(e=V.exec(t))?te(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=q.exec(t))?tc(e[1],e[2]/100,e[3]/100,1):(e=Z.exec(t))?tc(e[1],e[2]/100,e[3]/100,e[4]):Y.hasOwnProperty(t)?tt(Y[t]):"transparent"===t?new tn(NaN,NaN,NaN,0):null}function tt(t){return new tn(t>>16&255,t>>8&255,255&t,1)}function te(t,e,r,n){return n<=0&&(t=e=r=NaN),new tn(t,e,r,n)}function tr(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof U||(i=Q(i)),i)?new tn((i=i.rgb()).r,i.g,i.b,i.opacity):new tn:new tn(t,e,r,null==n?1:n)}function tn(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function ti(){return`#${tu(this.r)}${tu(this.g)}${tu(this.b)}`}function ta(){let t=to(this.opacity);return`${1===t?"rgb(":"rgba("}${tl(this.r)}, ${tl(this.g)}, ${tl(this.b)}${1===t?")":`, ${t})`}`}function to(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function tl(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function tu(t){return((t=tl(t))<16?"0":"")+t.toString(16)}function tc(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new tf(t,e,r,n)}function ts(t){if(t instanceof tf)return new tf(t.h,t.s,t.l,t.opacity);if(t instanceof U||(t=Q(t)),!t)return new tf;if(t instanceof tf)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),a=Math.max(e,r,n),o=NaN,l=a-i,u=(a+i)/2;return l?(o=e===a?(r-n)/l+(r<n)*6:r===a?(n-e)/l+2:(e-r)/l+4,l/=u<.5?a+i:2-a-i,o*=60):l=u>0&&u<1?0:o,new tf(o,l,u,t.opacity)}function tf(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function th(t){return(t=(t||0)%360)<0?t+360:t}function td(t){return Math.max(0,Math.min(1,t||0))}function tp(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function ty(t,e,r,n,i){var a=t*t,o=a*t;return((1-3*t+3*a-o)*e+(4-6*a+3*o)*r+(1+3*t+3*a-3*o)*n+o*i)/6}L(U,Q,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:X,formatHex:X,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ts(this).formatHsl()},formatRgb:J,toString:J}),L(tn,tr,B(U,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tn(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tn(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new tn(tl(this.r),tl(this.g),tl(this.b),to(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ti,formatHex:ti,formatHex8:function(){return`#${tu(this.r)}${tu(this.g)}${tu(this.b)}${tu((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ta,toString:ta})),L(tf,function(t,e,r,n){return 1==arguments.length?ts(t):new tf(t,e,r,null==n?1:n)},B(U,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tf(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tf(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new tn(tp(t>=240?t-240:t+120,i,n),tp(t,i,n),tp(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new tf(th(this.h),td(this.s),td(this.l),to(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=to(this.opacity);return`${1===t?"hsl(":"hsla("}${th(this.h)}, ${100*td(this.s)}%, ${100*td(this.l)}%${1===t?")":`, ${t})`}`}}));let tv=t=>()=>t;function tg(t,e){var r,n,i=e-t;return i?(r=t,n=i,function(t){return r+t*n}):tv(isNaN(t)?e:t)}let tm=function t(e){var r,n=1==(r=+e)?tg:function(t,e){var n,i,a;return e-t?(n=t,i=e,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(t){return Math.pow(n+t*i,a)}):tv(isNaN(t)?e:t)};function i(t,e){var r=n((t=tr(t)).r,(e=tr(e)).r),i=n(t.g,e.g),a=n(t.b,e.b),o=tg(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=a(e),t.opacity=o(e),t+""}}return i.gamma=t,i}(1);function tb(t){return function(e){var r,n,i=e.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=tr(e[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=t(a),o=t(o),l=t(l),n.opacity=1,function(t){return n.r=a(t),n.g=o(t),n.b=l(t),n+""}}}tb(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],a=t[n+1],o=n>0?t[n-1]:2*i-a,l=n<e-1?t[n+2]:2*a-i;return ty((r-n/e)*e,o,i,a,l)}}),tb(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],a=t[n%e],o=t[(n+1)%e],l=t[(n+2)%e];return ty((r-n/e)*e,i,a,o,l)}});function tx(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var tw=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tO=RegExp(tw.source,"g");function tj(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?tv(e):("number"===i?tx:"string"===i?(n=Q(e))?(e=n,tm):function(t,e){var r,n,i,a,o,l=tw.lastIndex=tO.lastIndex=0,u=-1,c=[],s=[];for(t+="",e+="";(i=tw.exec(t))&&(a=tO.exec(e));)(o=a.index)>l&&(o=e.slice(l,o),c[u]?c[u]+=o:c[++u]=o),(i=i[0])===(a=a[0])?c[u]?c[u]+=a:c[++u]=a:(c[++u]=null,s.push({i:u,x:tx(i,a)})),l=tO.lastIndex;return l<e.length&&(o=e.slice(l),c[u]?c[u]+=o:c[++u]=o),c.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)c[(r=s[n]).i]=r.x(t);return c.join("")})}:e instanceof Q?tm:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=tj(t[r],e[r]);for(;r<n;++r)o[r]=e[r];return function(t){for(r=0;r<i;++r)o[r]=a[r](t);return o}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=tj(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:tx:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(a){for(r=0;r<n;++r)i[r]=t[r]*(1-a)+e[r]*a;return i}})(t,e)}function tP(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function tE(t){return+t}var tA=[0,1];function tM(t){return t}function tS(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function t_(t,e,r){var n=t[0],i=t[1],a=e[0],o=e[1];return i<n?(n=tS(i,n),a=r(o,a)):(n=tS(n,i),a=r(a,o)),function(t){return a(n(t))}}function tk(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),a=Array(n),o=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++o<n;)i[o]=tS(t[o],t[o+1]),a[o]=r(e[o],e[o+1]);return function(e){var r=z(t,e,1,n)-1;return a[r](i[r](e))}}function tC(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function tT(){var t,e,r,n,i,a,o=tA,l=tA,u=tj,c=tM;function s(){var t,e,r,u=Math.min(o.length,l.length);return c!==tM&&(t=o[0],e=o[u-1],t>e&&(r=t,t=e,e=r),c=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?tk:t_,i=a=null,f}function f(e){return null==e||isNaN(e*=1)?r:(i||(i=n(o.map(t),l,u)))(t(c(e)))}return f.invert=function(r){return c(e((a||(a=n(l,o.map(t),tx)))(r)))},f.domain=function(t){return arguments.length?(o=Array.from(t,tE),s()):o.slice()},f.range=function(t){return arguments.length?(l=Array.from(t),s()):l.slice()},f.rangeRound=function(t){return l=Array.from(t),u=tP,s()},f.clamp=function(t){return arguments.length?(c=!!t||tM,s()):c!==tM},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function tD(){return tT()(tM,tM)}var tN=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tI(t){var e;if(!(e=tN.exec(t)))throw Error("invalid format: "+t);return new tz({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tz(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tL(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tB(t){return(t=tL(Math.abs(t)))?t[1]:NaN}function tU(t,e){var r=tL(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}tI.prototype=tz.prototype,tz.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let tR={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tU(100*t,e),r:tU,s:function(t,e){var r=tL(t,e);if(!r)return t+"";var i=r[0],a=r[1],o=a-(n=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,l=i.length;return o===l?i:o>l?i+Array(o-l+1).join("0"):o>0?i.slice(0,o)+"."+i.slice(o):"0."+Array(1-o).join("0")+tL(t,Math.max(0,e+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function t$(t){return t}var tF=Array.prototype.map,tK=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tG(t,e,r,n){var i,l,u,c=_(t,e,r);switch((n=tI(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(u=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tB(s)/3)))-tB(Math.abs(c))))||(n.precision=u),o(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(u=Math.max(0,tB(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=c)))-tB(i))+1)||(n.precision=u-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(u=Math.max(0,-tB(Math.abs(c))))||(n.precision=u-("%"===n.type)*2)}return a(n)}function tH(t){var e=t.domain;return t.ticks=function(t){var r=e();return M(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tG(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,a=e(),o=0,l=a.length-1,u=a[o],c=a[l],s=10;for(c<u&&(i=u,u=c,c=i,i=o,o=l,l=i);s-- >0;){if((i=S(u,c,r))===n)return a[o]=u,a[l]=c,e(a);if(i>0)u=Math.floor(u/i)*i,c=Math.ceil(c/i)*i;else if(i<0)u=Math.ceil(u*i)/i,c=Math.floor(c*i)/i;else break;n=i}return t},t}function tW(t,e){t=t.slice();var r,n=0,i=t.length-1,a=t[n],o=t[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),t[n]=e.floor(a),t[i]=e.ceil(o),t}function tV(t){return Math.log(t)}function tq(t){return Math.exp(t)}function tZ(t){return-Math.log(-t)}function tY(t){return-Math.exp(-t)}function tX(t){return isFinite(t)?+("1e"+t):t<0?0:t}function tJ(t){return(e,r)=>-t(-e,r)}function tQ(t){let e,r,n=t(tV,tq),i=n.domain,o=10;function l(){var a,l;return e=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(l=o)?tX:l===Math.E?Math.exp:t=>Math.pow(l,t),i()[0]<0?(e=tJ(e),r=tJ(r),t(tZ,tY)):t(tV,tq),n}return n.base=function(t){return arguments.length?(o=+t,l()):o},n.domain=function(t){return arguments.length?(i(t),l()):i()},n.ticks=t=>{let n,a,l=i(),u=l[0],c=l[l.length-1],s=c<u;s&&([u,c]=[c,u]);let f=e(u),h=e(c),d=null==t?10:+t,p=[];if(!(o%1)&&h-f<d){if(f=Math.floor(f),h=Math.ceil(h),u>0){for(;f<=h;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>c)break;p.push(a)}}else for(;f<=h;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>c)break;p.push(a)}2*p.length<d&&(p=M(u,c,d))}else p=M(f,h,Math.min(h-f,d)).map(r);return s?p.reverse():p},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=tI(i)).precision||(i.trim=!0),i=a(i)),t===1/0)return i;let l=Math.max(1,o*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*o<o-.5&&(n*=o),n<=l?i(t):""}},n.nice=()=>i(tW(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function t0(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function t1(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function t2(t){var e=1,r=t(t0(1),t1(e));return r.constant=function(r){return arguments.length?t(t0(e=+r),t1(e)):e},tH(r)}function t5(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function t6(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function t4(t){return t<0?-t*t:t*t}function t3(t){var e=t(tM,tM),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tM,tM):.5===r?t(t6,t4):t(t5(r),t5(1/r)):r},tH(e)}function t8(){var t=t3(tT());return t.copy=function(){return tC(t,t8()).exponent(t.exponent())},p.apply(t,arguments),t}function t7(){return t8.apply(null,arguments).exponent(.5)}function t9(t){return Math.sign(t)*t*t}function et(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function ee(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}a=(i=function(t){var e,r,i,a=void 0===t.grouping||void 0===t.thousands?t$:(e=tF.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,a=[],o=0,l=e[0],u=0;i>0&&l>0&&(u+l+1>n&&(l=Math.max(1,n-u)),a.push(t.substring(i-=l,i+l)),!((u+=l+1)>n));)l=e[o=(o+1)%e.length];return a.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",l=void 0===t.currency?"":t.currency[1]+"",u=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?t$:(i=tF.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return i[+t]})}),s=void 0===t.percent?"%":t.percent+"",f=void 0===t.minus?"−":t.minus+"",h=void 0===t.nan?"NaN":t.nan+"";function d(t){var e=(t=tI(t)).fill,r=t.align,i=t.sign,d=t.symbol,p=t.zero,y=t.width,v=t.comma,g=t.precision,m=t.trim,b=t.type;"n"===b?(v=!0,b="g"):tR[b]||(void 0===g&&(g=12),m=!0,b="g"),(p||"0"===e&&"="===r)&&(p=!0,e="0",r="=");var x="$"===d?o:"#"===d&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===d?l:/[%p]/.test(b)?s:"",O=tR[b],j=/[defgprs%]/.test(b);function P(t){var o,l,s,d=x,P=w;if("c"===b)P=O(t)+P,t="";else{var E=(t*=1)<0||1/t<0;if(t=isNaN(t)?h:O(Math.abs(t),g),m&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),E&&0==+t&&"+"!==i&&(E=!1),d=(E?"("===i?i:f:"-"===i||"("===i?"":i)+d,P=("s"===b?tK[8+n/3]:"")+P+(E&&"("===i?")":""),j){for(o=-1,l=t.length;++o<l;)if(48>(s=t.charCodeAt(o))||s>57){P=(46===s?u+t.slice(o+1):t.slice(o))+P,t=t.slice(0,o);break}}}v&&!p&&(t=a(t,1/0));var A=d.length+t.length+P.length,M=A<y?Array(y-A+1).join(e):"";switch(v&&p&&(t=a(M+t,M.length?y-P.length:1/0),M=""),r){case"<":t=d+t+P+M;break;case"=":t=d+M+t+P;break;case"^":t=M.slice(0,A=M.length>>1)+d+t+P+M.slice(A);break;default:t=M+d+t+P}return c(t)}return g=void 0===g?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),P.toString=function(){return t+""},P}return{format:d,formatPrefix:function(t,e){var r=d(((t=tI(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tB(e)/3))),i=Math.pow(10,-n),a=tK[8+n/3];return function(t){return r(i*t)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,o=i.formatPrefix;function er(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function en(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let ei=new Date,ea=new Date;function eo(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),e(r,a),t(r);while(o<r&&r<n);return l},i.filter=r=>eo(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(i.count=(e,n)=>(ei.setTime(+e),ea.setTime(+n),t(ei),t(ea),Math.floor(r(ei,ea))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let el=eo(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);el.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?eo(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):el:null,el.range;let eu=eo(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());eu.range;let ec=eo(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());ec.range;let es=eo(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());es.range;let ef=eo(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());ef.range;let eh=eo(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());eh.range;let ed=eo(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);ed.range;let ep=eo(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);ep.range;let ey=eo(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function ev(t){return eo(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}ey.range;let eg=ev(0),em=ev(1),eb=ev(2),ex=ev(3),ew=ev(4),eO=ev(5),ej=ev(6);function eP(t){return eo(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}eg.range,em.range,eb.range,ex.range,ew.range,eO.range,ej.range;let eE=eP(0),eA=eP(1),eM=eP(2),eS=eP(3),e_=eP(4),ek=eP(5),eC=eP(6);eE.range,eA.range,eM.range,eS.range,e_.range,ek.range,eC.range;let eT=eo(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());eT.range;let eD=eo(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eD.range;let eN=eo(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eN.every=t=>isFinite(t=Math.floor(t))&&t>0?eo(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,eN.range;let eI=eo(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ez(t,e,r,n,i,a){let o=[[eu,1,1e3],[eu,5,5e3],[eu,15,15e3],[eu,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function l(e,r,n){let i=Math.abs(r-e)/n,a=T(([,,t])=>t).right(o,i);if(a===o.length)return t.every(_(e/31536e6,r/31536e6,n));if(0===a)return el.every(Math.max(_(e,r,n),1));let[l,u]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:l(t,e,r),a=i?i.range(t,+e+1):[];return n?a.reverse():a},l]}eI.every=t=>isFinite(t=Math.floor(t))&&t>0?eo(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,eI.range;let[eL,eB]=ez(eI,eD,eE,ey,eh,es),[eU,eR]=ez(eN,eT,eg,ed,ef,ec);function e$(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eF(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function eK(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var eG={"-":"",_:" ",0:"0"},eH=/^\s*\d+/,eW=/^%/,eV=/[\\^$*+?|[\]().{}]/g;function eq(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",a=i.length;return n+(a<r?Array(r-a+1).join(e)+i:i)}function eZ(t){return t.replace(eV,"\\$&")}function eY(t){return RegExp("^(?:"+t.map(eZ).join("|")+")","i")}function eX(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eJ(t,e,r){var n=eH.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eQ(t,e,r){var n=eH.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function e0(t,e,r){var n=eH.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function e1(t,e,r){var n=eH.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function e2(t,e,r){var n=eH.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function e5(t,e,r){var n=eH.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function e6(t,e,r){var n=eH.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function e4(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function e3(t,e,r){var n=eH.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function e8(t,e,r){var n=eH.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function e7(t,e,r){var n=eH.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function e9(t,e,r){var n=eH.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function rt(t,e,r){var n=eH.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function re(t,e,r){var n=eH.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function rr(t,e,r){var n=eH.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function rn(t,e,r){var n=eH.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function ri(t,e,r){var n=eH.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ra(t,e,r){var n=eW.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function ro(t,e,r){var n=eH.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function rl(t,e,r){var n=eH.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function ru(t,e){return eq(t.getDate(),e,2)}function rc(t,e){return eq(t.getHours(),e,2)}function rs(t,e){return eq(t.getHours()%12||12,e,2)}function rf(t,e){return eq(1+ed.count(eN(t),t),e,3)}function rh(t,e){return eq(t.getMilliseconds(),e,3)}function rd(t,e){return rh(t,e)+"000"}function rp(t,e){return eq(t.getMonth()+1,e,2)}function ry(t,e){return eq(t.getMinutes(),e,2)}function rv(t,e){return eq(t.getSeconds(),e,2)}function rg(t){var e=t.getDay();return 0===e?7:e}function rm(t,e){return eq(eg.count(eN(t)-1,t),e,2)}function rb(t){var e=t.getDay();return e>=4||0===e?ew(t):ew.ceil(t)}function rx(t,e){return t=rb(t),eq(ew.count(eN(t),t)+(4===eN(t).getDay()),e,2)}function rw(t){return t.getDay()}function rO(t,e){return eq(em.count(eN(t)-1,t),e,2)}function rj(t,e){return eq(t.getFullYear()%100,e,2)}function rP(t,e){return eq((t=rb(t)).getFullYear()%100,e,2)}function rE(t,e){return eq(t.getFullYear()%1e4,e,4)}function rA(t,e){var r=t.getDay();return eq((t=r>=4||0===r?ew(t):ew.ceil(t)).getFullYear()%1e4,e,4)}function rM(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+eq(e/60|0,"0",2)+eq(e%60,"0",2)}function rS(t,e){return eq(t.getUTCDate(),e,2)}function r_(t,e){return eq(t.getUTCHours(),e,2)}function rk(t,e){return eq(t.getUTCHours()%12||12,e,2)}function rC(t,e){return eq(1+ep.count(eI(t),t),e,3)}function rT(t,e){return eq(t.getUTCMilliseconds(),e,3)}function rD(t,e){return rT(t,e)+"000"}function rN(t,e){return eq(t.getUTCMonth()+1,e,2)}function rI(t,e){return eq(t.getUTCMinutes(),e,2)}function rz(t,e){return eq(t.getUTCSeconds(),e,2)}function rL(t){var e=t.getUTCDay();return 0===e?7:e}function rB(t,e){return eq(eE.count(eI(t)-1,t),e,2)}function rU(t){var e=t.getUTCDay();return e>=4||0===e?e_(t):e_.ceil(t)}function rR(t,e){return t=rU(t),eq(e_.count(eI(t),t)+(4===eI(t).getUTCDay()),e,2)}function r$(t){return t.getUTCDay()}function rF(t,e){return eq(eA.count(eI(t)-1,t),e,2)}function rK(t,e){return eq(t.getUTCFullYear()%100,e,2)}function rG(t,e){return eq((t=rU(t)).getUTCFullYear()%100,e,2)}function rH(t,e){return eq(t.getUTCFullYear()%1e4,e,4)}function rW(t,e){var r=t.getUTCDay();return eq((t=r>=4||0===r?e_(t):e_.ceil(t)).getUTCFullYear()%1e4,e,4)}function rV(){return"+0000"}function rq(){return"%"}function rZ(t){return+t}function rY(t){return Math.floor(t/1e3)}function rX(t){return new Date(t)}function rJ(t){return t instanceof Date?+t:+new Date(+t)}function rQ(t,e,r,n,i,a,o,l,u,c){var s=tD(),f=s.invert,h=s.domain,d=c(".%L"),p=c(":%S"),y=c("%I:%M"),v=c("%I %p"),g=c("%a %d"),m=c("%b %d"),b=c("%B"),x=c("%Y");function w(t){return(u(t)<t?d:l(t)<t?p:o(t)<t?y:a(t)<t?v:n(t)<t?i(t)<t?g:m:r(t)<t?b:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?h(Array.from(t,rJ)):h().map(rX)},s.ticks=function(e){var r=h();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:c(e)},s.nice=function(t){var r=h();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?h(tW(r,t)):s},s.copy=function(){return tC(s,rQ(t,e,r,n,i,a,o,l,u,c))},s}function r0(){return p.apply(rQ(eU,eR,eN,eT,eg,ed,ef,ec,eu,u).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return p.apply(rQ(eL,eB,eI,eD,eE,ep,eh,es,eu,c).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var t,e,r,n,i,a=0,o=1,l=tM,u=!1;function c(e){return null==e||isNaN(e*=1)?i:l(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,l=t(r,n),c):[l(0),l(1)]}}return c.domain=function(i){return arguments.length?([a,o]=i,t=n(a*=1),e=n(o*=1),r=t===e?0:1/(e-t),c):[a,o]},c.clamp=function(t){return arguments.length?(u=!!t,c):u},c.interpolator=function(t){return arguments.length?(l=t,c):l},c.range=s(tj),c.rangeRound=s(tP),c.unknown=function(t){return arguments.length?(i=t,c):i},function(i){return n=i,t=i(a),e=i(o),r=t===e?0:1/(e-t),c}}function r5(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function r6(){var t=t3(r2());return t.copy=function(){return r5(t,r6()).exponent(t.exponent())},y.apply(t,arguments)}function r4(){return r6.apply(null,arguments).exponent(.5)}function r3(){var t,e,r,n,i,a,o,l=0,u=.5,c=1,s=1,f=tM,h=!1;function d(t){return isNaN(t*=1)?o:(t=.5+((t=+a(t))-e)*(s*t<s*e?n:i),f(h?Math.max(0,Math.min(1,t)):t))}function p(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=tj);for(var r=0,n=e.length-1,i=e[0],a=Array(n<0?0:n);r<n;)a[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return a[e](t-e)}}(t,[r,n,i]),d):[f(0),f(.5),f(1)]}}return d.domain=function(o){return arguments.length?([l,u,c]=o,t=a(l*=1),e=a(u*=1),r=a(c*=1),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,d):[l,u,c]},d.clamp=function(t){return arguments.length?(h=!!t,d):h},d.interpolator=function(t){return arguments.length?(f=t,d):f},d.range=p(tj),d.rangeRound=p(tP),d.unknown=function(t){return arguments.length?(o=t,d):o},function(o){return a=o,t=o(l),e=o(u),r=o(c),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,d}}function r8(){var t=t3(r3());return t.copy=function(){return r5(t,r8()).exponent(t.exponent())},y.apply(t,arguments)}function r7(){return r8.apply(null,arguments).exponent(.5)}u=(l=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,a=t.days,o=t.shortDays,l=t.months,u=t.shortMonths,c=eY(i),s=eX(i),f=eY(a),h=eX(a),d=eY(o),p=eX(o),y=eY(l),v=eX(l),g=eY(u),m=eX(u),b={a:function(t){return o[t.getDay()]},A:function(t){return a[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return l[t.getMonth()]},c:null,d:ru,e:ru,f:rd,g:rP,G:rA,H:rc,I:rs,j:rf,L:rh,m:rp,M:ry,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:rZ,s:rY,S:rv,u:rg,U:rm,V:rx,w:rw,W:rO,x:null,X:null,y:rj,Y:rE,Z:rM,"%":rq},x={a:function(t){return o[t.getUTCDay()]},A:function(t){return a[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return l[t.getUTCMonth()]},c:null,d:rS,e:rS,f:rD,g:rG,G:rW,H:r_,I:rk,j:rC,L:rT,m:rN,M:rI,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:rZ,s:rY,S:rz,u:rL,U:rB,V:rR,w:r$,W:rF,x:null,X:null,y:rK,Y:rH,Z:rV,"%":rq},w={a:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=g.exec(e.slice(r));return n?(t.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return P(t,e,r,n)},d:e7,e:e7,f:ri,g:e6,G:e5,H:rt,I:rt,j:e9,L:rn,m:e8,M:re,p:function(t,e,r){var n=c.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:e3,Q:ro,s:rl,S:rr,u:eQ,U:e0,V:e1,w:eJ,W:e2,x:function(t,e,n){return P(t,r,e,n)},X:function(t,e,r){return P(t,n,e,r)},y:e6,Y:e5,Z:e4,"%":ra};function O(t,e){return function(r){var n,i,a,o=[],l=-1,u=0,c=t.length;for(r instanceof Date||(r=new Date(+r));++l<c;)37===t.charCodeAt(l)&&(o.push(t.slice(u,l)),null!=(i=eG[n=t.charAt(++l)])?n=t.charAt(++l):i="e"===n?" ":"0",(a=e[n])&&(n=a(r,i)),o.push(n),u=l+1);return o.push(t.slice(u,l)),o.join("")}}function j(t,e){return function(r){var n,i,a=eK(1900,void 0,1);if(P(a,t,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!e||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=eF(eK(a.y,0,1))).getUTCDay())>4||0===i?eA.ceil(n):eA(n),n=ep.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=e$(eK(a.y,0,1))).getDay())>4||0===i?em.ceil(n):em(n),n=ed.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?eF(eK(a.y,0,1)).getUTCDay():e$(eK(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,eF(a)):e$(a)}}function P(t,e,r,n){for(var i,a,o=0,l=e.length,u=r.length;o<l;){if(n>=u)return -1;if(37===(i=e.charCodeAt(o++))){if(!(a=w[(i=e.charAt(o++))in eG?e.charAt(o++):i])||(n=a(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(e,b),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",b);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l.parse,c=l.utcFormat,l.utcParse;var r9=r(7238),nt=r(9827),ne=r(356),nr=r(6377),nn=r(8892);function ni(t){if(Array.isArray(t)&&2===t.length){var[e,r]=t;if((0,nn.H)(e)&&(0,nn.H)(r))return!0}return!1}function na(t,e,r){return r?t:[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}var no=r(8870),nl=r.n(no),nu=t=>t,nc={},ns=t=>t===nc,nf=t=>function e(){return 0==arguments.length||1==arguments.length&&ns(arguments.length<=0?void 0:arguments[0])?e:t(...arguments)},nh=(t,e)=>1===t?e:nf(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(t=>t!==nc).length;return a>=t?e(...n):nh(t-a,nf(function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];return e(...n.map(t=>ns(t)?r.shift():t),...r)}))}),nd=t=>nh(t.length,t),np=(t,e)=>{for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},ny=nd((t,e)=>Array.isArray(e)?e.map(t):Object.keys(e).map(t=>e[t]).map(t)),nv=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nu;var n=e.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((t,e)=>e(t),i(...arguments))}},ng=t=>Array.isArray(t)?t.reverse():t.split("").reverse().join(""),nm=t=>{var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return e&&i.every((t,r)=>{var n;return t===(null==(n=e)?void 0:n[r])})?r:(e=i,r=t(...i))}};function nb(t){var e;return 0===t?1:Math.floor(new(nl())(t).abs().log(10).toNumber())+1}function nx(t,e,r){for(var n=new(nl())(t),i=0,a=[];n.lt(e)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}nd((t,e,r)=>{var n=+t;return n+r*(e-n)}),nd((t,e,r)=>{var n=e-t;return(r-t)/(n=n||1/0)}),nd((t,e,r)=>{var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});var nw=t=>{var[e,r]=t,[n,i]=[e,r];return e>r&&([n,i]=[r,e]),[n,i]},nO=(t,e,r)=>{if(t.lte(0))return new(nl())(0);var n=nb(t.toNumber()),i=new(nl())(10).pow(n),a=t.div(i),o=1!==n?.05:.1,l=new(nl())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new(nl())(e?l.toNumber():Math.ceil(l.toNumber()))},nj=(t,e,r)=>{var n=new(nl())(1),i=new(nl())(t);if(!i.isint()&&r){var a=Math.abs(t);a<1?(n=new(nl())(10).pow(nb(t)-1),i=new(nl())(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new(nl())(Math.floor(t)))}else 0===t?i=new(nl())(Math.floor((e-1)/2)):r||(i=new(nl())(Math.floor(t)));var o=Math.floor((e-1)/2);return nv(ny(t=>i.add(new(nl())(t-o).mul(n)).toNumber()),np)(0,e)},nP=function(t,e,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new(nl())(0),tickMin:new(nl())(0),tickMax:new(nl())(0)};var o=nO(new(nl())(e).sub(t).div(r-1),n,a),l=Math.ceil((i=t<=0&&e>=0?new(nl())(0):(i=new(nl())(t).add(e).div(2)).sub(new(nl())(i).mod(o))).sub(t).div(o).toNumber()),u=Math.ceil(new(nl())(e).sub(i).div(o).toNumber()),c=l+u+1;return c>r?nP(t,e,r,n,a+1):(c<r&&(u=e>0?u+(r-c):u,l=e>0?l:l+(r-c)),{step:o,tickMin:i.sub(new(nl())(l).mul(o)),tickMax:i.add(new(nl())(u).mul(o))})},nE=nm(function(t){var[e,r]=t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=nw([e,r]);if(o===-1/0||l===1/0){var u=l===1/0?[o,...np(0,n-1).map(()=>1/0)]:[...np(0,n-1).map(()=>-1/0),l];return e>r?ng(u):u}if(o===l)return nj(o,n,i);var{step:c,tickMin:s,tickMax:f}=nP(o,l,a,i,0),h=nx(s,f.add(new(nl())(.1).mul(c)),c);return e>r?ng(h):h}),nA=nm(function(t,e){var[r,n]=t,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=nw([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(e,2),u=nO(new(nl())(o).sub(a).div(l-1),i,0),c=[...nx(new(nl())(a),new(nl())(o).sub(new(nl())(.99).mul(u)),u),o];return r>n?ng(c):c}),nM=r(2589),nS=r(6908),n_=r(9449),nk=r(972),nC=r(8478),nT=r(7062),nD=(t,e)=>e,nN=(t,e,r)=>r,nI=r(8190),nz=r(4421);function nL(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nB(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nL(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nL(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var nU=[0,"auto"],nR={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},n$=(t,e)=>{var r=t.cartesianAxis.xAxis[e];return null==r?nR:r},nF={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:nU,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nz.tQ},nK=(t,e)=>{var r=t.cartesianAxis.yAxis[e];return null==r?nF:r},nG={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nH=(t,e)=>{var r=t.cartesianAxis.zAxis[e];return null==r?nG:r},nW=(t,e,r)=>{switch(e){case"xAxis":return n$(t,r);case"yAxis":return nK(t,r);case"zAxis":return nH(t,r);case"angleAxis":return(0,nT.Be)(t,r);case"radiusAxis":return(0,nT.Gl)(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},nV=(t,e,r)=>{switch(e){case"xAxis":return n$(t,r);case"yAxis":return nK(t,r);case"angleAxis":return(0,nT.Be)(t,r);case"radiusAxis":return(0,nT.Gl)(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},nq=t=>t.graphicalItems.countOfBars>0;function nZ(t,e){return r=>{switch(t){case"xAxis":return"xAxisId"in r&&r.xAxisId===e;case"yAxis":return"yAxisId"in r&&r.yAxisId===e;case"zAxis":return"zAxisId"in r&&r.zAxisId===e;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===e;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===e;default:return!1}}}var nY=t=>t.graphicalItems.cartesianItems,nX=(0,f.Mz)([nD,nN],nZ),nJ=(t,e,r)=>t.filter(r).filter(t=>(null==e?void 0:e.includeHidden)===!0||!t.hide),nQ=(0,f.Mz)([nY,nW,nX],nJ),n0=t=>t.filter(t=>void 0===t.stackId),n1=(0,f.Mz)([nQ],n0),n2=t=>t.map(t=>t.data).filter(Boolean).flat(1),n5=(0,f.Mz)([nQ],n2),n6=(t,e)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=e;return t.length>0?t:r.slice(n,i+1)},n4=(0,f.Mz)([n5,ne.HS],n6),n3=(t,e,r)=>(null==e?void 0:e.dataKey)!=null?t.map(t=>({value:(0,nt.kr)(t,e.dataKey)})):r.length>0?r.map(t=>t.dataKey).flatMap(e=>t.map(t=>({value:(0,nt.kr)(t,e)}))):t.map(t=>({value:t})),n8=(0,f.Mz)([n4,nW,nQ],n3);function n7(t,e){switch(t){case"xAxis":return"x"===e.direction;case"yAxis":return"y"===e.direction;default:return!1}}function n9(t){return t.filter(t=>(0,nr.vh)(t)||t instanceof Date).map(Number).filter(t=>!1===(0,nr.M8)(t))}var it=(t,e,r)=>Object.fromEntries(Object.entries(e.reduce((t,e)=>(null==e.stackId||(null==t[e.stackId]&&(t[e.stackId]=[]),t[e.stackId].push(e)),t),{})).map(e=>{var[n,i]=e,a=i.map(t=>t.dataKey);return[n,{stackedData:(0,nt.yy)(t,a,r),graphicalItems:i}]})),ie=(0,f.Mz)([n4,nQ,nC.eC],it),ir=(t,e,r)=>{var{dataStartIndex:n,dataEndIndex:i}=e;if("zAxis"!==r){var a=(0,nt.Mk)(t,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},ii=(0,f.Mz)([ie,ne.LF,nD],ir),ia=(t,e,r,n)=>r.length>0?t.flatMap(t=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(t=>n7(n,t)),l=(0,nt.kr)(t,null!=(a=e.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(t,e,r){return!r||"number"!=typeof e||(0,nr.M8)(e)||!r.length?[]:n9(r.flatMap(r=>{var n,i,a=(0,nt.kr)(t,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,(0,nn.H)(n)&&(0,nn.H)(i))return[e-n,e+i]}))}(t,l,o)}})).filter(Boolean):(null==e?void 0:e.dataKey)!=null?t.map(t=>({value:(0,nt.kr)(t,e.dataKey),errorDomain:[]})):t.map(t=>({value:t,errorDomain:[]})),io=(0,f.Mz)(n4,nW,n1,nD,ia);function il(t){var{value:e}=t;if((0,nr.vh)(e)||e instanceof Date)return e}var iu=t=>{var e=n9(t.flatMap(t=>[t.value,t.errorDomain]).flat(1));if(0!==e.length)return[Math.min(...e),Math.max(...e)]},ic=(t,e,r)=>{var n=t.map(il).filter(t=>null!=t);return r&&(null==e.dataKey||e.allowDuplicatedCategory&&(0,nr.CG)(n))?d()(0,t.length):e.allowDuplicatedCategory?n:Array.from(new Set(n))},is=t=>{var e;if(null==t||!("domain"in t))return nU;if(null!=t.domain)return t.domain;if(null!=t.ticks){if("number"===t.type){var r=n9(t.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===t.type)return t.ticks.map(String)}return null!=(e=null==t?void 0:t.domain)?e:nU},ih=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},id=t=>t.referenceElements.dots,ip=(t,e,r)=>t.filter(t=>"extendDomain"===t.ifOverflow).filter(t=>"xAxis"===e?t.xAxisId===r:t.yAxisId===r),iy=(0,f.Mz)([id,nD,nN],ip),iv=t=>t.referenceElements.areas,ig=(0,f.Mz)([iv,nD,nN],ip),im=t=>t.referenceElements.lines,ib=(0,f.Mz)([im,nD,nN],ip),ix=(t,e)=>{var r=n9(t.map(t=>"xAxis"===e?t.x:t.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iw=(0,f.Mz)(iy,nD,ix),iO=(t,e)=>{var r=n9(t.flatMap(t=>["xAxis"===e?t.x1:t.y1,"xAxis"===e?t.x2:t.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ij=(0,f.Mz)([ig,nD],iO),iP=(t,e)=>{var r=n9(t.map(t=>"xAxis"===e?t.x:t.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iE=(0,f.Mz)(ib,nD,iP),iA=(0,f.Mz)(iw,iE,ij,(t,e,r)=>ih(t,r,e)),iM=(0,f.Mz)([nW],is),iS=(t,e,r,n,i)=>{var a=function(t,e){if(e&&"function"!=typeof t&&Array.isArray(t)&&2===t.length){var r,n,[i,a]=t;if((0,nn.H)(i))r=i;else if("function"==typeof i)return;if((0,nn.H)(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(ni(o))return o}}(e,t.allowDataOverflow);return null!=a?a:function(t,e,r){if(r||null!=e){if("function"==typeof t&&null!=e)try{var n=t(e,r);if(ni(n))return na(n,e,r)}catch(t){}if(Array.isArray(t)&&2===t.length){var i,a,[o,l]=t;if("auto"===o)null!=e&&(i=Math.min(...e));else if((0,nr.Et)(o))i=o;else if("function"==typeof o)try{null!=e&&(i=o(null==e?void 0:e[0]))}catch(t){}else if("string"==typeof o&&nt.IH.test(o)){var u=nt.IH.exec(o);if(null==u||null==e)i=void 0;else{var c=+u[1];i=e[0]-c}}else i=null==e?void 0:e[0];if("auto"===l)null!=e&&(a=Math.max(...e));else if((0,nr.Et)(l))a=l;else if("function"==typeof l)try{null!=e&&(a=l(null==e?void 0:e[1]))}catch(t){}else if("string"==typeof l&&nt.qx.test(l)){var s=nt.qx.exec(l);if(null==s||null==e)a=void 0;else{var f=+s[1];a=e[1]+f}}else a=null==e?void 0:e[1];var h=[i,a];if(ni(h))return null==e?h:na(h,e,r)}}}(e,ih(r,i,iu(n)),t.allowDataOverflow)},i_=(0,f.Mz)([nW,iM,ii,io,iA],iS),ik=[0,1],iC=(t,e,r,n,i,a,o)=>{if(null!=t&&null!=r&&0!==r.length){var{dataKey:l,type:u}=t,c=(0,nt._L)(e,a);return c&&null==l?d()(0,r.length):"category"===u?ic(n,t,c):"expand"===i?ik:o}},iT=(0,f.Mz)([nW,r9.fz,n4,n8,nC.eC,nD,i_],iC),iD=(t,e,r,n,i)=>{if(null!=t){var{scale:a,type:o}=t;if("auto"===a)return"radial"===e&&"radiusAxis"===i?"band":"radial"===e&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var l="scale".concat((0,nr.Zb)(a));return l in s?l:"point"}}},iN=(0,f.Mz)([nW,r9.fz,nq,nC.iO,nD],iD);function iI(t,e,r,n){if(null!=r&&null!=n){if("function"==typeof t.scale)return t.scale.copy().domain(r).range(n);var i=function(t){if(null!=t){if(t in s)return s[t]();var e="scale".concat((0,nr.Zb)(t));if(e in s)return s[e]()}}(e);if(null!=i){var a=i.domain(r).range(n);return(0,nt.YB)(a),a}}}var iz=(t,e,r)=>{var n=is(e);if("auto"===r||"linear"===r){if(null!=e&&e.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&ni(t))return nE(t,e.tickCount,e.allowDecimals);if(null!=e&&e.tickCount&&"number"===e.type&&ni(t))return nA(t,e.tickCount,e.allowDecimals)}},iL=(0,f.Mz)([iT,nV,iN],iz),iB=(t,e,r,n)=>"angleAxis"!==n&&(null==t?void 0:t.type)==="number"&&ni(e)&&Array.isArray(r)&&r.length>0?[Math.min(e[0],r[0]),Math.max(e[1],r[r.length-1])]:e,iU=(0,f.Mz)([nW,iT,iL,nD],iB),iR=(0,f.Mz)(n8,nW,(t,e)=>{if(e&&"number"===e.type){var r=1/0,n=Array.from(n9(t.map(t=>t.value))).sort((t,e)=>t-e);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),i$=(0,f.Mz)(iR,r9.fz,nC.gY,n_.GO,(t,e,r,n)=>n,(t,e,r,n,i)=>{if(!(0,nn.H)(t))return 0;var a="vertical"===e?n.height:n.width;if("gap"===i)return t*a/2;if("no-gap"===i){var o=(0,nr.F4)(r,t*a),l=t*a/2;return l-o-(l-o)/a*o}return 0}),iF=(0,f.Mz)(n$,(t,e)=>{var r=n$(t,e);return null==r||"string"!=typeof r.padding?0:i$(t,"xAxis",e,r.padding)},(t,e)=>{if(null==t)return{left:0,right:0};var r,n,{padding:i}=t;return"string"==typeof i?{left:e,right:e}:{left:(null!=(r=i.left)?r:0)+e,right:(null!=(n=i.right)?n:0)+e}}),iK=(0,f.Mz)(nK,(t,e)=>{var r=nK(t,e);return null==r||"string"!=typeof r.padding?0:i$(t,"yAxis",e,r.padding)},(t,e)=>{if(null==t)return{top:0,bottom:0};var r,n,{padding:i}=t;return"string"==typeof i?{top:e,bottom:e}:{top:(null!=(r=i.top)?r:0)+e,bottom:(null!=(n=i.bottom)?n:0)+e}}),iG=(0,f.Mz)([n_.GO,iF,nk.U,nk.C,(t,e,r)=>r],(t,e,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[t.left+e.left,t.left+t.width-e.right]}),iH=(0,f.Mz)([n_.GO,r9.fz,iK,nk.U,nk.C,(t,e,r)=>r],(t,e,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===e?[t.top+t.height-r.bottom,t.top+r.top]:[t.top+r.top,t.top+t.height-r.bottom]}),iW=(t,e,r,n)=>{var i;switch(e){case"xAxis":return iG(t,r,n);case"yAxis":return iH(t,r,n);case"zAxis":return null==(i=nH(t,r))?void 0:i.range;case"angleAxis":return(0,nT.Cv)(t);case"radiusAxis":return(0,nT.Dc)(t,r);default:return}},iV=(0,f.Mz)([nW,iW],nI.I),iq=(0,f.Mz)([nW,iN,iU,iV],iI);function iZ(t,e){return t.id<e.id?-1:+(t.id>e.id)}(0,f.Mz)(nQ,nD,(t,e)=>t.flatMap(t=>{var e;return null!=(e=t.errorBars)?e:[]}).filter(t=>n7(e,t)));var iY=(t,e)=>e,iX=(t,e,r)=>r,iJ=(0,f.Mz)(nS.h,iY,iX,(t,e,r)=>t.filter(t=>t.orientation===e).filter(t=>t.mirror===r).sort(iZ)),iQ=(0,f.Mz)(nS.W,iY,iX,(t,e,r)=>t.filter(t=>t.orientation===e).filter(t=>t.mirror===r).sort(iZ)),i0=(t,e)=>({width:t.width,height:e.height}),i1=(t,e)=>({width:"number"==typeof e.width?e.width:nz.tQ,height:t.height}),i2=(0,f.Mz)(n_.GO,n$,i0),i5=(t,e,r)=>{switch(e){case"top":return t.top;case"bottom":return r-t.bottom;default:return 0}},i6=(t,e,r)=>{switch(e){case"left":return t.left;case"right":return r-t.right;default:return 0}},i4=(0,f.Mz)(nM.A$,n_.GO,iJ,iY,iX,(t,e,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=i0(e,r);null==a&&(a=i5(e,n,t));var u="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(u)*l.height,a+=(u?-1:1)*l.height}),o}),i3=(0,f.Mz)(nM.Lp,n_.GO,iQ,iY,iX,(t,e,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=i1(e,r);null==a&&(a=i6(e,n,t));var u="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(u)*l.width,a+=(u?-1:1)*l.width}),o}),i8=(t,e)=>{var r=(0,n_.GO)(t),n=n$(t,e);if(null!=n){var i=i4(t,n.orientation,n.mirror)[e];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},i7=(t,e)=>{var r=(0,n_.GO)(t),n=nK(t,e);if(null!=n){var i=i3(t,n.orientation,n.mirror)[e];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},i9=(0,f.Mz)(n_.GO,nK,(t,e)=>({width:"number"==typeof e.width?e.width:nz.tQ,height:t.height})),at=(t,e,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=(0,nt._L)(t,n),u=e.map(t=>t.value);if(o&&l&&"category"===a&&i&&(0,nr.CG)(u))return u}},ae=(0,f.Mz)([r9.fz,n8,nW,nD],at),ar=(t,e,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if((0,nt._L)(t,n)&&("number"===i||"auto"!==a))return e.map(t=>t.value)}},an=(0,f.Mz)([r9.fz,n8,nV,nD],ar),ai=(0,f.Mz)([r9.fz,(t,e,r)=>{switch(e){case"xAxis":return n$(t,r);case"yAxis":return nK(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},iN,iq,ae,an,iW,iL,nD],(t,e,r,n,i,a,o,l,u)=>{if(null==e)return null;var c=(0,nt._L)(t,u);return{angle:e.angle,interval:e.interval,minTickGap:e.minTickGap,orientation:e.orientation,tick:e.tick,tickCount:e.tickCount,tickFormatter:e.tickFormatter,ticks:e.ticks,type:e.type,unit:e.unit,axisType:u,categoricalDomain:a,duplicateDomain:i,isCategorical:c,niceTicks:l,range:o,realScaleType:r,scale:n}}),aa=(0,f.Mz)([r9.fz,nV,iN,iq,iL,iW,ae,an,nD],(t,e,r,n,i,a,o,l,u)=>{if(null!=e&&null!=n){var c=(0,nt._L)(t,u),{type:s,ticks:f,tickCount:h}=e,d="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/d:0;p="angleAxis"===u&&null!=a&&a.length>=2?2*(0,nr.sA)(a[0]-a[1])*p:p;var y=f||i;return y?y.map((t,e)=>({index:e,coordinate:n(o?o.indexOf(t):t)+p,value:t,offset:p})).filter(t=>!(0,nr.M8)(t.coordinate)):c&&l?l.map((t,e)=>({coordinate:n(t)+p,value:t,index:e,offset:p})):n.ticks?n.ticks(h).map(t=>({coordinate:n(t)+p,value:t,offset:p})):n.domain().map((t,e)=>({coordinate:n(t)+p,value:o?o[t]:t,index:e,offset:p}))}}),ao=(0,f.Mz)([r9.fz,nV,iq,iW,ae,an,nD],(t,e,r,n,i,a,o)=>{if(null!=e&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,nt._L)(t,o),{tickCount:u}=e,c=0;return(c="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*(0,nr.sA)(n[0]-n[1])*c:c,l&&a)?a.map((t,e)=>({coordinate:r(t)+c,value:t,index:e,offset:c})):r.ticks?r.ticks(u).map(t=>({coordinate:r(t)+c,value:t,offset:c})):r.domain().map((t,e)=>({coordinate:r(t)+c,value:i?i[t]:t,index:e,offset:c}))}}),al=(0,f.Mz)(nW,iq,(t,e)=>{if(null!=t&&null!=e)return nB(nB({},t),{},{scale:e})}),au=(0,f.Mz)([nW,iN,iT,iV],iI);(0,f.Mz)((t,e,r)=>nH(t,r),au,(t,e)=>{if(null!=t&&null!=e)return nB(nB({},t),{},{scale:e})});var ac=(0,f.Mz)([r9.fz,nS.h,nS.W],(t,e,r)=>{switch(t){case"horizontal":return e.some(t=>t.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(t=>t.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},2188:(t,e,r)=>{t.exports=r(5252).isEqual},2194:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(921);e.property=function(t){return function(e){return n.get(e,t)}}},2248:(t,e,r)=>{"use strict";r.d(e,{Vi:()=>c,g5:()=>u,iZ:()=>h});var n=r(5710),i=r(4532),a=(0,n.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(t){t.countOfBars+=1},removeBar(t){t.countOfBars-=1},addCartesianGraphicalItem(t,e){t.cartesianItems.push((0,i.h4)(e.payload))},removeCartesianGraphicalItem(t,e){var r=(0,i.ss)(t).cartesianItems.indexOf((0,i.h4)(e.payload));r>-1&&t.cartesianItems.splice(r,1)},addPolarGraphicalItem(t,e){t.polarItems.push((0,i.h4)(e.payload))},removePolarGraphicalItem(t,e){var r=(0,i.ss)(t).polarItems.indexOf((0,i.h4)(e.payload));r>-1&&t.polarItems.splice(r,1)}}}),{addBar:o,removeBar:l,addCartesianGraphicalItem:u,removeCartesianGraphicalItem:c,addPolarGraphicalItem:s,removePolarGraphicalItem:f}=a.actions,h=a.reducer},2348:(t,e,r)=>{"use strict";r.d(e,{W:()=>u});var n=r(2115),i=r(2596),a=r(788),o=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var u=n.forwardRef((t,e)=>{var{children:r,className:u}=t,c=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,o),s=(0,i.$)("recharts-layer",u);return n.createElement("g",l({className:s},(0,a.J9)(c,!0),{ref:e}),r)})},2384:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isObject=function(t){return null!==t&&("object"==typeof t||"function"==typeof t)}},2429:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(4117);e.cloneDeep=function(t){return n.cloneDeepWithImpl(t,void 0,t,new Map,void 0)}},2434:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(7064),i=r(5998),a=r(4373);e.sortBy=function(t,...e){let r=e.length;return r>1&&a.isIterateeCall(t,e[0],e[1])?e=[]:r>2&&a.isIterateeCall(e[0],e[1],e[2])&&(e=[e[0]]),n.orderBy(t,i.flatten(e),["asc"])}},2436:(t,e,r)=>{"use strict";var n=r(2115),i="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,u=n.useDebugValue;function c(t){var e=t.getSnapshot;t=t.value;try{var r=e();return!i(t,r)}catch(t){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var r=e(),n=a({inst:{value:r,getSnapshot:e}}),i=n[0].inst,s=n[1];return l(function(){i.value=r,i.getSnapshot=e,c(i)&&s({inst:i})},[t,r,e]),o(function(){return c(i)&&s({inst:i}),t(function(){c(i)&&s({inst:i})})},[t]),u(r),r};e.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},2465:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.identity=function(t){return t}},2520:(t,e,r)=>{"use strict";var n=r(9641).Buffer;Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let i=r(1147),a=r(8221),o=r(5160),l=r(2721),u=r(3616);e.isEqualWith=function(t,e,r){return function t(e,r,c,s,f,h,d){let p=d(e,r,c,s,f,h);if(void 0!==p)return p;if(typeof e==typeof r)switch(typeof e){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return e===r;case"number":return e===r||Object.is(e,r)}return function e(r,c,s,f){if(Object.is(r,c))return!0;let h=o.getTag(r),d=o.getTag(c);if(h===l.argumentsTag&&(h=l.objectTag),d===l.argumentsTag&&(d=l.objectTag),h!==d)return!1;switch(h){case l.stringTag:return r.toString()===c.toString();case l.numberTag:{let t=r.valueOf(),e=c.valueOf();return u.eq(t,e)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),c.valueOf());case l.regexpTag:return r.source===c.source&&r.flags===c.flags;case l.functionTag:return r===c}let p=(s=s??new Map).get(r),y=s.get(c);if(null!=p&&null!=y)return p===c;s.set(r,c),s.set(c,r);try{switch(h){case l.mapTag:if(r.size!==c.size)return!1;for(let[e,n]of r.entries())if(!c.has(e)||!t(n,c.get(e),e,r,c,s,f))return!1;return!0;case l.setTag:{if(r.size!==c.size)return!1;let e=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<e.length;i++){let a=e[i],o=n.findIndex(e=>t(a,e,void 0,r,c,s,f));if(-1===o)return!1;n.splice(o,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(c)||r.length!==c.length)return!1;for(let e=0;e<r.length;e++)if(!t(r[e],c[e],e,r,c,s,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return e(new Uint8Array(r),new Uint8Array(c),s,f);case l.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return e(new Uint8Array(r),new Uint8Array(c),s,f);case l.errorTag:return r.name===c.name&&r.message===c.message;case l.objectTag:{if(!(e(r.constructor,c.constructor,s,f)||i.isPlainObject(r)&&i.isPlainObject(c)))return!1;let n=[...Object.keys(r),...a.getSymbols(r)],o=[...Object.keys(c),...a.getSymbols(c)];if(n.length!==o.length)return!1;for(let e=0;e<n.length;e++){let i=n[e],a=r[i];if(!Object.hasOwn(c,i))return!1;let o=c[i];if(!t(a,o,i,r,c,s,f))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(c)}}(e,r,h,d)}(t,e,void 0,void 0,void 0,void 0,r)}},2589:(t,e,r)=>{"use strict";r.d(e,{A$:()=>i,HK:()=>o,Lp:()=>n,et:()=>a});var n=t=>t.layout.width,i=t=>t.layout.height,a=t=>t.layout.scale,o=t=>t.layout.margin},2634:(t,e,r)=>{"use strict";r.d(e,{CU:()=>s,Lx:()=>u,u3:()=>c});var n=r(5710),i=r(4532),a=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(t,e){t.size.width=e.payload.width,t.size.height=e.payload.height},setLegendSettings(t,e){t.settings.align=e.payload.align,t.settings.layout=e.payload.layout,t.settings.verticalAlign=e.payload.verticalAlign,t.settings.itemSorter=e.payload.itemSorter},addLegendPayload(t,e){t.payload.push((0,i.h4)(e.payload))},removeLegendPayload(t,e){var r=(0,i.ss)(t).payload.indexOf((0,i.h4)(e.payload));r>-1&&t.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:u,removeLegendPayload:c}=a.actions,s=a.reducer},2661:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function a(t,e,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||t,o),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],l]:t._events[u].push(l):(t._events[u]=l,t._eventsCount++),t}function o(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},l.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},l.prototype.emit=function(t,e,n,i,a,o){var l=r?r+t:t;if(!this._events[l])return!1;var u,c,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,i),!0;case 5:return s.fn.call(s.context,e,n,i,a),!0;case 6:return s.fn.call(s.context,e,n,i,a,o),!0}for(c=1,u=Array(f-1);c<f;c++)u[c-1]=arguments[c];s.fn.apply(s.context,u)}else{var h,d=s.length;for(c=0;c<d;c++)switch(s[c].once&&this.removeListener(t,s[c].fn,void 0,!0),f){case 1:s[c].fn.call(s[c].context);break;case 2:s[c].fn.call(s[c].context,e);break;case 3:s[c].fn.call(s[c].context,e,n);break;case 4:s[c].fn.call(s[c].context,e,n,i);break;default:if(!u)for(h=1,u=Array(f-1);h<f;h++)u[h-1]=arguments[h];s[c].fn.apply(s[c].context,u)}}return!0},l.prototype.on=function(t,e,r){return a(this,t,e,r,!1)},l.prototype.once=function(t,e,r){return a(this,t,e,r,!0)},l.prototype.removeListener=function(t,e,n,i){var a=r?r+t:t;if(!this._events[a])return this;if(!e)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==e||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var u=0,c=[],s=l.length;u<s;u++)(l[u].fn!==e||i&&!l[u].once||n&&l[u].context!==n)&&c.push(l[u]);c.length?this._events[a]=1===c.length?c[0]:c:o(this,a)}return this},l.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&o(this,e)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,t.exports=l},2694:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(668);e.toNumber=function(t){return n.isSymbol(t)?NaN:Number(t)}},2721:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.argumentsTag="[object Arguments]",e.arrayBufferTag="[object ArrayBuffer]",e.arrayTag="[object Array]",e.bigInt64ArrayTag="[object BigInt64Array]",e.bigUint64ArrayTag="[object BigUint64Array]",e.booleanTag="[object Boolean]",e.dataViewTag="[object DataView]",e.dateTag="[object Date]",e.errorTag="[object Error]",e.float32ArrayTag="[object Float32Array]",e.float64ArrayTag="[object Float64Array]",e.functionTag="[object Function]",e.int16ArrayTag="[object Int16Array]",e.int32ArrayTag="[object Int32Array]",e.int8ArrayTag="[object Int8Array]",e.mapTag="[object Map]",e.numberTag="[object Number]",e.objectTag="[object Object]",e.regexpTag="[object RegExp]",e.setTag="[object Set]",e.stringTag="[object String]",e.symbolTag="[object Symbol]",e.uint16ArrayTag="[object Uint16Array]",e.uint32ArrayTag="[object Uint32Array]",e.uint8ArrayTag="[object Uint8Array]",e.uint8ClampedArrayTag="[object Uint8ClampedArray]"},2744:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(8132),i=r(2384),a=r(6633),o=r(3616);function l(t,e,r,n){if(e===t)return!0;switch(typeof e){case"object":return function(t,e,r,n){if(null==e)return!0;if(Array.isArray(e))return u(t,e,r,n);if(e instanceof Map){var i=t,o=e,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[t,e]of o.entries())if(!1===l(i.get(t),e,t,i,o,s))return!1;return!0}if(e instanceof Set)return c(t,e,r,n);let f=Object.keys(e);if(null==t)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(e))return n.get(e)===t;n&&n.set(e,t);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(t)&&!(o in t)||void 0===e[o]&&void 0!==t[o]||null===e[o]&&null!==t[o]||!r(t[o],e[o],o,t,e,n))return!1}return!0}finally{n&&n.delete(e)}}(t,e,r,n);case"function":if(Object.keys(e).length>0)return l(t,{...e},r,n);return o.eq(t,e);default:if(!i.isObject(t))return o.eq(t,e);if("string"==typeof e)return""===e;return!0}}function u(t,e,r,n){if(0===e.length)return!0;if(!Array.isArray(t))return!1;let i=new Set;for(let a=0;a<e.length;a++){let o=e[a],l=!1;for(let u=0;u<t.length;u++){if(i.has(u))continue;let c=t[u],s=!1;if(r(c,o,a,t,e,n)&&(s=!0),s){i.add(u),l=!0;break}}if(!l)return!1}return!0}function c(t,e,r,n){return 0===e.size||t instanceof Set&&u([...t],[...e],r,n)}e.isMatchWith=function(t,e,r){return"function"!=typeof r?n.isMatch(t,e):l(t,e,function t(e,n,i,a,o,u){let c=r(e,n,i,a,o,u);return void 0!==c?!!c:l(e,n,t,u)},new Map)},e.isSetMatch=c},2767:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.noop=function(){}},2832:(t,e,r)=>{"use strict";r.d(e,{b:()=>eg});var n=r(2115),i=r(6641);r(1992);var a=Symbol.for("react.forward_ref"),o=Symbol.for("react.memo");function l(t){return t.dependsOnOwnProps?!!t.dependsOnOwnProps:1!==t.length}var u={notify(){},get:()=>[]},c="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,s="undefined"!=typeof navigator&&"ReactNative"===navigator.product,f=c||s?n.useLayoutEffect:n.useEffect;function h(t,e){return t===e?0!==t||0!==e||1/t==1/e:t!=t&&e!=e}var d={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},p={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},y={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},v={[a]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[o]:y};function g(t){return function(t){if("object"==typeof t&&null!==t){let{$$typeof:e}=t;switch(e){case null:switch(t=t.type){case null:case null:case null:case null:case null:return t;default:switch(t=t&&t.$$typeof){case null:case a:case null:case o:case null:return t;default:return e}}case null:return e}}}(t)===o?y:v[t.$$typeof]||d}var m=Object.defineProperty,b=Object.getOwnPropertyNames,x=Object.getOwnPropertySymbols,w=Object.getOwnPropertyDescriptor,O=Object.getPrototypeOf,j=Object.prototype,P=Symbol.for("react-redux-context"),E="undefined"!=typeof globalThis?globalThis:{},A=function(){if(!n.createContext)return{};let t=E[P]??=new Map,e=t.get(n.createContext);return e||(e=n.createContext(null),t.set(n.createContext,e)),e}(),M=function(t){let{children:e,context:r,serverState:i,store:a}=t,o=n.useMemo(()=>{let t=function(t,e){let r,n=u,i=0,a=!1;function o(){s.onStateChange&&s.onStateChange()}function l(){if(i++,!r){let e,i;r=t.subscribe(o),e=null,i=null,n={clear(){e=null,i=null},notify(){let t=e;for(;t;)t.callback(),t=t.next},get(){let t=[],r=e;for(;r;)t.push(r),r=r.next;return t},subscribe(t){let r=!0,n=i={callback:t,next:null,prev:i};return n.prev?n.prev.next=n:e=n,function(){r&&null!==e&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:e=n.next)}}}}}function c(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=u)}let s={addNestedSub:function(t){l();let e=n.subscribe(t),r=!1;return()=>{r||(r=!0,e(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,l())},tryUnsubscribe:function(){a&&(a=!1,c())},getListeners:()=>n};return s}(a);return{store:a,subscription:t,getServerState:i?()=>i:void 0}},[a,i]),l=n.useMemo(()=>a.getState(),[a]);return f(()=>{let{subscription:t}=o;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),l!==a.getState()&&t.notifyNestedSubs(),()=>{t.tryUnsubscribe(),t.onStateChange=void 0}},[o,l]),n.createElement((r||A).Provider,{value:o},e)},S=r(52),_=r(5710),k=r(4890),C=r(4487),T=(0,_.Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(t,e){t.layoutType=e.payload},setChartSize(t,e){t.width=e.payload.width,t.height=e.payload.height},setMargin(t,e){t.margin.top=e.payload.top,t.margin.right=e.payload.right,t.margin.bottom=e.payload.bottom,t.margin.left=e.payload.left},setScale(t,e){t.scale=e.payload}}}),{setMargin:D,setLayout:N,setChartSize:I,setScale:z}=T.actions,L=T.reducer,B=r(8924),U=r(7238),R=r(215),$=r(9449),F=r(4732),K=r(7062),G=(0,B.Mz)([(t,e)=>e,U.fz,K.D0,R.Re,R.gL,R.R4,F.r1,$.GO],F.aX),H=r(6523),W=t=>{var e=t.currentTarget.getBoundingClientRect(),r=e.width/t.currentTarget.offsetWidth,n=e.height/t.currentTarget.offsetHeight;return{chartX:Math.round((t.clientX-e.left)/r),chartY:Math.round((t.clientY-e.top)/n)}},V=(0,_.VP)("mouseClick"),q=(0,_.Nc)();q.startListening({actionCreator:V,effect:(t,e)=>{var r=t.payload,n=G(e.getState(),W(r));(null==n?void 0:n.activeIndex)!=null&&e.dispatch((0,k.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var Z=(0,_.VP)("mouseMove"),Y=(0,_.Nc)();function X(t,e){return e instanceof HTMLElement?"HTMLElement <".concat(e.tagName,' class="').concat(e.className,'">'):e===window?"global.window":e}Y.startListening({actionCreator:Z,effect:(t,e)=>{var r=t.payload,n=e.getState(),i=(0,H.au)(n,n.tooltip.settings.shared),a=G(n,W(r));"axis"===i&&((null==a?void 0:a.activeIndex)!=null?e.dispatch((0,k.Nt)({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):e.dispatch((0,k.xS)()))}});var J=r(5306),Q=r(2248),tt=r(4532),te=(0,_.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(t,e)=>{t.dots.push(e.payload)},removeDot:(t,e)=>{var r=(0,tt.ss)(t).dots.findIndex(t=>t===e.payload);-1!==r&&t.dots.splice(r,1)},addArea:(t,e)=>{t.areas.push(e.payload)},removeArea:(t,e)=>{var r=(0,tt.ss)(t).areas.findIndex(t=>t===e.payload);-1!==r&&t.areas.splice(r,1)},addLine:(t,e)=>{t.lines.push(e.payload)},removeLine:(t,e)=>{var r=(0,tt.ss)(t).lines.findIndex(t=>t===e.payload);-1!==r&&t.lines.splice(r,1)}}}),{addDot:tr,removeDot:tn,addArea:ti,removeArea:ta,addLine:to,removeLine:tl}=te.actions,tu=te.reducer,tc={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},ts=(0,_.Z0)({name:"brush",initialState:tc,reducers:{setBrushSettings:(t,e)=>null==e.payload?tc:e.payload}}),{setBrushSettings:tf}=ts.actions,th=ts.reducer,td=r(2634),tp={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},ty=(0,_.Z0)({name:"rootProps",initialState:tp,reducers:{updateOptions:(t,e)=>{var r;t.accessibilityLayer=e.payload.accessibilityLayer,t.barCategoryGap=e.payload.barCategoryGap,t.barGap=null!=(r=e.payload.barGap)?r:tp.barGap,t.barSize=e.payload.barSize,t.maxBarSize=e.payload.maxBarSize,t.stackOffset=e.payload.stackOffset,t.syncId=e.payload.syncId,t.syncMethod=e.payload.syncMethod,t.className=e.payload.className}}}),tv=ty.reducer,{updateOptions:tg}=ty.actions,tm=(0,_.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(t,e){t.radiusAxis[e.payload.id]=(0,tt.h4)(e.payload)},removeRadiusAxis(t,e){delete t.radiusAxis[e.payload.id]},addAngleAxis(t,e){t.angleAxis[e.payload.id]=(0,tt.h4)(e.payload)},removeAngleAxis(t,e){delete t.angleAxis[e.payload.id]}}}),{addRadiusAxis:tb,removeRadiusAxis:tx,addAngleAxis:tw,removeAngleAxis:tO}=tm.actions,tj=tm.reducer,tP=(0,_.Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(t,e)=>e.payload}}),{updatePolarOptions:tE}=tP.actions,tA=tP.reducer,tM=r(2183),tS=r(841),t_=(0,_.VP)("keyDown"),tk=(0,_.VP)("focus"),tC=(0,_.Nc)();tC.startListening({actionCreator:t_,effect:(t,e)=>{var r=e.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=t.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number((0,tS.P)(n,(0,R.n4)(r))),o=(0,R.R4)(r);if("Enter"===i){var l=(0,F.pg)(r,"axis","hover",String(n.index));e.dispatch((0,k.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:l}));return}var u=a+("ArrowRight"===i?1:-1)*("left-to-right"===(0,tM._y)(r)?1:-1);if(null!=o&&!(u>=o.length)&&!(u<0)){var c=(0,F.pg)(r,"axis","hover",String(u));e.dispatch((0,k.o4)({active:!0,activeIndex:u.toString(),activeDataKey:void 0,activeCoordinate:c}))}}}}}),tC.startListening({actionCreator:tk,effect:(t,e)=>{var r=e.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=(0,F.pg)(r,"axis","hover",String("0"));e.dispatch((0,k.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var tT=(0,_.VP)("externalEvent"),tD=(0,_.Nc)();tD.startListening({actionCreator:tT,effect:(t,e)=>{if(null!=t.payload.handler){var r=e.getState(),n={activeCoordinate:(0,R.eE)(r),activeDataKey:(0,R.Xb)(r),activeIndex:(0,R.A2)(r),activeLabel:(0,R.BZ)(r),activeTooltipIndex:(0,R.A2)(r),isTooltipActive:(0,R.yn)(r)};t.payload.handler(n,t.payload.reactEvent)}}});var tN=r(4421),tI=r(6670),tz=r(5714),tL=(0,B.Mz)([tz.J],t=>t.tooltipItemPayloads),tB=(0,B.Mz)([tL,tI.x,(t,e,r)=>e,(t,e,r)=>r],(t,e,r,n)=>{var i=t.find(t=>t.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return e(a,r)}}),tU=(0,_.VP)("touchMove"),tR=(0,_.Nc)();tR.startListening({actionCreator:tU,effect:(t,e)=>{var r=t.payload,n=e.getState(),i=(0,H.au)(n,n.tooltip.settings.shared);if("axis"===i){var a=G(n,W({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==a?void 0:a.activeIndex)!=null&&e.dispatch((0,k.Nt)({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],u=document.elementFromPoint(l.clientX,l.clientY);if(!u||!u.getAttribute)return;var c=u.getAttribute(tN.F0),s=null!=(o=u.getAttribute(tN.um))?o:void 0,f=tB(e.getState(),c,s);e.dispatch((0,k.RD)({activeDataKey:s,activeIndex:c,activeCoordinate:f}))}}});var t$=(0,S.HY)({brush:th,cartesianAxis:J.CA,chartData:C.LV,graphicalItems:Q.iZ,layout:L,legend:td.CU,options:i.lJ,polarAxis:tj,polarOptions:tA,referenceElements:tu,rootProps:tv,tooltip:k.En}),tF=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,_.U1)({reducer:t$,preloadedState:t,middleware:t=>t({serializableCheck:!1}).concat([q.middleware,Y.middleware,tC.middleware,tD.middleware,tR.middleware]),devTools:{serialize:{replacer:X},name:"recharts-".concat(e)}})},tK=r(1807),tG=r(5064);function tH(t){var{preloadedState:e,children:r,reduxStoreName:i}=t,a=(0,tK.r)(),o=(0,n.useRef)(null);if(a)return r;null==o.current&&(o.current=tF(e,i));var l=tG.E;return n.createElement(M,{context:l,store:o.current},r)}var tW=r(1971),tV=t=>{var{chartData:e}=t,r=(0,tW.j)(),i=(0,tK.r)();return(0,n.useEffect)(()=>i?()=>{}:(r((0,C.hq)(e)),()=>{r((0,C.hq)(void 0))}),[e,r,i]),null};function tq(t){var{layout:e,width:r,height:i,margin:a}=t,o=(0,tW.j)(),l=(0,tK.r)();return(0,n.useEffect)(()=>{l||(o(N(e)),o(I({width:r,height:i})),o(D(a)))},[o,l,e,r,i,a]),null}function tZ(t){var e=(0,tW.j)();return(0,n.useEffect)(()=>{e(tg(t))},[e,t]),null}var tY=r(788),tX=r(6752),tJ=r(2596),tQ=["children","width","height","viewBox","className","style","title","desc"];function t0(){return(t0=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var t1=(0,n.forwardRef)((t,e)=>{var{children:r,width:i,height:a,viewBox:o,className:l,style:u,title:c,desc:s}=t,f=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tQ),h=o||{width:i,height:a,x:0,y:0},d=(0,tJ.$)("recharts-surface",l);return n.createElement("svg",t0({},(0,tY.J9)(f,!0,"svg"),{className:d,width:i,height:a,style:u,viewBox:"".concat(h.x," ").concat(h.y," ").concat(h.width," ").concat(h.height),ref:e}),n.createElement("title",null,c),n.createElement("desc",null,s),r)}),t2=r(972),t5=r(8892),t6=["children"];function t4(){return(t4=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var t3={width:"100%",height:"100%"},t8=(0,n.forwardRef)((t,e)=>{var r,i,a=(0,U.yi)(),o=(0,U.rY)(),l=(0,tX.$)();if(!(0,t5.F)(a)||!(0,t5.F)(o))return null;var{children:u,otherAttributes:c,title:s,desc:f}=t;return r="number"==typeof c.tabIndex?c.tabIndex:l?0:void 0,i="string"==typeof c.role?c.role:l?"application":void 0,n.createElement(t1,t4({},c,{title:s,desc:f,role:i,tabIndex:r,width:a,height:o,style:t3,ref:e}),u)}),t7=t=>{var{children:e}=t,r=(0,tW.G)(t2.U);if(!r)return null;var{width:i,height:a,y:o,x:l}=r;return n.createElement(t1,{width:i,height:a,x:l,y:o},e)},t9=(0,n.forwardRef)((t,e)=>{var{children:r}=t,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,t6);return(0,tK.r)()?n.createElement(t7,null,r):n.createElement(t8,t4({ref:e},i),r)}),et=r(6850),ee=r(2589),er=r(5115),en=(0,n.createContext)(null);function ei(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var ea=(0,n.forwardRef)((t,e)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:l,onDoubleClick:u,onMouseDown:c,onMouseEnter:s,onMouseLeave:f,onMouseMove:h,onMouseUp:d,onTouchEnd:p,onTouchMove:y,onTouchStart:v,style:g,width:m}=t,b=(0,tW.j)(),[x,w]=(0,n.useState)(null),[O,j]=(0,n.useState)(null);(0,et.l3)();var P=function(){var t=(0,tW.j)(),[e,r]=(0,n.useState)(null),i=(0,tW.G)(ee.et);return(0,n.useEffect)(()=>{if(null!=e){var r=e.getBoundingClientRect().width/e.offsetWidth;(0,t5.H)(r)&&r!==i&&t(z(r))}},[e,t,i]),r}(),E=(0,n.useCallback)(t=>{P(t),"function"==typeof e&&e(t),w(t),j(t)},[P,e,w,j]),A=(0,n.useCallback)(t=>{b(V(t)),b(tT({handler:o,reactEvent:t}))},[b,o]),M=(0,n.useCallback)(t=>{b(Z(t)),b(tT({handler:s,reactEvent:t}))},[b,s]),S=(0,n.useCallback)(t=>{b((0,k.xS)()),b(tT({handler:f,reactEvent:t}))},[b,f]),_=(0,n.useCallback)(t=>{b(Z(t)),b(tT({handler:h,reactEvent:t}))},[b,h]),C=(0,n.useCallback)(()=>{b(tk())},[b]),T=(0,n.useCallback)(t=>{b(t_(t.key))},[b]),D=(0,n.useCallback)(t=>{b(tT({handler:l,reactEvent:t}))},[b,l]),N=(0,n.useCallback)(t=>{b(tT({handler:u,reactEvent:t}))},[b,u]),I=(0,n.useCallback)(t=>{b(tT({handler:c,reactEvent:t}))},[b,c]),L=(0,n.useCallback)(t=>{b(tT({handler:d,reactEvent:t}))},[b,d]),B=(0,n.useCallback)(t=>{b(tT({handler:v,reactEvent:t}))},[b,v]),U=(0,n.useCallback)(t=>{b(tU(t)),b(tT({handler:y,reactEvent:t}))},[b,y]),R=(0,n.useCallback)(t=>{b(tT({handler:p,reactEvent:t}))},[b,p]);return n.createElement(er.$.Provider,{value:x},n.createElement(en.Provider,{value:O},n.createElement("div",{className:(0,tJ.$)("recharts-wrapper",i),style:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ei(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ei(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({position:"relative",cursor:"default",width:m,height:a},g),role:"application",onClick:A,onContextMenu:D,onDoubleClick:N,onFocus:C,onKeyDown:T,onMouseDown:I,onMouseEnter:M,onMouseLeave:S,onMouseMove:_,onMouseUp:L,onTouchEnd:R,onTouchMove:U,onTouchStart:B,ref:E},r)))}),eo=r(6377),el=(0,n.createContext)(void 0),eu=t=>{var{children:e}=t,[r]=(0,n.useState)("".concat((0,eo.NF)("recharts"),"-clip")),i=(0,U.hj)();if(null==i)return null;var{left:a,top:o,height:l,width:u}=i;return n.createElement(el.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:a,y:o,height:l,width:u}))),e)},ec=["children","className","width","height","style","compact","title","desc"],es=(0,n.forwardRef)((t,e)=>{var{children:r,className:i,width:a,height:o,style:l,compact:u,title:c,desc:s}=t,f=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,ec),h=(0,tY.J9)(f,!1);return u?n.createElement(t9,{otherAttributes:h,title:c,desc:s},r):n.createElement(ea,{className:i,style:l,width:a,height:o,onClick:t.onClick,onMouseLeave:t.onMouseLeave,onMouseEnter:t.onMouseEnter,onMouseMove:t.onMouseMove,onMouseDown:t.onMouseDown,onMouseUp:t.onMouseUp,onContextMenu:t.onContextMenu,onDoubleClick:t.onDoubleClick,onTouchStart:t.onTouchStart,onTouchMove:t.onTouchMove,onTouchEnd:t.onTouchEnd},n.createElement(t9,{otherAttributes:h,title:c,desc:s,ref:e},n.createElement(eu,null,r)))}),ef=r(3389),eh=["width","height"];function ed(){return(ed=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var ep={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},ey=(0,n.forwardRef)(function(t,e){var r,i=(0,ef.e)(t.categoricalChartProps,ep),{width:a,height:o}=i,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(i,eh);if(!(0,t5.F)(a)||!(0,t5.F)(o))return null;var{chartName:u,defaultTooltipEventType:c,validateTooltipEventTypes:s,tooltipPayloadSearcher:f,categoricalChartProps:h}=t;return n.createElement(tH,{preloadedState:{options:{chartName:u,defaultTooltipEventType:c,validateTooltipEventTypes:s,tooltipPayloadSearcher:f,eventEmitter:void 0}},reduxStoreName:null!=(r=h.id)?r:u},n.createElement(tV,{chartData:h.data}),n.createElement(tq,{width:a,height:o,layout:i.layout,margin:i.margin}),n.createElement(tZ,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(es,ed({},l,{width:a,height:o,ref:e})))}),ev=["axis"],eg=(0,n.forwardRef)((t,e)=>n.createElement(ey,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:ev,tooltipPayloadSearcher:i.uN,categoricalChartProps:t,ref:e}))},2962:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(8673);e.throttle=function(t,e=0,r={}){"object"!=typeof r&&(r={});let{leading:i=!0,trailing:a=!0}=r;return n.debounce(t,e,{leading:i,trailing:a,maxWait:e})}},3109:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3205:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(4545),i=r(8412),a=r(177),o=r(4072);e.has=function(t,e){let r;if(0===(r=Array.isArray(e)?e:"string"==typeof e&&n.isDeepKey(e)&&t?.[e]==null?o.toPath(e):[e]).length)return!1;let l=t;for(let t=0;t<r.length;t++){let e=r[t];if((null==l||!Object.hasOwn(l,e))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(e)&&e<l.length))return!1;l=l[e]}return!0}},3389:(t,e,r)=>{"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t,e){var r=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return Object.keys(e).reduce((t,r)=>(void 0===t[r]&&void 0!==e[r]&&(t[r]=e[r]),t),r)}r.d(e,{e:()=>i})},3540:(t,e,r)=>{"use strict";r.d(e,{u:()=>f});var n=r(2596),i=r(2115),a=r(400),o=r.n(a),l=r(6377),u=r(675);function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var f=(0,i.forwardRef)((t,e)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:c="100%",height:f="100%",minWidth:h=0,minHeight:d,maxHeight:p,children:y,debounce:v=0,id:g,className:m,onResize:b,style:x={}}=t,w=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(e,()=>w.current);var[j,P]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),E=(0,i.useCallback)((t,e)=>{P(r=>{var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var t=t=>{var e,{width:r,height:n}=t[0].contentRect;E(r,n),null==(e=O.current)||e.call(O,r,n)};v>0&&(t=o()(t,v,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),{width:r,height:n}=w.current.getBoundingClientRect();return E(r,n),e.observe(w.current),()=>{e.disconnect()}},[E,v]);var A=(0,i.useMemo)(()=>{var{containerWidth:t,containerHeight:e}=j;if(t<0||e<0)return null;(0,u.R)((0,l._3)(c)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,f),(0,u.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(c)?t:c,a=(0,l._3)(f)?e:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),p&&a>p&&(a=p)),(0,u.R)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,c,f,h,d,r),i.Children.map(y,t=>(0,i.cloneElement)(t,{width:n,height:a,style:s({height:"100%",width:"100%",maxHeight:a,maxWidth:n},t.props.style)}))},[r,y,f,p,d,h,j,c]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.$)("recharts-responsive-container",m),style:s(s({},x),{},{width:c,height:f,minWidth:h,minHeight:d,maxHeight:p}),ref:w},A)})},3597:(t,e,r)=>{"use strict";r.d(e,{QQ:()=>i,VU:()=>o,XC:()=>s,_U:()=>u,j2:()=>l});var n=r(2115),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],u=(t,e)=>{if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(t=>{l.includes(t)&&(i[t]=e||(e=>r[t](r,e)))}),i},c=(t,e,r)=>n=>(t(e,r,n),null),s=(t,e,r)=>{if(null===t||"object"!=typeof t&&"function"!=typeof t)return null;var n=null;return Object.keys(t).forEach(i=>{var a=t[i];l.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=c(a,e,r))}),n}},3616:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.eq=function(t,e){return t===e||Number.isNaN(t)&&Number.isNaN(e)}},3676:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.uniqBy=function(t,e){let r=new Map;for(let n=0;n<t.length;n++){let i=t[n],a=e(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},3778:(t,e,r)=>{"use strict";r.d(e,{m:()=>tf});var n=r(2115),i=r(7650),a=r(241),o=r.n(a),l=r(2596),u=r(6377);function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t){return Array.isArray(t)&&(0,u.vh)(t[0])&&(0,u.vh)(t[1])?t.join(" ~ "):t}var d=t=>{var{separator:e=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:d,itemSorter:p,wrapperClassName:y,labelClassName:v,label:g,labelFormatter:m,accessibilityLayer:b=!1}=t,x=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),w=f({margin:0},a),O=!(0,u.uy)(g),j=O?g:"",P=(0,l.$)("recharts-default-tooltip",y),E=(0,l.$)("recharts-tooltip-label",v);return O&&m&&null!=s&&(j=m(g,s)),n.createElement("div",c({className:P,style:x},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:E,style:w},n.isValidElement(j)?j:"".concat(j)),(()=>{if(s&&s.length){var t=(p?o()(s,p):s).map((t,r)=>{if("none"===t.type)return null;var a=t.formatter||d||h,{value:o,name:l}=t,c=o,p=l;if(a){var y=a(o,l,t,r,s);if(Array.isArray(y))[c,p]=y;else{if(null==y)return null;c=y}}var v=f({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:v},(0,u.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,u.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},e):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null})())},p="recharts-tooltip-wrapper",y={visibility:"hidden"};function v(t){var{allowEscapeViewBox:e,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:c,viewBoxDimension:s}=t;if(a&&(0,u.Et)(a[n]))return a[n];var f=r[n]-l-(i>0?i:0),h=r[n]+i;if(e[n])return o[n]?f:h;var d=c[n];return null==d?0:o[n]?f<d?Math.max(h,d):Math.max(f,d):null==s?0:h+l>d+s?Math.max(f,d):Math.max(h,d)}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){b(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function b(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class x extends n.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var t,e;this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:t,allowEscapeViewBox:e,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:c,isAnimationActive:s,offset:f,position:h,reverseDirection:d,useTranslate3d:g,viewBox:b,wrapperStyle:x,lastBoundingBox:w,innerRef:O,hasPortalFromProps:j}=this.props,{cssClasses:P,cssProperties:E}=function(t){var e,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:c,reverseDirection:s,tooltipBox:f,useTranslate3d:h,viewBox:d}=t;return{cssProperties:e=f.height>0&&f.width>0&&a?function(t){var{translateX:e,translateY:r,useTranslate3d:n}=t;return{transform:n?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:r=v({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:f.width,viewBox:d,viewBoxDimension:d.width}),translateY:n=v({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:f.height,viewBox:d,viewBoxDimension:d.height}),useTranslate3d:h}):y,cssClasses:function(t){var{coordinate:e,translateX:r,translateY:n}=t;return(0,l.$)(p,{["".concat(p,"-right")]:(0,u.Et)(r)&&e&&(0,u.Et)(e.x)&&r>=e.x,["".concat(p,"-left")]:(0,u.Et)(r)&&e&&(0,u.Et)(e.x)&&r<e.x,["".concat(p,"-bottom")]:(0,u.Et)(n)&&e&&(0,u.Et)(e.y)&&n>=e.y,["".concat(p,"-top")]:(0,u.Et)(n)&&e&&(0,u.Et)(e.y)&&n<e.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:e,coordinate:o,offsetTopLeft:f,position:h,reverseDirection:d,tooltipBox:{height:w.height,width:w.width},useTranslate3d:g,viewBox:b}),A=j?{}:m(m({transition:s&&t?"transform ".concat(r,"ms ").concat(i):void 0},E),{},{pointerEvents:"none",visibility:!this.state.dismissed&&t&&c?"visible":"hidden",position:"absolute",top:0,left:0}),M=m(m({},A),{},{visibility:!this.state.dismissed&&t&&c?"visible":"hidden"},x);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:P,style:M,ref:O},a)}constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",t=>{if("Escape"===t.key){var e,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(e=null==(r=this.props.coordinate)?void 0:r.x)?e:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}}var w=r(1643),O=r(512),j=r.n(O),P=r(7238),E=r(6752),A=r(4679),M=r(788),S=["x","y","top","left","width","height","className"];function _(){return(_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var C=(t,e,r,n,i,a)=>"M".concat(t,",").concat(i,"v").concat(n,"M").concat(a,",").concat(e,"h").concat(r),T=t=>{var{x:e=0,y:r=0,top:i=0,left:a=0,width:o=0,height:c=0,className:s}=t,f=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:e,y:r,top:i,left:a,width:o,height:c},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,S));return(0,u.Et)(e)&&(0,u.Et)(r)&&(0,u.Et)(o)&&(0,u.Et)(c)&&(0,u.Et)(i)&&(0,u.Et)(a)?n.createElement("path",_({},(0,M.J9)(f,!0),{className:(0,l.$)("recharts-cross",s),d:C(e,r,o,c,i,a)})):null},D=r(3389),N=r(4460);function I(){return(I=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var z=(t,e,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,u=r>=0?1:-1,c=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(t,",").concat(e+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(c,",").concat(t+u*s[0],",").concat(e)),a+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(c,",\n        ").concat(t+r,",").concat(e+l*s[1])),a+="L ".concat(t+r,",").concat(e+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(c,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),a+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(c,",\n        ").concat(t,",").concat(e+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var h=Math.min(o,i);a="M ".concat(t,",").concat(e+l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(t+u*h,",").concat(e,"\n            L ").concat(t+r-u*h,",").concat(e,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(t+r,",").concat(e+l*h,"\n            L ").concat(t+r,",").concat(e+n-l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(t+r-u*h,",").concat(e+n,"\n            L ").concat(t+u*h,",").concat(e+n,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(t,",").concat(e+n-l*h," Z")}else a="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},L={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},B=t=>{var e=(0,D.e)(t,L),r=(0,n.useRef)(null),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&a(t)}catch(t){}},[]);var{x:o,y:u,width:c,height:s,radius:f,className:h}=e,{animationEasing:d,animationDuration:p,animationBegin:y,isAnimationActive:v,isUpdateAnimationActive:g}=e;if(o!==+o||u!==+u||c!==+c||s!==+s||0===c||0===s)return null;var m=(0,l.$)("recharts-rectangle",h);return g?n.createElement(N.i,{canBegin:i>0,from:{width:c,height:s,x:o,y:u},to:{width:c,height:s,x:o,y:u},duration:p,animationEasing:d,isActive:g},t=>{var{width:a,height:o,x:l,y:u}=t;return n.createElement(N.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:p,isActive:v,easing:d},n.createElement("path",I({},(0,M.J9)(e,!0),{className:m,d:z(l,u,a,o,f),ref:r})))}):n.createElement("path",I({},(0,M.J9)(e,!0),{className:m,d:z(o,u,c,s,f)}))},U=r(5641);function R(t){var{cx:e,cy:r,radius:n,startAngle:i,endAngle:a}=t;return{points:[(0,U.IZ)(e,r,n,i),(0,U.IZ)(e,r,n,a)],cx:e,cy:r,radius:n,startAngle:i,endAngle:a}}function $(){return($=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var F=(t,e)=>(0,u.sA)(e-t)*Math.min(Math.abs(e-t),359.999),K=t=>{var{cx:e,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:l,cornerIsExternal:u}=t,c=l*(o?1:-1)+n,s=Math.asin(l/c)/U.Kg,f=u?i:i+a*s,h=(0,U.IZ)(e,r,c,f);return{center:h,circleTangency:(0,U.IZ)(e,r,n,f),lineTangency:(0,U.IZ)(e,r,c*Math.cos(s*U.Kg),u?i-a*s:i),theta:s}},G=t=>{var{cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=t,l=F(a,o),u=a+l,c=(0,U.IZ)(e,r,i,a),s=(0,U.IZ)(e,r,i,u),f="M ".concat(c.x,",").concat(c.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var h=(0,U.IZ)(e,r,n,a),d=(0,U.IZ)(e,r,n,u);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=u),",\n            ").concat(h.x,",").concat(h.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},H=t=>{var{cx:e,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:c,endAngle:s}=t,f=(0,u.sA)(s-c),{circleTangency:h,lineTangency:d,theta:p}=K({cx:e,cy:r,radius:i,angle:c,sign:f,cornerRadius:a,cornerIsExternal:l}),{circleTangency:y,lineTangency:v,theta:g}=K({cx:e,cy:r,radius:i,angle:s,sign:-f,cornerRadius:a,cornerIsExternal:l}),m=l?Math.abs(c-s):Math.abs(c-s)-p-g;if(m<0)return o?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):G({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:s});var b="M ".concat(d.x,",").concat(d.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(m>180),",").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(v.x,",").concat(v.y,"\n  ");if(n>0){var{circleTangency:x,lineTangency:w,theta:O}=K({cx:e,cy:r,radius:n,angle:c,sign:f,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:j,lineTangency:P,theta:E}=K({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),A=l?Math.abs(c-s):Math.abs(c-s)-O-E;if(A<0&&0===a)return"".concat(b,"L").concat(e,",").concat(r,"Z");b+="L".concat(P.x,",").concat(P.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(j.x,",").concat(j.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(A>180),",").concat(+(f>0),",").concat(x.x,",").concat(x.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(w.x,",").concat(w.y,"Z")}else b+="L".concat(e,",").concat(r,"Z");return b},W={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},V=t=>{var e,r=(0,D.e)(t,W),{cx:i,cy:a,innerRadius:o,outerRadius:c,cornerRadius:s,forceCornerRadius:f,cornerIsExternal:h,startAngle:d,endAngle:p,className:y}=r;if(c<o||d===p)return null;var v=(0,l.$)("recharts-sector",y),g=c-o,m=(0,u.F4)(s,g,0,!0);return e=m>0&&360>Math.abs(d-p)?H({cx:i,cy:a,innerRadius:o,outerRadius:c,cornerRadius:Math.min(m,g/2),forceCornerRadius:f,cornerIsExternal:h,startAngle:d,endAngle:p}):G({cx:i,cy:a,innerRadius:o,outerRadius:c,startAngle:d,endAngle:p}),n.createElement("path",$({},(0,M.J9)(r,!0),{className:v,d:e}))},q=r(1420),Z=r(4732);function Y(){return(Y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function X(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function J(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?X(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):X(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Q(t){var e,r,i,{coordinate:a,payload:o,index:u,offset:c,tooltipAxisBandSize:s,layout:f,cursor:h,tooltipEventType:d,chartName:p}=t;if(!h||!a||"ScatterChart"!==p&&"axis"!==d)return null;if("ScatterChart"===p)r=a,i=T;else if("BarChart"===p)e=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?a.x-e:c.left+.5,y:"horizontal"===f?c.top+.5:a.y-e,width:"horizontal"===f?s:c.width-1,height:"horizontal"===f?c.height-1:s},i=B;else if("radial"===f){var{cx:y,cy:v,radius:g,startAngle:m,endAngle:b}=R(a);r={cx:y,cy:v,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},i=V}else r={points:function(t,e,r){var n,i,a,o;if("horizontal"===t)a=n=e.x,i=r.top,o=r.top+r.height;else if("vertical"===t)o=i=e.y,n=r.left,a=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return R(e);else{var{cx:l,cy:u,innerRadius:c,outerRadius:s,angle:f}=e,h=(0,U.IZ)(l,u,c,f),d=(0,U.IZ)(l,u,s,f);n=h.x,i=h.y,a=d.x,o=d.y}return[{x:n,y:i},{x:a,y:o}]}(f,a,c)},i=A.I;var x="object"==typeof h&&"className"in h?h.className:void 0,w=J(J(J(J({stroke:"#ccc",pointerEvents:"none"},c),r),(0,M.J9)(h,!1)),{},{payload:o,payloadIndex:u,className:(0,l.$)("recharts-tooltip-cursor",x)});return(0,n.isValidElement)(h)?(0,n.cloneElement)(h,w):(0,n.createElement)(i,w)}function tt(t){var e=(0,q.O)(),r=(0,P.hj)(),i=(0,P.WX)(),a=(0,Z.fW)();return n.createElement(Q,Y({},t,{coordinate:t.coordinate,index:t.index,payload:t.payload,offset:r,layout:i,tooltipAxisBandSize:e,chartName:a}))}var te=r(5115),tr=r(1971),tn=r(4890),ti=r(6850),ta=r(6523);function to(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?to(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):to(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tu(t){return t.dataKey}var tc=[],ts={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!w.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function tf(t){var e,r,a=(0,D.e)(t,ts),{active:o,allowEscapeViewBox:l,animationDuration:u,animationEasing:c,content:s,filterNull:f,isAnimationActive:h,offset:p,payloadUniqBy:y,position:v,reverseDirection:g,useTranslate3d:m,wrapperStyle:b,cursor:w,shared:O,trigger:A,defaultIndex:M,portal:S,axisId:_}=a,k=(0,tr.j)(),C="number"==typeof M?String(M):M;(0,n.useEffect)(()=>{k((0,tn.UF)({shared:O,trigger:A,axisId:_,active:o,defaultIndex:C}))},[k,O,A,_,o,C]);var T=(0,P.sk)(),N=(0,E.$)(),I=(0,ta.Td)(O),{activeIndex:z,isActive:L}=(0,tr.G)(t=>(0,Z.yn)(t,I,A,C)),B=(0,tr.G)(t=>(0,Z.u9)(t,I,A,C)),U=(0,tr.G)(t=>(0,Z.BZ)(t,I,A,C)),R=(0,tr.G)(t=>(0,Z.dS)(t,I,A,C)),$=(0,te.X)(),F=null!=o?o:L,[K,G]=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[e,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(t=>{if(null!=t){var n=t.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-e.height)>1||Math.abs(i.left-e.left)>1||Math.abs(i.top-e.top)>1||Math.abs(i.width-e.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[e.width,e.height,e.top,e.left,...t]);return[e,i]}([B,F]),H="axis"===I?U:void 0;(0,ti.m7)(I,A,R,H,z,F);var W=null!=S?S:$;if(null==W)return null;var V=null!=B?B:tc;F||(V=tc),f&&V.length&&(e=B.filter(t=>null!=t.value&&(!0!==t.hide||a.includeHidden)),V=!0===y?j()(e,tu):"function"==typeof y?j()(e,y):e);var q=V.length>0,Y=n.createElement(x,{allowEscapeViewBox:l,animationDuration:u,animationEasing:c,isAnimationActive:h,active:F,coordinate:R,hasPayload:q,offset:p,position:v,reverseDirection:g,useTranslate3d:m,viewBox:T,wrapperStyle:b,lastBoundingBox:K,innerRef:G,hasPortalFromProps:!!S},(r=tl(tl({},a),{},{payload:V,label:H,active:F,coordinate:R,accessibilityLayer:N}),n.isValidElement(s)?n.cloneElement(s,r):"function"==typeof s?n.createElement(s,r):n.createElement(d,r)));return n.createElement(n.Fragment,null,(0,i.createPortal)(Y,W),F&&n.createElement(tt,{cursor:w,tooltipEventType:I,coordinate:R,payload:B,index:z}))}},3949:(t,e,r)=>{t.exports=r(9901).range},4072:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toPath=function(t){let e=[],r=t.length;if(0===r)return e;let n=0,i="",a="",o=!1;for(46===t.charCodeAt(0)&&(e.push(""),n++);n<r;){let l=t[n];a?"\\"===l&&n+1<r?i+=t[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,e.push(i),i=""):i+=l:"["===l?(o=!0,i&&(e.push(i),i="")):"."===l?i&&(e.push(i),i=""):i+=l,n++}return i&&e.push(i),e}},4117:(t,e,r)=>{"use strict";var n=r(9641).Buffer;Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let i=r(8221),a=r(5160),o=r(2721),l=r(6633),u=r(885);function c(t,e,r,i=new Map,f){let h=f?.(t,e,r,i);if(null!=h)return h;if(l.isPrimitive(t))return t;if(i.has(t))return i.get(t);if(Array.isArray(t)){let e=Array(t.length);i.set(t,e);for(let n=0;n<t.length;n++)e[n]=c(t[n],n,r,i,f);return Object.hasOwn(t,"index")&&(e.index=t.index),Object.hasOwn(t,"input")&&(e.input=t.input),e}if(t instanceof Date)return new Date(t.getTime());if(t instanceof RegExp){let e=new RegExp(t.source,t.flags);return e.lastIndex=t.lastIndex,e}if(t instanceof Map){let e=new Map;for(let[n,a]of(i.set(t,e),t))e.set(n,c(a,n,r,i,f));return e}if(t instanceof Set){let e=new Set;for(let n of(i.set(t,e),t))e.add(c(n,void 0,r,i,f));return e}if(void 0!==n&&n.isBuffer(t))return t.subarray();if(u.isTypedArray(t)){let e=new(Object.getPrototypeOf(t)).constructor(t.length);i.set(t,e);for(let n=0;n<t.length;n++)e[n]=c(t[n],n,r,i,f);return e}if(t instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&t instanceof SharedArrayBuffer)return t.slice(0);if(t instanceof DataView){let e=new DataView(t.buffer.slice(0),t.byteOffset,t.byteLength);return i.set(t,e),s(e,t,r,i,f),e}if("undefined"!=typeof File&&t instanceof File){let e=new File([t],t.name,{type:t.type});return i.set(t,e),s(e,t,r,i,f),e}if(t instanceof Blob){let e=new Blob([t],{type:t.type});return i.set(t,e),s(e,t,r,i,f),e}if(t instanceof Error){let e=new t.constructor;return i.set(t,e),e.message=t.message,e.name=t.name,e.stack=t.stack,e.cause=t.cause,s(e,t,r,i,f),e}if("object"==typeof t&&function(t){switch(a.getTag(t)){case o.argumentsTag:case o.arrayTag:case o.arrayBufferTag:case o.dataViewTag:case o.booleanTag:case o.dateTag:case o.float32ArrayTag:case o.float64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.mapTag:case o.numberTag:case o.objectTag:case o.regexpTag:case o.setTag:case o.stringTag:case o.symbolTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:return!0;default:return!1}}(t)){let e=Object.create(Object.getPrototypeOf(t));return i.set(t,e),s(e,t,r,i,f),e}return t}function s(t,e,r=t,n,a){let o=[...Object.keys(e),...i.getSymbols(e)];for(let i=0;i<o.length;i++){let l=o[i],u=Object.getOwnPropertyDescriptor(t,l);(null==u||u.writable)&&(t[l]=c(e[l],l,r,n,a))}}e.cloneDeepWith=function(t,e){return c(t,void 0,t,new Map,e)},e.cloneDeepWithImpl=c,e.copyProperties=s},4186:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4373:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(8412),i=r(8179),a=r(2384),o=r(3616);e.isIterateeCall=function(t,e,r){return!!a.isObject(r)&&(!!("number"==typeof e&&i.isArrayLike(r)&&n.isIndex(e))&&e<r.length||"string"==typeof e&&e in r)&&o.eq(r[e],t)}},4421:(t,e,r)=>{"use strict";r.d(e,{F0:()=>n,tQ:()=>a,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},4460:(t,e,r)=>{"use strict";r.d(e,{i:()=>D});var n=r(2115),i=r(2188),a=r.n(i),o=(t,e)=>[0,3*t,3*e-6*t,3*t-3*e+1],l=(t,e)=>t.map((t,r)=>t*e**r).reduce((t,e)=>t+e),u=(t,e)=>r=>l(o(t,e),r),c=(t,e)=>r=>l([...o(t,e).map((t,e)=>t*e).slice(1),0],r),s=function(){for(var t,e,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[t,r,e,n]=[0,0,1,1];break;case"ease":[t,r,e,n]=[.25,.1,.25,1];break;case"ease-in":[t,r,e,n]=[.42,0,1,1];break;case"ease-out":[t,r,e,n]=[.42,0,.58,1];break;case"ease-in-out":[t,r,e,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([t,r,e,n]=l[1].split(")")[0].split(",").map(t=>parseFloat(t)))}else 4===a.length&&([t,r,e,n]=a);var s=u(t,e),f=u(r,n),h=c(t,e),d=t=>t>1?1:t<0?0:t,p=t=>{for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i=s(r)-e,a=h(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=d(r-i/a)}return f(r)};return p.isStepper=!1,p},f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:e=100,damping:r=8,dt:n=17}=t,i=(t,i,a)=>{var o=a+(-(t-i)*e-a*r)*n/1e3,l=a*n/1e3+t;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},h=t=>{if("string"==typeof t)switch(t){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return s(t);case"spring":return f();default:if("cubic-bezier"===t.split("(")[0])return s(t)}return"function"==typeof t?t:null};function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var y=t=>t.replace(/([A-Z])/g,t=>"-".concat(t.toLowerCase())),v=(t,e,r)=>t.map(t=>"".concat(y(t)," ").concat(e,"ms ").concat(r)).join(","),g=(t,e)=>[Object.keys(t),Object.keys(e)].reduce((t,e)=>t.filter(t=>e.includes(t))),m=(t,e)=>Object.keys(e).reduce((r,n)=>p(p({},r),{},{[n]:t(n,e[n])}),{});function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var w=(t,e,r)=>t+(e-t)*r,O=t=>{var{from:e,to:r}=t;return e!==r},j=(t,e,r)=>{var n=m((e,r)=>{if(O(r)){var[n,i]=t(r.from,r.to,r.velocity);return x(x({},r),{},{from:n,velocity:i})}return r},e);return r<1?m((t,e)=>O(e)?x(x({},e),{},{velocity:w(e.velocity,n[t].velocity,r),from:w(e.from,n[t].from,r)}):e,e):j(t,n,r-1)};let P=(t,e,r,n,i,a)=>{var o=g(t,e);return!0===r.isStepper?function(t,e,r,n,i,a){var o,l=n.reduce((r,n)=>x(x({},r),{},{[n]:{from:t[n],velocity:0,to:e[n]}}),{}),u=()=>m((t,e)=>e.from,l),c=()=>!Object.values(l).filter(O).length,s=null,f=n=>{o||(o=n);var h=(n-o)/r.dt;l=j(r,l,h),i(x(x(x({},t),e),u())),o=n,c()||(s=a.setTimeout(f))};return()=>(s=a.setTimeout(f),()=>{s()})}(t,e,r,o,i,a):function(t,e,r,n,i,a,o){var l,u=null,c=i.reduce((r,n)=>x(x({},r),{},{[n]:[t[n],e[n]]}),{}),s=i=>{l||(l=i);var f=(i-l)/n,h=m((t,e)=>w(...e,r(f)),c);if(a(x(x(x({},t),e),h)),f<1)u=o.setTimeout(s);else{var d=m((t,e)=>w(...e,r(1)),c);a(x(x(x({},t),e),d))}};return()=>(u=o.setTimeout(s),()=>{u()})}(t,e,r,n,o,i,a)};class E{setTimeout(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=e?t(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var A=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function M(){return(M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){k(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function k(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class C extends n.PureComponent{componentDidMount(){var{isActive:t,canBegin:e}=this.props;this.mounted=!0,t&&e&&this.runAnimation(this.props)}componentDidUpdate(t){var{isActive:e,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:l}=this.props,{style:u}=this.state;if(r){if(!e){this.state&&u&&(n&&u[n]!==o||!n&&u!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(t.to,o)||!t.canBegin||!t.isActive){var c=!t.canBegin||!t.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||i?l:t.to;this.state&&u&&(n&&u[n]!==s||!n&&u!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(_(_({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:t}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}handleStyleChange(t){this.changeStyle(t)}changeStyle(t){this.mounted&&this.setState({style:t})}runJSAnimation(t){var{from:e,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=t,u=P(e,r,h(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=u()},n,o])}runAnimation(t){var{begin:e,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:u}=t;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof u||"spring"===a)return void this.runJSAnimation(t);var c=n?{[n]:i}:i,s=v(Object.keys(c),r,a);this.manager.start([o,e,_(_({},c),{},{transition:s}),r,l])}render(){var t=this.props,{children:e,begin:r,duration:i,attributeName:a,easing:o,isActive:l,from:u,to:c,canBegin:s,onAnimationEnd:f,shouldReAnimate:h,onAnimationReStart:d,animationManager:p}=t,y=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,A),v=n.Children.count(e),g=this.state.style;if("function"==typeof e)return e(g);if(!l||0===v||i<=0)return e;var m=t=>{var{style:e={},className:r}=t.props;return(0,n.cloneElement)(t,_(_({},y),{},{style:_(_({},e),g),className:r}))};return 1===v?m(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,t=>m(t)))}constructor(t,e){super(t,e),k(this,"mounted",!1),k(this,"manager",null),k(this,"stopJSAnimation",null),k(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:u}=this.props;if(this.manager=u,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}k(C,"displayName","Animate"),k(C,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var T=(0,n.createContext)(null);function D(t){var e,r,i,a,o,l,u,c,s=(0,n.useContext)(T);return n.createElement(C,M({},t,{animationManager:null!=(u=null!=(c=t.animationManager)?c:s)?u:(e=new E,i=()=>null,a=!1,o=null,l=t=>{if(!a){if(Array.isArray(t)){if(!t.length)return;var[r,...n]=t;if("number"==typeof r){o=e.setTimeout(l.bind(null,n),r);return}l(r),o=e.setTimeout(l.bind(null,n));return}"object"==typeof t&&i(t),"function"==typeof t&&t()}},{stop:()=>{a=!0},start:t=>{a=!1,o&&(o(),o=null),l(t)},subscribe:t=>(i=t,()=>{i=()=>null}),getTimeoutController:()=>e})}))}},4487:(t,e,r)=>{"use strict";r.d(e,{LV:()=>l,M:()=>a,hq:()=>i});var n=(0,r(5710).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(t,e){if(t.chartData=e.payload,null==e.payload){t.dataStartIndex=0,t.dataEndIndex=0;return}e.payload.length>0&&t.dataEndIndex!==e.payload.length-1&&(t.dataEndIndex=e.payload.length-1)},setComputedData(t,e){t.computedData=e.payload},setDataStartEndIndexes(t,e){var{startIndex:r,endIndex:n}=e.payload;null!=r&&(t.dataStartIndex=r),null!=n&&(t.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:a,setComputedData:o}=n.actions,l=n.reducer},4517:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(8132),i=r(6200),a=r(7298),o=r(921),l=r(3205);e.matchesProperty=function(t,e){switch(typeof t){case"object":Object.is(t?.valueOf(),-0)&&(t="-0");break;case"number":t=i.toKey(t)}return e=a.cloneDeep(e),function(r){let i=o.get(r,t);return void 0===i?l.has(r,t):void 0===e?void 0===i:n.isMatch(i,e)}}},4532:(t,e,r)=>{"use strict";r.d(e,{Qx:()=>c,a6:()=>s,h4:()=>H,jM:()=>G,ss:()=>F});var n,i=Symbol.for("immer-nothing"),a=Symbol.for("immer-draftable"),o=Symbol.for("immer-state");function l(t,...e){throw Error(`[Immer] minified error nr: ${t}. Full error at: https://bit.ly/3cXEKWf`)}var u=Object.getPrototypeOf;function c(t){return!!t&&!!t[o]}function s(t){return!!t&&(h(t)||Array.isArray(t)||!!t[a]||!!t.constructor?.[a]||g(t)||m(t))}var f=Object.prototype.constructor.toString();function h(t){if(!t||"object"!=typeof t)return!1;let e=u(t);if(null===e)return!0;let r=Object.hasOwnProperty.call(e,"constructor")&&e.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function d(t,e){0===p(t)?Reflect.ownKeys(t).forEach(r=>{e(r,t[r],t)}):t.forEach((r,n)=>e(n,r,t))}function p(t){let e=t[o];return e?e.type_:Array.isArray(t)?1:g(t)?2:3*!!m(t)}function y(t,e){return 2===p(t)?t.has(e):Object.prototype.hasOwnProperty.call(t,e)}function v(t,e,r){let n=p(t);2===n?t.set(e,r):3===n?t.add(r):t[e]=r}function g(t){return t instanceof Map}function m(t){return t instanceof Set}function b(t){return t.copy_||t.base_}function x(t,e){if(g(t))return new Map(t);if(m(t))return new Set(t);if(Array.isArray(t))return Array.prototype.slice.call(t);let r=h(t);if(!0!==e&&("class_only"!==e||r)){let e=u(t);return null!==e&&r?{...t}:Object.assign(Object.create(e),t)}{let e=Object.getOwnPropertyDescriptors(t);delete e[o];let r=Reflect.ownKeys(e);for(let n=0;n<r.length;n++){let i=r[n],a=e[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(e[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:t[i]})}return Object.create(u(t),e)}}function w(t,e=!1){return j(t)||c(t)||!s(t)||(p(t)>1&&(t.set=t.add=t.clear=t.delete=O),Object.freeze(t),e&&Object.entries(t).forEach(([t,e])=>w(e,!0))),t}function O(){l(2)}function j(t){return Object.isFrozen(t)}var P={};function E(t){let e=P[t];return e||l(0,t),e}function A(t,e){e&&(E("Patches"),t.patches_=[],t.inversePatches_=[],t.patchListener_=e)}function M(t){S(t),t.drafts_.forEach(k),t.drafts_=null}function S(t){t===n&&(n=t.parent_)}function _(t){return n={drafts_:[],parent_:n,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function k(t){let e=t[o];0===e.type_||1===e.type_?e.revoke_():e.revoked_=!0}function C(t,e){e.unfinalizedDrafts_=e.drafts_.length;let r=e.drafts_[0];return void 0!==t&&t!==r?(r[o].modified_&&(M(e),l(4)),s(t)&&(t=T(e,t),e.parent_||N(e,t)),e.patches_&&E("Patches").generateReplacementPatches_(r[o].base_,t,e.patches_,e.inversePatches_)):t=T(e,r,[]),M(e),e.patches_&&e.patchListener_(e.patches_,e.inversePatches_),t!==i?t:void 0}function T(t,e,r){if(j(e))return e;let n=e[o];if(!n)return d(e,(i,a)=>D(t,n,e,i,a,r)),e;if(n.scope_!==t)return e;if(!n.modified_)return N(t,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let e=n.copy_,i=e,a=!1;3===n.type_&&(i=new Set(e),e.clear(),a=!0),d(i,(i,o)=>D(t,n,e,i,o,r,a)),N(t,e,!1),r&&t.patches_&&E("Patches").generatePatches_(n,r,t.patches_,t.inversePatches_)}return n.copy_}function D(t,e,r,n,i,a,o){if(c(i)){let o=T(t,i,a&&e&&3!==e.type_&&!y(e.assigned_,n)?a.concat(n):void 0);if(v(r,n,o),!c(o))return;t.canAutoFreeze_=!1}else o&&r.add(i);if(s(i)&&!j(i)){if(!t.immer_.autoFreeze_&&t.unfinalizedDrafts_<1)return;T(t,i),(!e||!e.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&N(t,i)}}function N(t,e,r=!1){!t.parent_&&t.immer_.autoFreeze_&&t.canAutoFreeze_&&w(e,r)}var I={get(t,e){if(e===o)return t;let r=b(t);if(!y(r,e)){var n=t,i=r,a=e;let o=B(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let l=r[e];return t.finalized_||!s(l)?l:l===L(t.base_,e)?(R(t),t.copy_[e]=$(l,t)):l},has:(t,e)=>e in b(t),ownKeys:t=>Reflect.ownKeys(b(t)),set(t,e,r){let n=B(b(t),e);if(n?.set)return n.set.call(t.draft_,r),!0;if(!t.modified_){let n=L(b(t),e),i=n?.[o];if(i&&i.base_===r)return t.copy_[e]=r,t.assigned_[e]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(t.base_,e)))return!0;R(t),U(t)}return!!(t.copy_[e]===r&&(void 0!==r||e in t.copy_)||Number.isNaN(r)&&Number.isNaN(t.copy_[e]))||(t.copy_[e]=r,t.assigned_[e]=!0,!0)},deleteProperty:(t,e)=>(void 0!==L(t.base_,e)||e in t.base_?(t.assigned_[e]=!1,R(t),U(t)):delete t.assigned_[e],t.copy_&&delete t.copy_[e],!0),getOwnPropertyDescriptor(t,e){let r=b(t),n=Reflect.getOwnPropertyDescriptor(r,e);return n?{writable:!0,configurable:1!==t.type_||"length"!==e,enumerable:n.enumerable,value:r[e]}:n},defineProperty(){l(11)},getPrototypeOf:t=>u(t.base_),setPrototypeOf(){l(12)}},z={};function L(t,e){let r=t[o];return(r?b(r):t)[e]}function B(t,e){if(!(e in t))return;let r=u(t);for(;r;){let t=Object.getOwnPropertyDescriptor(r,e);if(t)return t;r=u(r)}}function U(t){!t.modified_&&(t.modified_=!0,t.parent_&&U(t.parent_))}function R(t){t.copy_||(t.copy_=x(t.base_,t.scope_.immer_.useStrictShallowCopy_))}function $(t,e){let r=g(t)?E("MapSet").proxyMap_(t,e):m(t)?E("MapSet").proxySet_(t,e):function(t,e){let r=Array.isArray(t),i={type_:+!!r,scope_:e?e.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:e,base_:t,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=I;r&&(a=[i],o=z);let{revoke:l,proxy:u}=Proxy.revocable(a,o);return i.draft_=u,i.revoke_=l,u}(t,e);return(e?e.scope_:n).drafts_.push(r),r}function F(t){return c(t)||l(10,t),function t(e){let r;if(!s(e)||j(e))return e;let n=e[o];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=x(e,n.scope_.immer_.useStrictShallowCopy_)}else r=x(e,!0);return d(r,(e,n)=>{v(r,e,t(n))}),n&&(n.finalized_=!1),r}(t)}d(I,(t,e)=>{z[t]=function(){return arguments[0]=arguments[0][0],e.apply(this,arguments)}}),z.deleteProperty=function(t,e){return z.set.call(this,t,e,void 0)},z.set=function(t,e,r){return I.set.call(this,t[0],e,r,t[0])};var K=new class{constructor(t){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,e,r)=>{let n;if("function"==typeof t&&"function"!=typeof e){let r=e;e=t;let n=this;return function(t=r,...i){return n.produce(t,t=>e.call(this,t,...i))}}if("function"!=typeof e&&l(6),void 0!==r&&"function"!=typeof r&&l(7),s(t)){let i=_(this),a=$(t,void 0),o=!0;try{n=e(a),o=!1}finally{o?M(i):S(i)}return A(i,r),C(n,i)}if(t&&"object"==typeof t)l(1,t);else{if(void 0===(n=e(t))&&(n=t),n===i&&(n=void 0),this.autoFreeze_&&w(n,!0),r){let e=[],i=[];E("Patches").generateReplacementPatches_(t,n,e,i),r(e,i)}return n}},this.produceWithPatches=(t,e)=>{let r,n;return"function"==typeof t?(e,...r)=>this.produceWithPatches(e,e=>t(e,...r)):[this.produce(t,e,(t,e)=>{r=t,n=e}),r,n]},"boolean"==typeof t?.autoFreeze&&this.setAutoFreeze(t.autoFreeze),"boolean"==typeof t?.useStrictShallowCopy&&this.setUseStrictShallowCopy(t.useStrictShallowCopy)}createDraft(t){s(t)||l(8),c(t)&&(t=F(t));let e=_(this),r=$(t,void 0);return r[o].isManual_=!0,S(e),r}finishDraft(t,e){let r=t&&t[o];r&&r.isManual_||l(9);let{scope_:n}=r;return A(n,e),C(void 0,n)}setAutoFreeze(t){this.autoFreeze_=t}setUseStrictShallowCopy(t){this.useStrictShallowCopy_=t}applyPatches(t,e){let r;for(r=e.length-1;r>=0;r--){let n=e[r];if(0===n.path.length&&"replace"===n.op){t=n.value;break}}r>-1&&(e=e.slice(r+1));let n=E("Patches").applyPatches_;return c(t)?n(t,e):this.produce(t,t=>n(t,e))}},G=K.produce;function H(t){return t}K.produceWithPatches.bind(K),K.setAutoFreeze.bind(K),K.setUseStrictShallowCopy.bind(K),K.applyPatches.bind(K),K.createDraft.bind(K),K.finishDraft.bind(K)},4545:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isDeepKey=function(t){switch(typeof t){case"number":case"symbol":return!1;case"string":return t.includes(".")||t.includes("[")||t.includes("]")}}},4624:(t,e,r)=>{"use strict";r.d(e,{DX:()=>o});var n=r(2115);function i(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}var a=r(5155),o=function(t){let e=function(t){let e=n.forwardRef((t,e)=>{let{children:r,...a}=t;if(n.isValidElement(r)){var o;let t,l,u=(o=r,(l=(t=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.ref:(l=(t=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.props.ref:o.props.ref||o.ref),c=function(t,e){let r={...e};for(let n in e){let i=t[n],a=e[n];/^on[A-Z]/.test(n)?i&&a?r[n]=(...t)=>{let e=a(...t);return i(...t),e}:i&&(r[n]=i):"style"===n?r[n]={...i,...a}:"className"===n&&(r[n]=[i,a].filter(Boolean).join(" "))}return{...t,...r}}(a,r.props);return r.type!==n.Fragment&&(c.ref=e?function(...t){return e=>{let r=!1,n=t.map(t=>{let n=i(t,e);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let e=0;e<n.length;e++){let r=n[e];"function"==typeof r?r():i(t[e],null)}}}}(e,u):u),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),r=n.forwardRef((t,r)=>{let{children:i,...o}=t,l=n.Children.toArray(i),c=l.find(u);if(c){let t=c.props.children,i=l.map(e=>e!==c?e:n.Children.count(t)>1?n.Children.only(null):n.isValidElement(t)?t.props.children:null);return(0,a.jsx)(e,{...o,ref:r,children:n.isValidElement(t)?n.cloneElement(t,void 0,i):null})}return(0,a.jsx)(e,{...o,ref:r,children:i})});return r.displayName=`${t}.Slot`,r}("Slot"),l=Symbol("radix.slottable");function u(t){return n.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===l}},4664:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(2694);e.toFinite=function(t){return t?(t=n.toNumber(t))===1/0||t===-1/0?(t<0?-1:1)*Number.MAX_VALUE:t==t?t:0:0===t?t:0}},4679:(t,e,r)=>{"use strict";r.d(e,{I:()=>q});var n=r(2115);function i(){}function a(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function o(t){this._context=t}function l(t){this._context=t}function u(t){this._context=t}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class c{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function h(t){return new f(t)}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function d(t,e,r){var n=t._x1-t._x0,i=e-t._x1,a=(t._y1-t._y0)/(n||i<0&&-0),o=(r-t._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function p(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function y(t,e,r){var n=t._x0,i=t._y0,a=t._x1,o=t._y1,l=(a-n)/3;t._context.bezierCurveTo(n+l,i+l*e,a-l,o-l*r,a,o)}function v(t){this._context=t}function g(t){this._context=new m(t)}function m(t){this._context=t}function b(t){this._context=t}function x(t){var e,r,n=t.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,a[e]=4,o[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/a[e-1],a[e]-=r,o[e]-=r*o[e-1];for(i[n-1]=o[n-1]/a[n-1],e=n-2;e>=0;--e)i[e]=(o[e]-i[e+1])/a[e];for(e=0,a[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)a[e]=2*t[e+1]-i[e+1];return[i,a]}function w(t,e){this._context=t,this._t=e}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,p(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,y(this,p(this,r=d(this,t,e)),r);break;default:y(this,this._t0,r=d(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(g.prototype=Object.create(v.prototype)).point=function(t,e){v.prototype.point.call(this,e,t)},m.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,a){this._context.bezierCurveTo(e,t,n,r,a,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=x(t),i=x(e),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],t[o],e[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var O=r(9819),j=r(5654);let P=Math.PI,E=2*P,A=E-1e-6;function M(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class S{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?M:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return M;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,a){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(t,e,r,n,i){if(t*=1,e*=1,r*=1,n*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let a=this._x1,o=this._y1,l=r-t,u=n-e,c=a-t,s=o-e,f=c*c+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6)if(Math.abs(s*l-u*c)>1e-6&&i){let h=r-a,d=n-o,p=l*l+u*u,y=Math.sqrt(p),v=Math.sqrt(f),g=i*Math.tan((P-Math.acos((p+f-(h*h+d*d))/(2*y*v)))/2),m=g/v,b=g/y;Math.abs(m-1)>1e-6&&this._append`L${t+m*c},${e+m*s}`,this._append`A${i},${i},0,0,${+(s*h>c*d)},${this._x1=t+b*l},${this._y1=e+b*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,n,i,a){if(t*=1,e*=1,r*=1,a=!!a,r<0)throw Error(`negative radius: ${r}`);let o=r*Math.cos(n),l=r*Math.sin(n),u=t+o,c=e+l,s=1^a,f=a?n-i:i-n;null===this._x1?this._append`M${u},${c}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-c)>1e-6)&&this._append`L${u},${c}`,r&&(f<0&&(f=f%E+E),f>A?this._append`A${r},${r},0,1,${s},${t-o},${e-l}A${r},${r},0,1,${s},${this._x1=u},${this._y1=c}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=P)},${s},${this._x1=t+r*Math.cos(i)},${this._y1=e+r*Math.sin(i)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function _(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new S(e)}function k(t){return t[0]}function C(t){return t[1]}function T(t,e){var r=(0,j.A)(!0),n=null,i=h,a=null,o=_(l);function l(l){var u,c,s,f=(l=(0,O.A)(l)).length,h=!1;for(null==n&&(a=i(s=o())),u=0;u<=f;++u)!(u<f&&r(c=l[u],u,l))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+t(c,u,l),+e(c,u,l));if(s)return a=null,s+""||null}return t="function"==typeof t?t:void 0===t?k:(0,j.A)(t),e="function"==typeof e?e:void 0===e?C:(0,j.A)(e),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),l):t},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),l):e},l.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,j.A)(!!t),l):r},l.curve=function(t){return arguments.length?(i=t,null!=n&&(a=i(n)),l):i},l.context=function(t){return arguments.length?(null==t?n=a=null:a=i(n=t),l):n},l}function D(t,e,r){var n=null,i=(0,j.A)(!0),a=null,o=h,l=null,u=_(c);function c(c){var s,f,h,d,p,y=(c=(0,O.A)(c)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(p=u())),s=0;s<=y;++s){if(!(s<y&&i(d=c[s],s,c))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),h=s-1;h>=f;--h)l.point(g[h],m[h]);l.lineEnd(),l.areaEnd()}v&&(g[s]=+t(d,s,c),m[s]=+e(d,s,c),l.point(n?+n(d,s,c):g[s],r?+r(d,s,c):m[s]))}if(p)return l=null,p+""||null}function s(){return T().defined(i).curve(o).context(a)}return t="function"==typeof t?t:void 0===t?k:(0,j.A)(+t),e="function"==typeof e?e:void 0===e?(0,j.A)(0):(0,j.A)(+e),r="function"==typeof r?r:void 0===r?C:(0,j.A)(+r),c.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),n=null,c):t},c.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),c):t},c.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,j.A)(+t),c):n},c.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),r=null,c):e},c.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),c):e},c.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,j.A)(+t),c):r},c.lineX0=c.lineY0=function(){return s().x(t).y(e)},c.lineY1=function(){return s().x(t).y(r)},c.lineX1=function(){return s().x(n).y(e)},c.defined=function(t){return arguments.length?(i="function"==typeof t?t:(0,j.A)(!!t),c):i},c.curve=function(t){return arguments.length?(o=t,null!=a&&(l=o(a)),c):o},c.context=function(t){return arguments.length?(null==t?a=l=null:l=o(a=t),c):a},c}S.prototype;var N=r(2596),I=r(3597),z=r(788),L=r(6377),B=r(8892);function U(){return(U=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function R(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function $(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?R(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var F={curveBasisClosed:function(t){return new l(t)},curveBasisOpen:function(t){return new u(t)},curveBasis:function(t){return new o(t)},curveBumpX:function(t){return new c(t,!0)},curveBumpY:function(t){return new c(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:h,curveMonotoneX:function(t){return new v(t)},curveMonotoneY:function(t){return new g(t)},curveNatural:function(t){return new b(t)},curveStep:function(t){return new w(t,.5)},curveStepAfter:function(t){return new w(t,1)},curveStepBefore:function(t){return new w(t,0)}},K=t=>(0,B.H)(t.x)&&(0,B.H)(t.y),G=t=>t.x,H=t=>t.y,W=(t,e)=>{if("function"==typeof t)return t;var r="curve".concat((0,L.Zb)(t));return("curveMonotone"===r||"curveBump"===r)&&e?F["".concat(r).concat("vertical"===e?"Y":"X")]:F[r]||h},V=t=>{var e,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=t,l=W(r,a),u=o?n.filter(K):n;if(Array.isArray(i)){var c=o?i.filter(t=>K(t)):i,s=u.map((t,e)=>$($({},t),{},{base:c[e]}));return(e="vertical"===a?D().y(H).x1(G).x0(t=>t.base.x):D().x(G).y1(H).y0(t=>t.base.y)).defined(K).curve(l),e(s)}return(e="vertical"===a&&(0,L.Et)(i)?D().y(H).x1(G).x0(i):(0,L.Et)(i)?D().x(G).y1(H).y0(i):T().x(G).y(H)).defined(K).curve(l),e(u)},q=t=>{var{className:e,points:r,path:i,pathRef:a}=t;if((!r||!r.length)&&!i)return null;var o=r&&r.length?V(t):i;return n.createElement("path",U({},(0,z.J9)(t,!1),(0,I._U)(t),{className:(0,N.$)("recharts-curve",e),d:null===o?void 0:o,ref:a}))}},4732:(t,e,r)=>{"use strict";r.d(e,{BZ:()=>I,aX:()=>B,dS:()=>N,dp:()=>C,fW:()=>P,pg:()=>D,r1:()=>S,u9:()=>z,yn:()=>L});var n=r(8924),i=r(241),a=r.n(i),o=r(1971),l=r(9827),u=r(6377),c=r(356),s=r(215),f=r(8478),h=r(7238),d=r(9449),p=r(2589),y=r(530),v=r(1928),g=r(841),m=r(4968),b=r(5146),x=r(6670),w=r(5714);function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var P=()=>(0,o.G)(f.iO),E=(t,e)=>e,A=(t,e,r)=>r,M=(t,e,r,n)=>n,S=(0,n.Mz)(s.R4,t=>a()(t,t=>t.coordinate)),_=(0,n.Mz)([w.J,E,A,M],v.i),k=(0,n.Mz)([_,s.n4],g.P),C=(t,e,r)=>{if(null!=e){var n=(0,w.J)(t);return"axis"===e?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},T=(0,n.Mz)([w.J,E,A,M],b.q),D=(0,n.Mz)([p.Lp,p.A$,h.fz,d.GO,s.R4,M,T,x.x],m.o),N=(0,n.Mz)([_,D],(t,e)=>{var r;return null!=(r=t.coordinate)?r:e}),I=(0,n.Mz)(s.R4,k,y.E),z=(0,n.Mz)([T,k,c.LF,s.Dn,I,x.x,E],(t,e,r,n,i,a,o)=>{if(null!=e&&null!=a){var{chartData:c,computedData:s,dataStartIndex:f,dataEndIndex:h}=r;return t.reduce((t,r)=>{var d,p,y,v,g,{dataDefinedOnItem:m,settings:b}=r,x=function(t,e,r){return Array.isArray(t)&&t&&e+r!==0?t.slice(e,r+1):t}((d=m,p=c,null!=d?d:p),f,h),w=null!=(y=null==b?void 0:b.dataKey)?y:null==n?void 0:n.dataKey,O=null==b?void 0:b.nameKey;return Array.isArray(v=null!=n&&n.dataKey&&!(null!=n&&n.allowDuplicatedCategory)&&Array.isArray(x)&&"axis"===o?(0,u.eP)(x,n.dataKey,i):a(x,e,s,O))?v.forEach(e=>{var r=j(j({},b),{},{name:e.name,unit:e.unit,color:void 0,fill:void 0});t.push((0,l.GF)({tooltipEntrySettings:r,dataKey:e.dataKey,payload:e.payload,value:(0,l.kr)(e.payload,e.dataKey),name:e.name}))}):t.push((0,l.GF)({tooltipEntrySettings:b,dataKey:w,payload:v,value:(0,l.kr)(v,w),name:null!=(g=(0,l.kr)(v,O))?g:null==b?void 0:b.name})),t},[])}}),L=(0,n.Mz)([_],t=>({isActive:t.active,activeIndex:t.index})),B=(t,e,r,n,i,a,o,u)=>{if(t&&e&&n&&i&&a){var c=(0,l.r4)(t.chartX,t.chartY,e,r,u);if(c){var s=(0,l.SW)(c,e),f=(0,l.gH)(s,o,a,n,i),h=(0,l.bk)(e,a,f,c);return{activeIndex:String(f),activeCoordinate:h}}}}},4754:(t,e,r)=>{"use strict";r.d(e,{d:()=>T});var n=r(2115),i=r(675),a=r(6377),o=r(788),l=r(9827),u=r(9035),c=r(9584),s=r(7238),f=r(2183),h=r(1971),d=r(1807),p=r(3389),y=["x1","y1","x2","y2","key"],v=["offset"],g=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function w(){return(w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function O(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var j=t=>{var{fill:e}=t;if(!e||"none"===e)return null;var{fillOpacity:r,x:i,y:a,width:o,height:l,ry:u}=t;return n.createElement("rect",{x:i,y:a,ry:u,width:o,height:l,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function P(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if("function"==typeof t)r=t(e);else{var{x1:i,y1:a,x2:l,y2:u,key:c}=e,s=O(e,y),f=(0,o.J9)(s,!1),{offset:h}=f,d=O(f,v);r=n.createElement("line",w({},d,{x1:i,y1:a,x2:l,y2:u,fill:"none",key:c}))}return r}function E(t){var{x:e,width:r,horizontal:i=!0,horizontalPoints:a}=t;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=t,u=O(t,g),c=a.map((t,n)=>P(i,x(x({},u),{},{x1:e,y1:t,x2:e+r,y2:t,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function A(t){var{y:e,height:r,vertical:i=!0,verticalPoints:a}=t;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=t,u=O(t,m),c=a.map((t,n)=>P(i,x(x({},u),{},{x1:t,y1:e,x2:t,y2:e+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function M(t){var{horizontalFill:e,fillOpacity:r,x:i,y:a,width:o,height:l,horizontalPoints:u,horizontal:c=!0}=t;if(!c||!e||!e.length)return null;var s=u.map(t=>Math.round(t+a-a)).sort((t,e)=>t-e);a!==s[0]&&s.unshift(0);var f=s.map((t,u)=>{var c=s[u+1]?s[u+1]-t:a+l-t;if(c<=0)return null;var f=u%e.length;return n.createElement("rect",{key:"react-".concat(u),y:t,x:i,height:c,width:o,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function S(t){var{vertical:e=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:l,height:u,verticalPoints:c}=t;if(!e||!r||!r.length)return null;var s=c.map(t=>Math.round(t+a-a)).sort((t,e)=>t-e);a!==s[0]&&s.unshift(0);var f=s.map((t,e)=>{var c=s[e+1]?s[e+1]-t:a+l-t;if(c<=0)return null;var f=e%r.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:o,width:c,height:u,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var _=(t,e)=>{var{xAxis:r,width:n,height:i,offset:a}=t;return(0,l.PW)((0,u.f)(x(x(x({},c.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,e)},k=(t,e)=>{var{yAxis:r,width:n,height:i,offset:a}=t;return(0,l.PW)((0,u.f)(x(x(x({},c.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,e)},C={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function T(t){var e=(0,s.yi)(),r=(0,s.rY)(),o=(0,s.hj)(),l=x(x({},(0,p.e)(t,C)),{},{x:(0,a.Et)(t.x)?t.x:o.left,y:(0,a.Et)(t.y)?t.y:o.top,width:(0,a.Et)(t.width)?t.width:o.width,height:(0,a.Et)(t.height)?t.height:o.height}),{xAxisId:u,yAxisId:c,x:y,y:v,width:g,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:P}=l,T=(0,d.r)(),D=(0,h.G)(t=>(0,f.ZB)(t,"xAxis",u,T)),N=(0,h.G)(t=>(0,f.ZB)(t,"yAxis",c,T));if(!(0,a.Et)(g)||g<=0||!(0,a.Et)(m)||m<=0||!(0,a.Et)(y)||y!==+y||!(0,a.Et)(v)||v!==+v)return null;var I=l.verticalCoordinatesGenerator||_,z=l.horizontalCoordinatesGenerator||k,{horizontalPoints:L,verticalPoints:B}=l;if((!L||!L.length)&&"function"==typeof z){var U=O&&O.length,R=z({yAxis:N?x(x({},N),{},{ticks:U?O:N.ticks}):void 0,width:e,height:r,offset:o},!!U||b);(0,i.R)(Array.isArray(R),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof R,"]")),Array.isArray(R)&&(L=R)}if((!B||!B.length)&&"function"==typeof I){var $=P&&P.length,F=I({xAxis:D?x(x({},D),{},{ticks:$?P:D.ticks}):void 0,width:e,height:r,offset:o},!!$||b);(0,i.R)(Array.isArray(F),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof F,"]")),Array.isArray(F)&&(B=F)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(j,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(M,w({},l,{horizontalPoints:L})),n.createElement(S,w({},l,{verticalPoints:B})),n.createElement(E,w({},l,{offset:o,horizontalPoints:L,xAxis:D,yAxis:N})),n.createElement(A,w({},l,{offset:o,verticalPoints:B,xAxis:D,yAxis:N})))}T.displayName="CartesianGrid"},4804:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(8132),i=r(2429);e.matches=function(t){return t=i.cloneDeep(t),e=>n.isMatch(e,t)}},4890:(t,e,r)=>{"use strict";r.d(e,{E1:()=>v,En:()=>m,Ix:()=>l,Nt:()=>p,RD:()=>s,UF:()=>c,XB:()=>u,jF:()=>y,k_:()=>a,o4:()=>g,xS:()=>h});var n=r(5710),i=r(4532),a={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:a,hover:a},axisInteraction:{click:a,hover:a},keyboardInteraction:a,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(t,e){t.tooltipItemPayloads.push((0,i.h4)(e.payload))},removeTooltipEntrySettings(t,e){var r=(0,i.ss)(t).tooltipItemPayloads.indexOf((0,i.h4)(e.payload));r>-1&&t.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(t,e){t.settings=e.payload},setActiveMouseOverItemIndex(t,e){t.syncInteraction.active=!1,t.keyboardInteraction.active=!1,t.itemInteraction.hover.active=!0,t.itemInteraction.hover.index=e.payload.activeIndex,t.itemInteraction.hover.dataKey=e.payload.activeDataKey,t.itemInteraction.hover.coordinate=e.payload.activeCoordinate},mouseLeaveChart(t){t.itemInteraction.hover.active=!1,t.axisInteraction.hover.active=!1},mouseLeaveItem(t){t.itemInteraction.hover.active=!1},setActiveClickItemIndex(t,e){t.syncInteraction.active=!1,t.itemInteraction.click.active=!0,t.keyboardInteraction.active=!1,t.itemInteraction.click.index=e.payload.activeIndex,t.itemInteraction.click.dataKey=e.payload.activeDataKey,t.itemInteraction.click.coordinate=e.payload.activeCoordinate},setMouseOverAxisIndex(t,e){t.syncInteraction.active=!1,t.axisInteraction.hover.active=!0,t.keyboardInteraction.active=!1,t.axisInteraction.hover.index=e.payload.activeIndex,t.axisInteraction.hover.dataKey=e.payload.activeDataKey,t.axisInteraction.hover.coordinate=e.payload.activeCoordinate},setMouseClickAxisIndex(t,e){t.syncInteraction.active=!1,t.keyboardInteraction.active=!1,t.axisInteraction.click.active=!0,t.axisInteraction.click.index=e.payload.activeIndex,t.axisInteraction.click.dataKey=e.payload.activeDataKey,t.axisInteraction.click.coordinate=e.payload.activeCoordinate},setSyncInteraction(t,e){t.syncInteraction=e.payload},setKeyboardInteraction(t,e){t.keyboardInteraction.active=e.payload.active,t.keyboardInteraction.index=e.payload.activeIndex,t.keyboardInteraction.coordinate=e.payload.activeCoordinate,t.keyboardInteraction.dataKey=e.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:u,setTooltipSettingsState:c,setActiveMouseOverItemIndex:s,mouseLeaveItem:f,mouseLeaveChart:h,setActiveClickItemIndex:d,setMouseOverAxisIndex:p,setMouseClickAxisIndex:y,setSyncInteraction:v,setKeyboardInteraction:g}=o.actions,m=o.reducer},4968:(t,e,r)=>{"use strict";r.d(e,{o:()=>n});var n=(t,e,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var u=o[0],c=null==u?void 0:l(u.positions,a);if(null!=c)return c;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+e)/2};else return{x:(n.left+t)/2,y:s.coordinate}}}},4986:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toArray=function(t){return Array.isArray(t)?t:Array.from(t)}},4993:(t,e,r)=>{"use strict";var n=r(2115);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},5064:(t,e,r)=>{"use strict";r.d(e,{E:()=>n});var n=(0,r(2115).createContext)(null)},5115:(t,e,r)=>{"use strict";r.d(e,{$:()=>i,X:()=>a});var n=r(2115),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},5146:(t,e,r)=>{"use strict";r.d(e,{q:()=>n});var n=(t,e,r,n)=>{var i;return"axis"===e?t.tooltipItemPayloads:0===t.tooltipItemPayloads.length?[]:null==(i="hover"===r?t.itemInteraction.hover.dataKey:t.itemInteraction.click.dataKey)&&null!=n?[t.tooltipItemPayloads[0]]:t.tooltipItemPayloads.filter(t=>{var e;return(null==(e=t.settings)?void 0:e.dataKey)===i})}},5160:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.getTag=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}},5181:(t,e)=>{"use strict";function r(t){return"symbol"==typeof t?1:null===t?2:void 0===t?3:4*(t!=t)}Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.compareValues=(t,e,n)=>{if(t!==e){let i=r(t),a=r(e);if(i===a&&0===i){if(t<e)return"desc"===n?1:-1;if(t>e)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},5252:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(2520),i=r(2767);e.isEqual=function(t,e){return n.isEqualWith(t,e,i.noop)}},5306:(t,e,r)=>{"use strict";r.d(e,{CA:()=>y,MC:()=>c,QG:()=>p,Vi:()=>u,cU:()=>s,fR:()=>f});var n=r(5710),i=r(4532);function a(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(t,e){t.xAxis[e.payload.id]=(0,i.h4)(e.payload)},removeXAxis(t,e){delete t.xAxis[e.payload.id]},addYAxis(t,e){t.yAxis[e.payload.id]=(0,i.h4)(e.payload)},removeYAxis(t,e){delete t.yAxis[e.payload.id]},addZAxis(t,e){t.zAxis[e.payload.id]=(0,i.h4)(e.payload)},removeZAxis(t,e){delete t.zAxis[e.payload.id]},updateYAxisWidth(t,e){var{id:r,width:n}=e.payload;t.yAxis[r]&&(t.yAxis[r]=o(o({},t.yAxis[r]),{},{width:n}))}}}),{addXAxis:u,removeXAxis:c,addYAxis:s,removeYAxis:f,addZAxis:h,removeZAxis:d,updateYAxisWidth:p}=l.actions,y=l.reducer},5641:(t,e,r)=>{"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}r.d(e,{IZ:()=>l,Kg:()=>a,lY:()=>u,yy:()=>d}),r(2115);var a=Math.PI/180,o=t=>180*t/Math.PI,l=(t,e,r,n)=>({x:t+Math.cos(-a*n)*r,y:e+Math.sin(-a*n)*r}),u=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},c=(t,e)=>{var{x:r,y:n}=t,{x:i,y:a}=e;return Math.sqrt((r-i)**2+(n-a)**2)},s=(t,e)=>{var{x:r,y:n}=t,{cx:i,cy:a}=e,l=c({x:r,y:n},{x:i,y:a});if(l<=0)return{radius:l,angle:0};var u=Math.acos((r-i)/l);return n>a&&(u=2*Math.PI-u),{radius:l,angle:o(u),angleInRadian:u}},f=t=>{var{startAngle:e,endAngle:r}=t,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},h=(t,e)=>{var{startAngle:r,endAngle:n}=e;return t+360*Math.min(Math.floor(r/360),Math.floor(n/360))},d=(t,e)=>{var r,{x:n,y:a}=t,{radius:o,angle:l}=s({x:n,y:a},e),{innerRadius:u,outerRadius:c}=e;if(o<u||o>c||0===o)return null;var{startAngle:d,endAngle:p}=f(e),y=l;if(d<=p){for(;y>p;)y-=360;for(;y<d;)y+=360;r=y>=d&&y<=p}else{for(;y>d;)y-=360;for(;y<p;)y+=360;r=y>=p&&y<=d}return r?i(i({},e),{},{radius:o,angle:h(y,e)}):null}},5643:(t,e,r)=>{"use strict";t.exports=r(6115)},5654:(t,e,r)=>{"use strict";function n(t){return function(){return t}}r.d(e,{A:()=>n})},5672:(t,e,r)=>{t.exports=r(921).get},5710:(t,e,r)=>{"use strict";r.d(e,{U1:()=>m,VP:()=>c,Nc:()=>ty,Z0:()=>C});var n=r(52);function i(t){return({dispatch:e,getState:r})=>n=>i=>"function"==typeof i?i(e,r,t):n(i)}var a=i(),o=r(4532),l=(r(9509),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)});"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var u=t=>t&&"function"==typeof t.match;function c(t,e){function r(...n){if(e){let r=e(...n);if(!r)throw Error(tw(0));return{type:t,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:t,payload:n[0]}}return r.toString=()=>`${t}`,r.type=t,r.match=e=>(0,n.ve)(e)&&e.type===t,r}function s(t){return["type","payload","error","meta"].indexOf(t)>-1}var f=class t extends Array{constructor(...e){super(...e),Object.setPrototypeOf(this,t.prototype)}static get[Symbol.species](){return t}concat(...t){return super.concat.apply(this,t)}prepend(...e){return 1===e.length&&Array.isArray(e[0])?new t(...e[0].concat(this)):new t(...e.concat(this))}};function h(t){return(0,o.a6)(t)?(0,o.jM)(t,()=>{}):t}function d(t,e,r){return t.has(e)?t.get(e):t.set(e,r(e)).get(e)}var p=()=>function(t){let{thunk:e=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=t??{},l=new f;return e&&("boolean"==typeof e?l.push(a):l.push(i(e.extraArgument))),l},y=t=>e=>{setTimeout(e,t)},v=(t={type:"raf"})=>e=>(...r)=>{let n=e(...r),i=!0,a=!1,o=!1,l=new Set,u="tick"===t.type?queueMicrotask:"raf"===t.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:y(10):"callback"===t.type?t.queueNotification:y(t.timeout),c=()=>{o=!1,a&&(a=!1,l.forEach(t=>t()))};return Object.assign({},n,{subscribe(t){let e=n.subscribe(()=>i&&t());return l.add(t),()=>{e(),l.delete(t)}},dispatch(t){try{return(a=!(i=!t?.meta?.RTK_autoBatch))&&!o&&(o=!0,u(c)),n.dispatch(t)}finally{i=!0}}})},g=t=>function(e){let{autoBatch:r=!0}=e??{},n=new f(t);return r&&n.push(v("object"==typeof r?r:void 0)),n};function m(t){let e,r,i=p(),{reducer:a,middleware:o,devTools:u=!0,duplicateMiddlewareCheck:c=!0,preloadedState:s,enhancers:f}=t||{};if("function"==typeof a)e=a;else if((0,n.Qd)(a))e=(0,n.HY)(a);else throw Error(tw(1));r="function"==typeof o?o(i):i();let h=n.Zz;u&&(h=l({trace:!1,..."object"==typeof u&&u}));let d=g((0,n.Tw)(...r)),y=h(..."function"==typeof f?f(d):d());return(0,n.y$)(e,s,y)}function b(t){let e,r={},n=[],i={addCase(t,e){let n="string"==typeof t?t:t.type;if(!n)throw Error(tw(28));if(n in r)throw Error(tw(29));return r[n]=e,i},addMatcher:(t,e)=>(n.push({matcher:t,reducer:e}),i),addDefaultCase:t=>(e=t,i)};return t(i),[r,n,e]}var x=(t,e)=>u(t)?t.match(e):t(e);function w(...t){return e=>t.some(t=>x(t,e))}var O=(t=21)=>{let e="",r=t;for(;r--;)e+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return e},j=["name","message","stack","code"],P=class{constructor(t,e){this.payload=t,this.meta=e}_type},E=class{constructor(t,e){this.payload=t,this.meta=e}_type},A=t=>{if("object"==typeof t&&null!==t){let e={};for(let r of j)"string"==typeof t[r]&&(e[r]=t[r]);return e}return{message:String(t)}},M="External signal was aborted";function S(t){if(t.meta&&t.meta.rejectedWithValue)throw t.payload;if(t.error)throw t.error;return t.payload}var _=Symbol.for("rtk-slice-createasyncthunk"),k=(t=>(t.reducer="reducer",t.reducerWithPrepare="reducerWithPrepare",t.asyncThunk="asyncThunk",t))(k||{}),C=function({creators:t}={}){let e=t?.asyncThunk?.[_];return function(t){let r,{name:n,reducerPath:i=n}=t;if(!n)throw Error(tw(11));let a=("function"==typeof t.reducers?t.reducers(function(){function t(t,e){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...e}}return t.withTypes=()=>t,{reducer:t=>Object.assign({[t.name]:(...e)=>t(...e)}[t.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(t,e)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:e}),asyncThunk:t}}()):t.reducers)||{},l=Object.keys(a),u={},s={},f={},p=[],y={addCase(t,e){let r="string"==typeof t?t:t.type;if(!r)throw Error(tw(12));if(r in s)throw Error(tw(13));return s[r]=e,y},addMatcher:(t,e)=>(p.push({matcher:t,reducer:e}),y),exposeAction:(t,e)=>(f[t]=e,y),exposeCaseReducer:(t,e)=>(u[t]=e,y)};function v(){let[e={},r=[],n]="function"==typeof t.extraReducers?b(t.extraReducers):[t.extraReducers],i={...e,...s};return function(t,e){let r,[n,i,a]=b(e);if("function"==typeof t)r=()=>h(t());else{let e=h(t);r=()=>e}function l(t=r(),e){let u=[n[e.type],...i.filter(({matcher:t})=>t(e)).map(({reducer:t})=>t)];return 0===u.filter(t=>!!t).length&&(u=[a]),u.reduce((t,r)=>{if(r)if((0,o.Qx)(t)){let n=r(t,e);return void 0===n?t:n}else{if((0,o.a6)(t))return(0,o.jM)(t,t=>r(t,e));let n=r(t,e);if(void 0===n){if(null===t)return t;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return t},t)}return l.getInitialState=r,l}(t.initialState,t=>{for(let e in i)t.addCase(e,i[e]);for(let e of p)t.addMatcher(e.matcher,e.reducer);for(let e of r)t.addMatcher(e.matcher,e.reducer);n&&t.addDefaultCase(n)})}l.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof t.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:t,reducerName:e},r,n,i){if(!i)throw Error(tw(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:u,settled:c,options:s}=r,f=i(t,a,s);n.exposeAction(e,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),u&&n.addCase(f.rejected,u),c&&n.addMatcher(f.settled,c),n.exposeCaseReducer(e,{fulfilled:o||T,pending:l||T,rejected:u||T,settled:c||T})}(o,i,y,e):function({type:t,reducerName:e,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(tw(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(t,a).exposeCaseReducer(e,a).exposeAction(e,o?c(t,o):c(t))}(o,i,y)});let g=t=>t,m=new Map,x=new WeakMap;function w(t,e){return r||(r=v()),r(t,e)}function O(){return r||(r=v()),r.getInitialState()}function j(e,r=!1){function n(t){let i=t[e];return void 0===i&&r&&(i=d(x,n,O)),i}function i(e=g){let n=d(m,r,()=>new WeakMap);return d(n,e,()=>{let n={};for(let[i,a]of Object.entries(t.selectors??{}))n[i]=function(t,e,r,n){function i(a,...o){let l=e(a);return void 0===l&&n&&(l=r()),t(l,...o)}return i.unwrapped=t,i}(a,e,()=>d(x,e,O),r);return n})}return{reducerPath:e,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let P={name:n,reducer:w,actions:f,caseReducers:u,getInitialState:O,...j(i),injectInto(t,{reducerPath:e,...r}={}){let n=e??i;return t.inject({reducerPath:n,reducer:w},r),{...P,...j(n,!0)}}};return P}}();function T(){}function D(t){return function(e,r){let n=e=>{isAction(r)&&Object.keys(r).every(s)?t(r.payload,e):t(r,e)};return(null)(e)?(n(e),e):createNextState3(e,n)}}function N(t,e){return e(t)}function I(t){return Array.isArray(t)||(t=Object.values(t)),t}var z="listener",L="completed",B="cancelled",U=`task-${B}`,R=`task-${L}`,$=`${z}-${B}`,F=`${z}-${L}`,K=class{constructor(t){this.code=t,this.message=`task ${B} (reason: ${t})`}name="TaskAbortError";message},G=(t,e)=>{if("function"!=typeof t)throw TypeError(tw(32))},H=()=>{},W=(t,e=H)=>(t.catch(e),t),V=(t,e)=>(t.addEventListener("abort",e,{once:!0}),()=>t.removeEventListener("abort",e)),q=(t,e)=>{let r=t.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:e,configurable:!0,writable:!0}),t.abort(e))},Z=t=>{if(t.aborted){let{reason:e}=t;throw new K(e)}};function Y(t,e){let r=H;return new Promise((n,i)=>{let a=()=>i(new K(t.reason));if(t.aborted)return void a();r=V(t,a),e.finally(()=>r()).then(n,i)}).finally(()=>{r=H})}var X=async(t,e)=>{try{await Promise.resolve();let e=await t();return{status:"ok",value:e}}catch(t){return{status:t instanceof K?"cancelled":"rejected",error:t}}finally{e?.()}},J=t=>e=>W(Y(t,e).then(e=>(Z(t),e))),Q=t=>{let e=J(t);return t=>e(new Promise(e=>setTimeout(e,t)))},{assign:tt}=Object,te={},tr="listenerMiddleware",tn=(t,e)=>{let r=e=>V(t,()=>q(e,t.reason));return(n,i)=>{G(n,"taskExecutor");let a=new AbortController;r(a);let o=X(async()=>{Z(t),Z(a.signal);let e=await n({pause:J(a.signal),delay:Q(a.signal),signal:a.signal});return Z(a.signal),e},()=>q(a,R));return i?.autoJoin&&e.push(o.catch(H)),{result:J(t)(o),cancel(){q(a,U)}}}},ti=(t,e)=>{let r=async(r,n)=>{Z(e);let i=()=>{},a=[new Promise((e,n)=>{let a=t({predicate:r,effect:(t,r)=>{r.unsubscribe(),e([t,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(t=>setTimeout(t,n,null)));try{let t=await Y(e,Promise.race(a));return Z(e),t}finally{i()}};return(t,e)=>W(r(t,e))},ta=t=>{let{type:e,actionCreator:r,matcher:n,predicate:i,effect:a}=t;if(e)i=c(e).match;else if(r)e=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(tw(21));return G(a,"options.listener"),{predicate:i,type:e,effect:a}},to=tt(t=>{let{type:e,predicate:r,effect:n}=ta(t);return{id:O(),effect:n,type:e,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(tw(22))}}},{withTypes:()=>to}),tl=(t,e)=>{let{type:r,effect:n,predicate:i}=ta(e);return Array.from(t.values()).find(t=>("string"==typeof r?t.type===r:t.predicate===i)&&t.effect===n)},tu=t=>{t.pending.forEach(t=>{q(t,$)})},tc=t=>()=>{t.forEach(tu),t.clear()},ts=(t,e,r)=>{try{t(e,r)}catch(t){setTimeout(()=>{throw t},0)}},tf=tt(c(`${tr}/add`),{withTypes:()=>tf}),th=c(`${tr}/removeAll`),td=tt(c(`${tr}/remove`),{withTypes:()=>td}),tp=(...t)=>{console.error(`${tr}/error`,...t)},ty=(t={})=>{let e=new Map,{extra:r,onError:i=tp}=t;G(i,"onError");let a=t=>(t.unsubscribe=()=>e.delete(t.id),e.set(t.id,t),e=>{t.unsubscribe(),e?.cancelActive&&tu(t)}),o=t=>a(tl(e,t)??to(t));tt(o,{withTypes:()=>o});let l=t=>{let r=tl(e,t);return r&&(r.unsubscribe(),t.cancelActive&&tu(r)),!!r};tt(l,{withTypes:()=>l});let u=async(t,n,a,l)=>{let u=new AbortController,c=ti(o,u.signal),s=[];try{t.pending.add(u),await Promise.resolve(t.effect(n,tt({},a,{getOriginalState:l,condition:(t,e)=>c(t,e).then(Boolean),take:c,delay:Q(u.signal),pause:J(u.signal),extra:r,signal:u.signal,fork:tn(u.signal,s),unsubscribe:t.unsubscribe,subscribe:()=>{e.set(t.id,t)},cancelActiveListeners:()=>{t.pending.forEach((t,e,r)=>{t!==u&&(q(t,$),r.delete(t))})},cancel:()=>{q(u,$),t.pending.delete(u)},throwIfCancelled:()=>{Z(u.signal)}})))}catch(t){t instanceof K||ts(i,t,{raisedBy:"effect"})}finally{await Promise.all(s),q(u,F),t.pending.delete(u)}},c=tc(e);return{middleware:t=>r=>a=>{let s;if(!(0,n.ve)(a))return r(a);if(tf.match(a))return o(a.payload);if(th.match(a))return void c();if(td.match(a))return l(a.payload);let f=t.getState(),h=()=>{if(f===te)throw Error(tw(23));return f};try{if(s=r(a),e.size>0){let r=t.getState();for(let n of Array.from(e.values())){let e=!1;try{e=n.predicate(a,r,f)}catch(t){e=!1,ts(i,t,{raisedBy:"predicate"})}e&&u(n,a,t,h)}}}finally{f=te}return s},startListening:o,stopListening:l,clearListeners:c}},tv=t=>"reducerPath"in t&&"string"==typeof t.reducerPath,tg=Symbol.for("rtk-state-proxy-original"),tm=t=>!!t&&!!t[tg],tb=new WeakMap,tx={};function tw(t){return`Minified Redux Toolkit error #${t}; visit https://redux-toolkit.js.org/Errors?code=${t} for the full message or use the non-minified dev environment for full errors. `}},5714:(t,e,r)=>{"use strict";r.d(e,{J:()=>n});var n=t=>t.tooltip},5989:(t,e,r)=>{"use strict";r.d(e,{N:()=>tF,l:()=>t$});var n=r(2115),i=r(2596),a=r(4679),o=r(3597),l=r(788);function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var c=t=>{var{cx:e,cy:r,r:a,className:c}=t,s=(0,i.$)("recharts-dot",c);return e===+e&&r===+r&&a===+a?n.createElement("circle",u({},(0,l.J9)(t,!1),(0,o._U)(t),{className:s,cx:e,cy:r,r:a})):null},s=r(2348),f=r(8080),h=r.n(f),d=r(379),p=r(9827),y=r(6377),v=["valueAccessor"],g=["data","dataKey","clockWise","id","textBreakAll"];function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function w(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var O=t=>Array.isArray(t.value)?h()(t.value):t.value;function j(t){var{valueAccessor:e=O}=t,r=w(t,v),{data:i,dataKey:a,clockWise:o,id:u,textBreakAll:c}=r,f=w(r,g);return i&&i.length?n.createElement(s.W,{className:"recharts-label-list"},i.map((t,r)=>{var i=(0,y.uy)(a)?e(t,r):(0,p.kr)(t&&t.payload,a),s=(0,y.uy)(u)?{}:{id:"".concat(u,"-").concat(r)};return n.createElement(d.J,m({},(0,l.J9)(t,!0),f,s,{parentViewBox:t.parentViewBox,value:i,textBreakAll:c,viewBox:d.J.parseViewBox((0,y.uy)(o)?t:x(x({},t),{},{clockWise:o})),key:"label-".concat(r),index:r}))})):null}j.displayName="LabelList",j.renderCallByParent=function(t,e){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var{children:a}=t,o=(0,l.aS)(a,j).map((t,r)=>(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)}));return i?[(r=t.label,r?!0===r?n.createElement(j,{key:"labelList-implicit",data:e}):n.isValidElement(r)||(0,d.Z)(r)?n.createElement(j,{key:"labelList-implicit",data:e,content:r}):"object"==typeof r?n.createElement(j,m({data:e},r,{key:"labelList-implicit"})):null:null),...o]:o};var P=r(1971),E=r(2248);function A(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?A(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(t){var e=(0,P.j)();return(0,n.useEffect)(()=>{var r=M(M({},t),{},{stackId:(0,p.$8)(t.stackId)});return e((0,E.g5)(r)),()=>{e((0,E.Vi)(r))}},[e,t]),null}var _=r(1807),k=["children"],C=()=>{},T=(0,n.createContext)({addErrorBar:C,removeErrorBar:C}),D=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function N(t){var{children:e}=t,r=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,k);return n.createElement(D.Provider,{value:r},e)}var I=()=>(0,n.useContext)(D),z=t=>{var{children:e,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,data:l,stackId:u,hide:c,type:s,barSize:f}=t,[h,d]=n.useState([]),p=(0,n.useCallback)(t=>{d(e=>[...e,t])},[d]),y=(0,n.useCallback)(t=>{d(e=>e.filter(e=>e!==t))},[d]),v=(0,_.r)();return n.createElement(T.Provider,{value:{addErrorBar:p,removeErrorBar:y}},n.createElement(S,{type:s,data:l,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,errorBars:h,stackId:u,hide:c,barSize:f,isPanorama:v}),e)};function L(t){var{addErrorBar:e,removeErrorBar:r}=(0,n.useContext)(T);return(0,n.useEffect)(()=>(e(t),()=>{r(t)}),[e,r,t]),null}var B=r(2183),U=t=>{var e=(0,_.r)();return(0,P.G)(r=>(0,B.Gx)(r,"xAxis",t,e))},R=t=>{var e=(0,_.r)();return(0,P.G)(r=>(0,B.Gx)(r,"yAxis",t,e))},$=r(3389),F=r(4460),K=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function G(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function H(){return(H=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function W(t){var{direction:e,width:r,dataKey:i,isAnimationActive:a,animationBegin:o,animationDuration:u,animationEasing:c}=t,f=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,K),h=(0,l.J9)(f,!1),{data:d,dataPointFormatter:p,xAxisId:y,yAxisId:v,errorBarOffset:g}=I(),m=U(y),b=R(v);if((null==m?void 0:m.scale)==null||(null==b?void 0:b.scale)==null||null==d||"x"===e&&"number"!==m.type)return null;var x=d.map(t=>{var l,f,{x:d,y,value:v,errorVal:x}=p(t,i,e);if(!x)return null;var w=[];if(Array.isArray(x)?[l,f]=x:l=f=x,"x"===e){var{scale:O}=m,j=y+g,P=j+r,E=j-r,A=O(v-l),M=O(v+f);w.push({x1:M,y1:P,x2:M,y2:E}),w.push({x1:A,y1:j,x2:M,y2:j}),w.push({x1:A,y1:P,x2:A,y2:E})}else if("y"===e){var{scale:S}=b,_=d+g,k=_-r,C=_+r,T=S(v-l),D=S(v+f);w.push({x1:k,y1:D,x2:C,y2:D}),w.push({x1:_,y1:T,x2:_,y2:D}),w.push({x1:k,y1:T,x2:C,y2:T})}var N="".concat(d+g,"px ").concat(y+g,"px");return n.createElement(s.W,H({className:"recharts-errorBar",key:"bar-".concat(w.map(t=>"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)))},h),w.map(t=>{var e=a?{transformOrigin:"".concat(t.x1-5,"px")}:void 0;return n.createElement(F.i,{from:{transform:"scaleY(0)",transformOrigin:N},to:{transform:"scaleY(1)",transformOrigin:N},begin:o,easing:c,isActive:a,duration:u,key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2),style:{transformOrigin:N}},n.createElement("line",H({},t,{style:e})))}))});return n.createElement(s.W,{className:"recharts-errorBars"},x)}var V=(0,n.createContext)(void 0);function q(t){var{direction:e,children:r}=t;return n.createElement(V.Provider,{value:e},r)}var Z={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function Y(t){var e,r,i=(e=t.direction,r=(0,n.useContext)(V),null!=e?e:null!=r?r:"x"),{width:a,isAnimationActive:o,animationBegin:l,animationDuration:u,animationEasing:c}=(0,$.e)(t,Z);return n.createElement(n.Fragment,null,n.createElement(L,{dataKey:t.dataKey,direction:i}),n.createElement(W,H({},t,{direction:i,width:a,isAnimationActive:o,animationBegin:l,animationDuration:u,animationEasing:c})))}class X extends n.Component{render(){return n.createElement(Y,this.props)}}G(X,"defaultProps",Z),G(X,"displayName","ErrorBar");var J=r(1643),Q=r(1420),tt=r(215);function te(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?te(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):te(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var tn=t=>{var e,{point:r,childIndex:i,mainColor:a,activeDot:u,dataKey:f}=t;if(!1===u||null==r.x||null==r.y)return null;var h=tr(tr({index:i,dataKey:f,cx:r.x,cy:r.y,r:4,fill:null!=a?a:"none",strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,l.J9)(u,!1)),(0,o._U)(u));return e=(0,n.isValidElement)(u)?(0,n.cloneElement)(u,h):"function"==typeof u?u(h):n.createElement(c,h),n.createElement(s.W,{className:"recharts-active-dot"},e)};function ti(t){var e,{points:r,mainColor:n,activeDot:i,itemDataKey:a}=t,o=(0,Q.E)(),l=(0,P.G)(tt.A2),u=(0,P.G)(tt.BZ);if(!l)return null;var c=o.dataKey;if(c&&!o.allowDuplicatedCategory){var s="function"==typeof c?t=>c(t.payload):"payload.".concat(c);e=(0,y.eP)(r,s,u)}else e=null==r?void 0:r[Number(l)];return(0,y.uy)(e)?null:tn({point:e,childIndex:Number(l),mainColor:n,dataKey:a,activeDot:i})}var ta=r(4890);function to(t){var{fn:e,args:r}=t,i=(0,P.j)(),a=(0,_.r)();return(0,n.useEffect)(()=>{if(!a){var t=e(r);return i((0,ta.Ix)(t)),()=>{i((0,ta.XB)(t))}}},[e,r,i,a]),null}var tl=r(7238);function tu(t,e){var r,n,i=(0,P.G)(e=>(0,B.Rl)(e,t)),a=(0,P.G)(t=>(0,B.sf)(t,e)),o=null!=(r=null==i?void 0:i.allowDataOverflow)?r:B.PU.allowDataOverflow,l=null!=(n=null==a?void 0:a.allowDataOverflow)?n:B.cd.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function tc(t){var{xAxisId:e,yAxisId:r,clipPathId:i}=t,a=(0,tl.hj)(),{needClipX:o,needClipY:l,needClip:u}=tu(e,r);if(!u)return null;var{left:c,top:s,width:f,height:h}=a;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:o?c:c-f/2,y:l?s:s-h/2,width:o?f:2*f,height:l?h:2*h}))}var ts=r(8924),tf=r(356),th=(t,e,r,n)=>(0,B.Gx)(t,"xAxis",e,n),td=(t,e,r,n)=>(0,B.CR)(t,"xAxis",e,n),tp=(t,e,r,n)=>(0,B.Gx)(t,"yAxis",r,n),ty=(t,e,r,n)=>(0,B.CR)(t,"yAxis",r,n),tv=(0,ts.Mz)([tl.fz,th,tp,td,ty],(t,e,r,n,i)=>(0,p._L)(t,"xAxis")?(0,p.Hj)(e,n,!1):(0,p.Hj)(r,i,!1)),tg=(0,ts.Mz)([B.ld,(t,e,r,n,i)=>i],(t,e)=>{if(t.some(t=>"line"===t.type&&e.dataKey===t.dataKey&&e.data===t.data))return e}),tm=(0,ts.Mz)([tl.fz,th,tp,td,ty,tg,tv,tf.HS],(t,e,r,n,i,a,o,l)=>{var u,{chartData:c,dataStartIndex:s,dataEndIndex:f}=l;if(null!=a&&null!=e&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=o){var{dataKey:h,data:d}=a;if(null!=(u=null!=d&&d.length>0?d:null==c?void 0:c.slice(s,f+1)))return t$({layout:t,xAxis:e,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:h,bandSize:o,displayedData:u})}}),tb=r(2634),tx=()=>{};function tw(t){var{legendPayload:e}=t,r=(0,P.j)(),i=(0,_.r)();return(0,n.useEffect)(()=>i?tx:(r((0,tb.Lx)(e)),()=>{r((0,tb.u3)(e))}),[r,i,e]),null}var tO=["type","layout","connectNulls","needClip"],tj=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function tP(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function tE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tE(Object(r),!0).forEach(function(e){tM(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tM(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tS(){return(tS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var t_=t=>{var{dataKey:e,name:r,stroke:n,legendType:i,hide:a}=t;return[{inactive:a,dataKey:e,type:i,color:n,value:(0,p.uM)(r,e),payload:t}]};function tk(t){var{dataKey:e,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:u}=t;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:e,nameKey:void 0,name:(0,p.uM)(o,e),hide:l,type:t.tooltipType,color:t.stroke,unit:u}}}var tC=(t,e)=>"".concat(e,"px ").concat(t-e,"px"),tT=(t,e,r)=>{var n=r.reduce((t,e)=>t+e);if(!n)return tC(e,t);for(var i=Math.floor(t/n),a=t%n,o=e-t,l=[],u=0,c=0;u<r.length;c+=r[u],++u)if(c+r[u]>a){l=[...r.slice(0,u),a-c];break}var s=l.length%2==0?[0,o]:[o];return[...function(t,e){for(var r=t.length%2!=0?[...t,0]:t,n=[],i=0;i<e;++i)n=[...n,...r];return n}(r,i),...l,...s].map(t=>"".concat(t,"px")).join(", ")};function tD(t){var{clipPathId:e,points:r,props:a}=t,{dot:o,dataKey:u,needClip:f}=a;if(null==r||!o&&1!==r.length)return null;var h=(0,l.y$)(o),d=(0,l.J9)(a,!1),p=(0,l.J9)(o,!0),y=r.map((t,e)=>{var a,l=tA(tA(tA({key:"dot-".concat(e),r:3},d),p),{},{index:e,cx:t.x,cy:t.y,dataKey:u,value:t.value,payload:t.payload,points:r});if(n.isValidElement(o))a=n.cloneElement(o,l);else if("function"==typeof o)a=o(l);else{var s=(0,i.$)("recharts-line-dot","boolean"!=typeof o?o.className:"");a=n.createElement(c,tS({},l,{className:s}))}return a}),v={clipPath:f?"url(#clipPath-".concat(h?"":"dots-").concat(e,")"):null};return n.createElement(s.W,tS({className:"recharts-line-dots",key:"dots"},v),y)}function tN(t){var{clipPathId:e,pathRef:r,points:i,strokeDasharray:o,props:u,showLabels:c}=t,{type:s,layout:f,connectNulls:h,needClip:d}=u,p=tP(u,tO),y=tA(tA({},(0,l.J9)(p,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:d?"url(#clipPath-".concat(e,")"):null,points:i,type:s,layout:f,connectNulls:h,strokeDasharray:null!=o?o:u.strokeDasharray});return n.createElement(n.Fragment,null,(null==i?void 0:i.length)>1&&n.createElement(a.I,tS({},y,{pathRef:r})),n.createElement(tD,{points:i,clipPathId:e,props:u}),c&&j.renderCallByParent(u,i))}function tI(t){var{clipPathId:e,props:r,pathRef:i,previousPointsRef:a,longestAnimatedLengthRef:o}=t,{points:l,strokeDasharray:u,isAnimationActive:c,animationBegin:s,animationDuration:f,animationEasing:h,animateNewValues:d,width:p,height:v,onAnimationEnd:g,onAnimationStart:m}=r,b=a.current,x=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,y.NF)(e)),i=(0,n.useRef)(t);return i.current!==t&&(r.current=(0,y.NF)(e),i.current=t),r.current}(r,"recharts-line-"),[w,O]=(0,n.useState)(!1),j=(0,n.useCallback)(()=>{"function"==typeof g&&g(),O(!1)},[g]),P=(0,n.useCallback)(()=>{"function"==typeof m&&m(),O(!0)},[m]),E=function(t){try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}(i.current),A=o.current;return n.createElement(F.i,{begin:s,duration:f,isActive:c,easing:h,from:{t:0},to:{t:1},onAnimationEnd:j,onAnimationStart:P,key:x},t=>{var c,{t:s}=t,f=Math.min((0,y.Dj)(A,E+A)(s),E);if(c=u?tT(f,E,"".concat(u).split(/[,\s]+/gim).map(t=>parseFloat(t))):tC(E,f),b){var h=b.length/l.length,g=1===s?l:l.map((t,e)=>{var r=Math.floor(e*h);if(b[r]){var n=b[r],i=(0,y.Dj)(n.x,t.x),a=(0,y.Dj)(n.y,t.y);return tA(tA({},t),{},{x:i(s),y:a(s)})}if(d){var o=(0,y.Dj)(2*p,t.x),l=(0,y.Dj)(v/2,t.y);return tA(tA({},t),{},{x:o(s),y:l(s)})}return tA(tA({},t),{},{x:t.x,y:t.y})});return a.current=g,n.createElement(tN,{props:r,points:g,clipPathId:e,pathRef:i,showLabels:!w,strokeDasharray:c})}return s>0&&E>0&&(a.current=l,o.current=f),n.createElement(tN,{props:r,points:l,clipPathId:e,pathRef:i,showLabels:!w,strokeDasharray:c})})}function tz(t){var{clipPathId:e,props:r}=t,{points:i,isAnimationActive:a}=r,o=(0,n.useRef)(null),l=(0,n.useRef)(0),u=(0,n.useRef)(null),c=o.current;return a&&i&&i.length&&c!==i?n.createElement(tI,{props:r,clipPathId:e,previousPointsRef:o,longestAnimatedLengthRef:l,pathRef:u}):n.createElement(tN,{props:r,points:i,clipPathId:e,pathRef:u,showLabels:!0})}var tL=(t,e)=>({x:t.x,y:t.y,value:t.value,errorVal:(0,p.kr)(t.payload,e)});class tB extends n.Component{render(){var t,{hide:e,dot:r,points:a,className:o,xAxisId:u,yAxisId:c,top:f,left:h,width:d,height:p,id:v,needClip:g,layout:m}=this.props;if(e)return null;var b=(0,i.$)("recharts-line",o),x=(0,y.uy)(v)?this.id:v,{r:w=3,strokeWidth:O=2}=null!=(t=(0,l.J9)(r,!1))?t:{r:3,strokeWidth:2},j=(0,l.y$)(r),P=2*w+O;return n.createElement(n.Fragment,null,n.createElement(s.W,{className:b},g&&n.createElement("defs",null,n.createElement(tc,{clipPathId:x,xAxisId:u,yAxisId:c}),!j&&n.createElement("clipPath",{id:"clipPath-dots-".concat(x)},n.createElement("rect",{x:h-P/2,y:f-P/2,width:d+P,height:p+P}))),n.createElement(tz,{props:this.props,clipPathId:x}),n.createElement(q,{direction:"horizontal"===m?"y":"x"},n.createElement(N,{xAxisId:u,yAxisId:c,data:a,dataPointFormatter:tL,errorBarOffset:0},this.props.children))),n.createElement(ti,{activeDot:this.props.activeDot,points:a,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}constructor(){super(...arguments),tM(this,"id",(0,y.NF)("recharts-line-"))}}var tU={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!J.m.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function tR(t){var e=(0,$.e)(t,tU),{activeDot:r,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:l,connectNulls:u,dot:c,hide:s,isAnimationActive:f,label:h,legendType:d,xAxisId:p,yAxisId:y}=e,v=tP(e,tj),{needClip:g}=tu(p,y),{height:m,width:b,left:x,top:w}=(0,tl.hj)(),O=(0,tl.WX)(),j=(0,_.r)(),E=(0,n.useMemo)(()=>({dataKey:t.dataKey,data:t.data}),[t.dataKey,t.data]),A=(0,P.G)(t=>tm(t,p,y,j,E));return"horizontal"!==O&&"vertical"!==O?null:n.createElement(tB,tS({},v,{connectNulls:u,dot:c,activeDot:r,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:l,isAnimationActive:f,hide:s,label:h,legendType:d,xAxisId:p,yAxisId:y,points:A,layout:O,height:m,width:b,left:x,top:w,needClip:g}))}function t$(t){var{layout:e,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:l,displayedData:u}=t;return u.map((t,u)=>{var c=(0,p.kr)(t,o);return"horizontal"===e?{x:(0,p.nb)({axis:r,ticks:i,bandSize:l,entry:t,index:u}),y:(0,y.uy)(c)?null:n.scale(c),value:c,payload:t}:{x:(0,y.uy)(c)?null:r.scale(c),y:(0,p.nb)({axis:n,ticks:a,bandSize:l,entry:t,index:u}),value:c,payload:t}})}class tF extends n.PureComponent{render(){return n.createElement(z,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},n.createElement(tw,{legendPayload:t_(this.props)}),n.createElement(to,{fn:tk,args:this.props}),n.createElement(tR,this.props))}}tM(tF,"displayName","Line"),tM(tF,"defaultProps",tU)},5998:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.flatten=function(t,e=1){let r=[],n=Math.floor(e),i=(t,e)=>{for(let a=0;a<t.length;a++){let o=t[a];Array.isArray(o)&&e<n?i(o,e+1):r.push(o)}};return i(t,0),r}},6025:(t,e,r)=>{"use strict";r.d(e,{W:()=>b});var n=r(2115),i=r(2596),a=r(9584),o=r(1971),l=r(5306),u=r(2183),c=r(9449),s=r(1807),f=["children"],h=["dangerouslySetInnerHTML","ticks"];function d(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function y(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function v(t){var e=(0,o.j)(),r=(0,n.useMemo)(()=>{var{children:e}=t;return y(t,f)},[t]),i=(0,o.G)(t=>(0,u.Rl)(t,r.id)),a=r===i;return((0,n.useEffect)(()=>(e((0,l.Vi)(r)),()=>{e((0,l.MC)(r))}),[r,e]),a)?t.children:null}var g=t=>{var{xAxisId:e,className:r}=t,l=(0,o.G)(c.c2),f=(0,s.r)(),d="xAxis",v=(0,o.G)(t=>(0,u.iV)(t,d,e,f)),g=(0,o.G)(t=>(0,u.Zi)(t,d,e,f)),m=(0,o.G)(t=>(0,u.Lw)(t,e)),b=(0,o.G)(t=>(0,u.L$)(t,e));if(null==m||null==b)return null;var{dangerouslySetInnerHTML:x,ticks:w}=t,O=y(t,h);return n.createElement(a.u,p({},O,{scale:v,x:b.x,y:b.y,width:m.width,height:m.height,className:(0,i.$)("recharts-".concat(d," ").concat(d),r),viewBox:l,ticks:g}))},m=t=>{var e,r,i,a,o;return n.createElement(v,{interval:null!=(e=t.interval)?e:"preserveEnd",id:t.xAxisId,scale:t.scale,type:t.type,padding:t.padding,allowDataOverflow:t.allowDataOverflow,domain:t.domain,dataKey:t.dataKey,allowDuplicatedCategory:t.allowDuplicatedCategory,allowDecimals:t.allowDecimals,tickCount:t.tickCount,includeHidden:null!=(r=t.includeHidden)&&r,reversed:t.reversed,ticks:t.ticks,height:t.height,orientation:t.orientation,mirror:t.mirror,hide:t.hide,unit:t.unit,name:t.name,angle:null!=(i=t.angle)?i:0,minTickGap:null!=(a=t.minTickGap)?a:5,tick:null==(o=t.tick)||o,tickFormatter:t.tickFormatter},n.createElement(g,t))};class b extends n.Component{render(){return n.createElement(m,this.props)}}d(b,"displayName","XAxis"),d(b,"defaultProps",{allowDataOverflow:u.PU.allowDataOverflow,allowDecimals:u.PU.allowDecimals,allowDuplicatedCategory:u.PU.allowDuplicatedCategory,height:u.PU.height,hide:!1,mirror:u.PU.mirror,orientation:u.PU.orientation,padding:u.PU.padding,reversed:u.PU.reversed,scale:u.PU.scale,tickCount:u.PU.tickCount,type:u.PU.type,xAxisId:0})},6115:(t,e,r)=>{"use strict";var n=r(2115),i=r(9033),a="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},o=i.useSyncExternalStore,l=n.useRef,u=n.useEffect,c=n.useMemo,s=n.useDebugValue;e.useSyncExternalStoreWithSelector=function(t,e,r,n,i){var f=l(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var d=o(t,(f=c(function(){function t(t){if(!u){if(u=!0,o=t,t=n(t),void 0!==i&&h.hasValue){var e=h.value;if(i(e,t))return l=e}return l=t}if(e=l,a(o,t))return e;var r=n(t);return void 0!==i&&i(e,r)?(o=t,e):(o=t,l=r)}var o,l,u=!1,c=void 0===r?null:r;return[function(){return t(e())},null===c?void 0:function(){return t(c())}]},[e,r,n,i]))[0],f[1]);return u(function(){h.hasValue=!0,h.value=d},[d]),s(d),d}},6200:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toKey=function(t){return"string"==typeof t||"symbol"==typeof t?t:Object.is(t?.valueOf?.(),-0)?"-0":String(t)}},6377:(t,e,r)=>{"use strict";r.d(e,{CG:()=>d,Dj:()=>p,Et:()=>u,F4:()=>h,M8:()=>o,NF:()=>f,Zb:()=>g,_3:()=>l,eP:()=>y,sA:()=>a,uy:()=>v,vh:()=>c});var n=r(5672),i=r.n(n),a=t=>0===t?0:t>0?1:-1,o=t=>"number"==typeof t&&t!=+t,l=t=>"string"==typeof t&&t.indexOf("%")===t.length-1,u=t=>("number"==typeof t||t instanceof Number)&&!o(t),c=t=>u(t)||"string"==typeof t,s=0,f=t=>{var e=++s;return"".concat(t||"").concat(e)},h=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!u(t)&&"string"!=typeof t)return n;if(l(t)){if(null==e)return n;var a=t.indexOf("%");r=e*parseFloat(t.slice(0,a))/100}else r=+t;return o(r)&&(r=n),i&&null!=e&&r>e&&(r=e),r},d=t=>{if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},p=(t,e)=>u(t)&&u(e)?r=>t+r*(e-t):()=>e;function y(t,e,r){if(t&&t.length)return t.find(t=>t&&("function"==typeof e?e(t):i()(t,e))===r)}var v=t=>null==t,g=t=>v(t)?t:"".concat(t.charAt(0).toUpperCase()).concat(t.slice(1))},6523:(t,e,r)=>{"use strict";r.d(e,{$g:()=>o,Hw:()=>a,Td:()=>u,au:()=>l,xH:()=>i});var n=r(1971),i=t=>t.options.defaultTooltipEventType,a=t=>t.options.validateTooltipEventTypes;function o(t,e,r){if(null==t)return e;var n=t?"axis":"item";return null==r?e:r.includes(n)?n:e}function l(t,e){return o(e,i(t),a(t))}function u(t){return(0,n.G)(e=>l(e,t))}},6605:(t,e,r)=>{"use strict";r.d(e,{P:()=>c});var n=r(1643);function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var o={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},u="recharts_measurement_span",c=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(e=a({},r)).forEach(t=>{e[t]||delete e[t]}),e),c=JSON.stringify({text:t,copyStyle:i});if(o.widthCache[c])return o.widthCache[c];try{var s=document.getElementById(u);s||((s=document.createElement("span")).setAttribute("id",u),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=a(a({},l),i);Object.assign(s.style,f),s.textContent="".concat(t);var h=s.getBoundingClientRect(),d={width:h.width,height:h.height};return o.widthCache[c]=d,++o.cacheCount>2e3&&(o.cacheCount=0,o.widthCache={}),d}catch(t){return{width:0,height:0}}}},6633:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPrimitive=function(t){return null==t||"object"!=typeof t&&"function"!=typeof t}},6641:(t,e,r)=>{"use strict";r.d(e,{dl:()=>u,lJ:()=>l,uN:()=>a});var n=r(5710),i=r(6377);function a(t,e){if(e){var r=Number.parseInt(e,10);if(!(0,i.M8)(r))return null==t?void 0:t[r]}}var o=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:t=>{null==t.eventEmitter&&(t.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:u}=o.actions},6670:(t,e,r)=>{"use strict";r.d(e,{x:()=>n});var n=t=>t.options.tooltipPayloadSearcher},6752:(t,e,r)=>{"use strict";r.d(e,{$:()=>i});var n=r(1971),i=()=>(0,n.G)(t=>t.rootProps.accessibilityLayer)},6850:(t,e,r)=>{"use strict";r.d(e,{l3:()=>g,m7:()=>m});var n=r(2115),i=r(1971),a=r(8478),o=new(r(2661)),l="recharts.syncEvent.tooltip",u="recharts.syncEvent.brush",c=r(6641),s=r(4890),f=r(4732),h=r(215);function d(t){return t.tooltip.syncInteraction}var p=r(7238),y=r(4487),v=()=>{};function g(){var t,e,r,f,d,g,m,b,x,w,O,j=(0,i.j)();(0,n.useEffect)(()=>{j((0,c.dl)())},[j]),t=(0,i.G)(a.lZ),e=(0,i.G)(a.pH),r=(0,i.j)(),f=(0,i.G)(a.hX),d=(0,i.G)(h.R4),g=(0,p.WX)(),m=(0,p.sk)(),b=(0,i.G)(t=>t.rootProps.className),(0,n.useEffect)(()=>{if(null==t)return v;var n=(n,i,a)=>{if(e!==a&&t===n){if("index"===f)return void r(i);if(null!=d){if("function"==typeof f){var o,l=f(d,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});o=d[l]}else"value"===f&&(o=d.find(t=>String(t.value)===i.payload.label));var{coordinate:u}=i.payload;if(null==o||!1===i.payload.active||null==u||null==m)return void r((0,s.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:c,y:h}=u,p=Math.min(c,m.x+m.width),y=Math.min(h,m.y+m.height),v={x:"horizontal"===g?o.coordinate:p,y:"horizontal"===g?y:o.coordinate};r((0,s.E1)({active:i.payload.active,coordinate:v,dataKey:i.payload.dataKey,index:String(o.index),label:i.payload.label}))}}};return o.on(l,n),()=>{o.off(l,n)}},[b,r,e,t,f,d,g,m]),x=(0,i.G)(a.lZ),w=(0,i.G)(a.pH),O=(0,i.j)(),(0,n.useEffect)(()=>{if(null==x)return v;var t=(t,e,r)=>{w!==r&&x===t&&O((0,y.M)(e))};return o.on(u,t),()=>{o.off(u,t)}},[O,w,x])}function m(t,e,r,u,c,h){var p=(0,i.G)(r=>(0,f.dp)(r,t,e)),y=(0,i.G)(a.pH),v=(0,i.G)(a.lZ),g=(0,i.G)(a.hX),m=(0,i.G)(d),b=null==m?void 0:m.active;(0,n.useEffect)(()=>{if(!b&&null!=v&&null!=y){var t=(0,s.E1)({active:h,coordinate:r,dataKey:p,index:c,label:"number"==typeof u?String(u):u});o.emit(l,v,t,y)}},[b,r,p,c,u,y,v,g,h])}},6908:(t,e,r)=>{"use strict";r.d(e,{W:()=>a,h:()=>i});var n=r(8924),i=(0,n.Mz)(t=>t.cartesianAxis.xAxis,t=>Object.values(t)),a=(0,n.Mz)(t=>t.cartesianAxis.yAxis,t=>Object.values(t))},7040:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isUnsafeProperty=function(t){return"__proto__"===t}},7062:(t,e,r)=>{"use strict";r.d(e,{Be:()=>v,Cv:()=>O,D0:()=>P,Gl:()=>g,Dc:()=>j});var n=r(8924),i=r(2589),a=r(9449),o=r(5641),l=r(6377),u={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},c={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},s=r(8190),f=r(7238),h={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:u.reversed,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:u.type,unit:void 0},d={allowDataOverflow:c.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:c.tickCount,ticks:void 0,type:c.type,unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:c.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:c.tickCount,ticks:void 0,type:"category",unit:void 0},v=(t,e)=>null!=t.polarAxis.angleAxis[e]?t.polarAxis.angleAxis[e]:"radial"===t.layout.layoutType?p:h,g=(t,e)=>null!=t.polarAxis.radiusAxis[e]?t.polarAxis.radiusAxis[e]:"radial"===t.layout.layoutType?y:d,m=t=>t.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,a.GO],o.lY),x=(0,n.Mz)([m,b],(t,e)=>{if(null!=t)return(0,l.F4)(t.innerRadius,e,0)}),w=(0,n.Mz)([m,b],(t,e)=>{if(null!=t)return(0,l.F4)(t.outerRadius,e,.8*e)}),O=(0,n.Mz)([m],t=>{if(null==t)return[0,0];var{startAngle:e,endAngle:r}=t;return[e,r]});(0,n.Mz)([v,O],s.I);var j=(0,n.Mz)([b,x,w],(t,e,r)=>{if(null!=t&&null!=e&&null!=r)return[e,r]});(0,n.Mz)([g,j],s.I);var P=(0,n.Mz)([f.fz,m,x,w,i.Lp,i.A$],(t,e,r,n,i,a)=>{if(("centric"===t||"radial"===t)&&null!=e&&null!=r&&null!=n){var{cx:o,cy:u,startAngle:c,endAngle:s}=e;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(u,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}})},7064:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(5181),i=r(1551),a=r(4072);e.orderBy=function(t,e,r,o){if(null==t)return[];r=o?void 0:r,Array.isArray(t)||(t=Object.values(t)),Array.isArray(e)||(e=null==e?[null]:[e]),0===e.length&&(e=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(t=>String(t));let l=(t,e)=>{let r=t;for(let t=0;t<e.length&&null!=r;++t)r=r[e[t]];return r},u=(t,e)=>null==e||null==t?e:"object"==typeof t&&"key"in t?Object.hasOwn(e,t.key)?e[t.key]:l(e,t.path):"function"==typeof t?t(e):Array.isArray(t)?l(e,t):"object"==typeof e?e[t]:e,c=e.map(t=>(Array.isArray(t)&&1===t.length&&(t=t[0]),null==t||"function"==typeof t||Array.isArray(t)||i.isKey(t))?t:{key:t,path:a.toPath(t)});return t.map(t=>({original:t,criteria:c.map(e=>u(e,t))})).slice().sort((t,e)=>{for(let i=0;i<c.length;i++){let a=n.compareValues(t.criteria[i],e.criteria[i],r[i]);if(0!==a)return a}return 0}).map(t=>t.original)}},7238:(t,e,r)=>{"use strict";r.d(e,{WX:()=>p,fz:()=>d,hj:()=>s,rY:()=>h,sk:()=>u,yi:()=>f}),r(2115);var n=r(1971),i=r(9449),a=r(2589),o=r(1807),l=r(972),u=()=>{var t,e=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(l.U),u=null==(t=(0,n.G)(l.C))?void 0:t.padding;return e&&a&&u?{width:a.width-u.left-u.right,height:a.height-u.top-u.bottom,x:u.left,y:u.top}:r},c={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var t;return null!=(t=(0,n.G)(i.GO))?t:c},f=()=>(0,n.G)(a.Lp),h=()=>(0,n.G)(a.A$),d=t=>t.layout.layoutType,p=()=>(0,n.G)(d)},7298:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(9738);e.cloneDeep=function(t){return n.cloneDeepWith(t)}},7547:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(3676),i=r(2465),a=r(656),o=r(1571);e.uniqBy=function(t,e=i.identity){return a.isArrayLikeObject(t)?n.uniqBy(Array.from(t),o.iteratee(e)):[]}},8080:(t,e,r)=>{t.exports=r(8359).last},8132:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(2744);e.isMatch=function(t,e){return n.isMatchWith(t,e,()=>void 0)}},8179:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(9452);e.isArrayLike=function(t){return null!=t&&"function"!=typeof t&&n.isLength(t.length)}},8190:(t,e,r)=>{"use strict";r.d(e,{I:()=>n});var n=(t,e)=>{if(t&&e)return null!=t&&t.reversed?[e[1],e[0]]:e}},8221:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.getSymbols=function(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}},8359:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(220),i=r(4986),a=r(8179);e.last=function(t){if(a.isArrayLike(t))return n.last(i.toArray(t))}},8412:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;e.isIndex=function(t,e=Number.MAX_SAFE_INTEGER){switch(typeof t){case"number":return Number.isInteger(t)&&t>=0&&t<e;case"symbol":return!1;case"string":return r.test(t)}}},8478:(t,e,r)=>{"use strict";r.d(e,{eC:()=>i,gY:()=>n,hX:()=>l,iO:()=>a,lZ:()=>o,pH:()=>u});var n=t=>t.rootProps.barCategoryGap,i=t=>t.rootProps.stackOffset,a=t=>t.options.chartName,o=t=>t.rootProps.syncId,l=t=>t.rootProps.syncMethod,u=t=>t.options.eventEmitter},8673:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(564);e.debounce=function(t,e=0,r={}){let i;"object"!=typeof r&&(r={});let{leading:a=!1,trailing:o=!0,maxWait:l}=r,u=[,,];a&&(u[0]="leading"),o&&(u[1]="trailing");let c=null,s=n.debounce(function(...e){i=t.apply(this,e),c=null},e,{edges:u}),f=function(...e){return null!=l&&(null===c&&(c=Date.now()),Date.now()-c>=l)?(i=t.apply(this,e),c=Date.now(),s.cancel(),s.schedule(),i):(s.apply(this,e),i)};return f.cancel=s.cancel,f.flush=()=>(s.flush(),i),f}},8870:function(t,e,r){var n;!function(i){"use strict";var a,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,u="[DecimalError] ",c=u+"Invalid argument: ",s=u+"Exponent out of range: ",f=Math.floor,h=Math.pow,d=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,p=f(1286742750677284.5),y={};function v(t,e){var r,n,i,a,o,u,c,s,f=t.constructor,h=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),l?A(e,h):e;if(c=t.d,s=e.d,o=t.e,i=e.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),a>(u=(o=Math.ceil(h/7))>u?o+1:u+1)&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((u=c.length)-(a=s.length)<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/1e7|0,c[a]%=1e7;for(r&&(c.unshift(r),++i),u=c.length;0==c[--u];)c.pop();return e.d=c,e.e=i,l?A(e,h):e}function g(t,e,r){if(t!==~~t||t<e||t>r)throw Error(c+t)}function m(t){var e,r,n,i=t.length-1,a="",o=t[0];if(i>0){for(a+=o,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(a+=j(r)),a+=n;(r=7-(n=(o=t[e])+"").length)&&(a+=j(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}y.absoluteValue=y.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},y.comparedTo=y.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},y.dividedBy=y.div=function(t){return b(this,new this.constructor(t))},y.dividedToIntegerBy=y.idiv=function(t){var e=this.constructor;return A(b(this,new e(t),0,1),e.precision)},y.equals=y.eq=function(t){return!this.cmp(t)},y.exponent=function(){return w(this)},y.greaterThan=y.gt=function(t){return this.cmp(t)>0},y.greaterThanOrEqualTo=y.gte=function(t){return this.cmp(t)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(t){return 0>this.cmp(t)},y.lessThanOrEqualTo=y.lte=function(t){return 1>this.cmp(t)},y.logarithm=y.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(a))throw Error(u+"NaN");if(this.s<1)throw Error(u+(this.s?"NaN":"-Infinity"));return this.eq(a)?new r(0):(l=!1,e=b(P(this,i),P(t,i),i),l=!0,A(e,n))},y.minus=y.sub=function(t){return t=new this.constructor(t),this.s==t.s?M(this,t):v(this,(t.s=-t.s,t))},y.modulo=y.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(u+"NaN");return this.s?(l=!1,e=b(this,t,0,1).times(t),l=!0,this.minus(e)):A(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return P(this)},y.negated=y.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},y.plus=y.add=function(t){return t=new this.constructor(t),this.s==t.s?v(this,t):M(this,(t.s=-t.s,t))},y.precision=y.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(c+t);if(e=w(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},y.squareRoot=y.sqrt=function(){var t,e,r,n,i,a,o,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(u+"NaN")}for(t=w(this),l=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=m(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),n=new c(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(i.toString()),i=o=(r=c.precision)+3;;)if(n=(a=n).plus(b(this,a,o+2)).times(.5),m(a.d).slice(0,o)===(e=m(n.d)).slice(0,o)){if(e=e.slice(o-3,o+1),i==o&&"4999"==e){if(A(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=e)break;o+=4}return l=!0,A(n,r)},y.times=y.mul=function(t){var e,r,n,i,a,o,u,c,s,f=this.constructor,h=this.d,d=(t=new f(t)).d;if(!this.s||!t.s)return new f(0);for(t.s*=this.s,r=this.e+t.e,(c=h.length)<(s=d.length)&&(a=h,h=d,d=a,o=c,c=s,s=o),a=[],n=o=c+s;n--;)a.push(0);for(n=s;--n>=0;){for(e=0,i=c+n;i>n;)u=a[i]+d[n]*h[i-n-1]+e,a[i--]=u%1e7|0,e=u/1e7|0;a[i]=(a[i]+e)%1e7|0}for(;!a[--o];)a.pop();return e?++r:a.shift(),t.d=a,t.e=r,l?A(t,f.precision):t},y.toDecimalPlaces=y.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(g(t,0,1e9),void 0===e?e=n.rounding:g(e,0,8),A(r,t+w(r)+1,e))},y.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=S(n,!0):(g(t,0,1e9),void 0===e?e=i.rounding:g(e,0,8),r=S(n=A(new i(n),t+1,e),!0,t+1)),r},y.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?S(this):(g(t,0,1e9),void 0===e?e=i.rounding:g(e,0,8),r=S((n=A(new i(this),t+w(this)+1,e)).abs(),!1,t+w(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var t=this.constructor;return A(new t(this),w(this)+1,t.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(t){var e,r,n,i,o,c,s=this,h=s.constructor,d=+(t=new h(t));if(!t.s)return new h(a);if(!(s=new h(s)).s){if(t.s<1)throw Error(u+"Infinity");return s}if(s.eq(a))return s;if(n=h.precision,t.eq(a))return A(s,n);if(c=(e=t.e)>=(r=t.d.length-1),o=s.s,c){if((r=d<0?-d:d)<=0x1fffffffffffff){for(i=new h(a),e=Math.ceil(n/7+4),l=!1;r%2&&_((i=i.times(s)).d,e),0!==(r=f(r/2));)_((s=s.times(s)).d,e);return l=!0,t.s<0?new h(a).div(i):A(i,n)}}else if(o<0)throw Error(u+"NaN");return o=o<0&&1&t.d[Math.max(e,r)]?-1:1,s.s=1,l=!1,i=t.times(P(s,n+12)),l=!0,(i=x(i)).s=o,i},y.toPrecision=function(t,e){var r,n,i=this,a=i.constructor;return void 0===t?(r=w(i),n=S(i,r<=a.toExpNeg||r>=a.toExpPos)):(g(t,1,1e9),void 0===e?e=a.rounding:g(e,0,8),r=w(i=A(new a(i),t,e)),n=S(i,t<=r||r<=a.toExpNeg,t)),n},y.toSignificantDigits=y.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(g(t,1,1e9),void 0===e?e=r.rounding:g(e,0,8)),A(new r(this),t,e)},y.toString=y.valueOf=y.val=y.toJSON=function(){var t=w(this),e=this.constructor;return S(this,t<=e.toExpNeg||t>=e.toExpPos)};var b=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(t[i]!=e[i]){a=t[i]>e[i]?1:-1;break}return a}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,a,o){var l,c,s,f,h,d,p,y,v,g,m,b,x,O,j,P,E,M,S=n.constructor,_=n.s==i.s?1:-1,k=n.d,C=i.d;if(!n.s)return new S(n);if(!i.s)throw Error(u+"Division by zero");for(s=0,c=n.e-i.e,E=C.length,j=k.length,y=(p=new S(_)).d=[];C[s]==(k[s]||0);)++s;if(C[s]>(k[s]||0)&&--c,(b=null==a?a=S.precision:o?a+(w(n)-w(i))+1:a)<0)return new S(0);if(b=b/7+2|0,s=0,1==E)for(f=0,C=C[0],b++;(s<j||f)&&b--;s++)x=1e7*f+(k[s]||0),y[s]=x/C|0,f=x%C|0;else{for((f=1e7/(C[0]+1)|0)>1&&(C=t(C,f),k=t(k,f),E=C.length,j=k.length),O=E,g=(v=k.slice(0,E)).length;g<E;)v[g++]=0;(M=C.slice()).unshift(0),P=C[0],C[1]>=1e7/2&&++P;do f=0,(l=e(C,v,E,g))<0?(m=v[0],E!=g&&(m=1e7*m+(v[1]||0)),(f=m/P|0)>1?(f>=1e7&&(f=1e7-1),d=(h=t(C,f)).length,g=v.length,1==(l=e(h,v,d,g))&&(f--,r(h,E<d?M:C,d))):(0==f&&(l=f=1),h=C.slice()),(d=h.length)<g&&h.unshift(0),r(v,h,g),-1==l&&(g=v.length,(l=e(C,v,E,g))<1&&(f++,r(v,E<g?M:C,g))),g=v.length):0===l&&(f++,v=[0]),y[s++]=f,l&&v[0]?v[g++]=k[O]||0:(v=[k[O]],g=1);while((O++<j||void 0!==v[0])&&b--)}return y[0]||y.shift(),p.e=c,A(p,o?a+w(p)+1:a)}}();function x(t,e){var r,n,i,o,u,c=0,f=0,d=t.constructor,p=d.precision;if(w(t)>16)throw Error(s+w(t));if(!t.s)return new d(a);for(null==e?(l=!1,u=p):u=e,o=new d(.03125);t.abs().gte(.1);)t=t.times(o),f+=5;for(u+=Math.log(h(2,f))/Math.LN10*2+5|0,r=n=i=new d(a),d.precision=u;;){if(n=A(n.times(t),u),r=r.times(++c),m((o=i.plus(b(n,r,u))).d).slice(0,u)===m(i.d).slice(0,u)){for(;f--;)i=A(i.times(i),u);return d.precision=p,null==e?(l=!0,A(i,p)):i}i=o}}function w(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function O(t,e,r){if(e>t.LN10.sd())throw l=!0,r&&(t.precision=r),Error(u+"LN10 precision limit exceeded");return A(new t(t.LN10),e)}function j(t){for(var e="";t--;)e+="0";return e}function P(t,e){var r,n,i,o,c,s,f,h,d,p=1,y=t,v=y.d,g=y.constructor,x=g.precision;if(y.s<1)throw Error(u+(y.s?"NaN":"-Infinity"));if(y.eq(a))return new g(0);if(null==e?(l=!1,h=x):h=e,y.eq(10))return null==e&&(l=!0),O(g,h);if(g.precision=h+=10,n=(r=m(v)).charAt(0),!(15e14>Math.abs(o=w(y))))return f=O(g,h+2,x).times(o+""),y=P(new g(n+"."+r.slice(1)),h-10).plus(f),g.precision=x,null==e?(l=!0,A(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((y=y.times(t)).d)).charAt(0),p++;for(o=w(y),n>1?(y=new g("0."+r),o++):y=new g(n+"."+r.slice(1)),s=c=y=b(y.minus(a),y.plus(a),h),d=A(y.times(y),h),i=3;;){if(c=A(c.times(d),h),m((f=s.plus(b(c,new g(i),h))).d).slice(0,h)===m(s.d).slice(0,h))return s=s.times(2),0!==o&&(s=s.plus(O(g,h+2,x).times(o+""))),s=b(s,new g(p),h),g.precision=x,null==e?(l=!0,A(s,x)):s;s=f,i+=2}}function E(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t.e=f((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),l&&(t.e>p||t.e<-p))throw Error(s+r)}else t.s=0,t.e=0,t.d=[0];return t}function A(t,e,r){var n,i,a,o,u,c,d,y,v=t.d;for(o=1,a=v[0];a>=10;a/=10)o++;if((n=e-o)<0)n+=7,i=e,d=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=v.length))return t;for(o=1,d=a=v[y];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(u=d/(a=h(10,o-i-1))%10|0,c=e<0||void 0!==v[y+1]||d%a,c=r<4?(u||c)&&(0==r||r==(t.s<0?3:2)):u>5||5==u&&(4==r||c||6==r&&(n>0?i>0?d/h(10,o-i):0:v[y-1])%10&1||r==(t.s<0?8:7))),e<1||!v[0])return c?(a=w(t),v.length=1,e=e-a-1,v[0]=h(10,(7-e%7)%7),t.e=f(-e/7)||0):(v.length=1,v[0]=t.e=t.s=0),t;if(0==n?(v.length=y,a=1,y--):(v.length=y+1,a=h(10,7-n),v[y]=i>0?(d/h(10,o-i)%h(10,i)|0)*a:0),c)for(;;)if(0==y){1e7==(v[0]+=a)&&(v[0]=1,++t.e);break}else{if(v[y]+=a,1e7!=v[y])break;v[y--]=0,a=1}for(n=v.length;0===v[--n];)v.pop();if(l&&(t.e>p||t.e<-p))throw Error(s+w(t));return t}function M(t,e){var r,n,i,a,o,u,c,s,f,h,d=t.constructor,p=d.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new d(t),l?A(e,p):e;if(c=t.d,h=e.d,n=e.e,s=t.e,c=c.slice(),o=s-n){for((f=o<0)?(r=c,o=-o,u=h.length):(r=h,n=s,u=c.length),o>(i=Math.max(Math.ceil(p/7),u)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=c.length)<(u=h.length))&&(u=i),i=0;i<u;i++)if(c[i]!=h[i]){f=c[i]<h[i];break}o=0}for(f&&(r=c,c=h,h=r,e.s=-e.s),u=c.length,i=h.length-u;i>0;--i)c[u++]=0;for(i=h.length;i>o;){if(c[--i]<h[i]){for(a=i;a&&0===c[--a];)c[a]=1e7-1;--c[a],c[i]+=1e7}c[i]-=h[i]}for(;0===c[--u];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(e.d=c,e.e=n,l?A(e,p):e):new d(0)}function S(t,e,r){var n,i=w(t),a=m(t.d),o=a.length;return e?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+j(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+j(-i-1)+a,r&&(n=r-o)>0&&(a+=j(n))):i>=o?(a+=j(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+j(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=j(n))),t.s<0?"-"+a:a}function _(t,e){if(t.length>e)return t.length=e,!0}function k(t){if(!t||"object"!=typeof t)throw Error(u+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]]))if(f(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(c+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(c+r+": "+n);return this}(o=function t(e){var r,n,i;function a(t){if(!(this instanceof a))return new a(t);if(this.constructor=a,t instanceof a){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(c+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return E(this,t.toString())}if("string"!=typeof t)throw Error(c+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,d.test(t))E(this,t);else throw Error(c+t)}if(a.prototype=y,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=t,a.config=a.set=k,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return a.config(e),a}(o)).default=o.Decimal=o,a=new o(1),void 0===(n=(function(){return o}).call(e,r,e,t))||(t.exports=n)}(0)},8892:(t,e,r)=>{"use strict";function n(t){return Number.isFinite(t)}function i(t){return"number"==typeof t&&t>0&&Number.isFinite(t)}r.d(e,{F:()=>i,H:()=>n})},8924:(t,e,r)=>{"use strict";r.d(e,{Mz:()=>w});var n=t=>Array.isArray(t)?t:[t],i=0,a=null,o=class{revision=i;_value;_lastValue;_isEqual=l;constructor(t,e=l){this._value=this._lastValue=t,this._isEqual=e}get value(){return a?.add(this),this._value}set value(t){this.value!==t&&(this._value=t,this.revision=++i)}};function l(t,e){return t===e}function u(t){return t instanceof o||console.warn("Not a valid cell! ",t),t.value}var c=(t,e)=>!1;function s(){return function(t,e=l){return new o(null,e)}(0,c)}var f=t=>{let e=t.collectionTag;null===e&&(e=t.collectionTag=s()),u(e)};Symbol();var h=0,d=Object.getPrototypeOf({}),p=class{constructor(t){this.value=t,this.value=t,this.tag.value=t}proxy=new Proxy(this,y);tag=s();tags={};children={};collectionTag=null;id=h++},y={get:(t,e)=>(function(){let{value:r}=t,n=Reflect.get(r,e);if("symbol"==typeof e||e in d)return n;if("object"==typeof n&&null!==n){let r=t.children[e];return void 0===r&&(r=t.children[e]=function(t){return Array.isArray(t)?new v(t):new p(t)}(n)),r.tag&&u(r.tag),r.proxy}{let r=t.tags[e];return void 0===r&&((r=t.tags[e]=s()).value=n),u(r),n}})(),ownKeys:t=>(f(t),Reflect.ownKeys(t.value)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t.value,e),has:(t,e)=>Reflect.has(t.value,e)},v=class{constructor(t){this.value=t,this.value=t,this.tag.value=t}proxy=new Proxy([this],g);tag=s();tags={};children={};collectionTag=null;id=h++},g={get:([t],e)=>("length"===e&&f(t),y.get(t,e)),ownKeys:([t])=>y.ownKeys(t),getOwnPropertyDescriptor:([t],e)=>y.getOwnPropertyDescriptor(t,e),has:([t],e)=>y.has(t,e)},m="undefined"!=typeof WeakRef?WeakRef:class{constructor(t){this.value=t}deref(){return this.value}};function b(){return{s:0,v:void 0,o:null,p:null}}function x(t,e={}){let r,n=b(),{resultEqualityCheck:i}=e,a=0;function o(){let e,o=n,{length:l}=arguments;for(let t=0;t<l;t++){let e=arguments[t];if("function"==typeof e||"object"==typeof e&&null!==e){let t=o.o;null===t&&(o.o=t=new WeakMap);let r=t.get(e);void 0===r?(o=b(),t.set(e,o)):o=r}else{let t=o.p;null===t&&(o.p=t=new Map);let r=t.get(e);void 0===r?(o=b(),t.set(e,o)):o=r}}let u=o;if(1===o.s)e=o.v;else if(e=t.apply(null,arguments),a++,i){let t=r?.deref?.()??r;null!=t&&i(t,e)&&(e=t,0!==a&&a--),r="object"==typeof e&&null!==e||"function"==typeof e?new m(e):e}return u.s=1,u.v=e,e}return o.clearCache=()=>{n=b(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var w=function(t,...e){let r="function"==typeof t?{memoize:t,memoizeOptions:e}:t,i=(...t)=>{let e,i=0,a=0,o={},l=t.pop();"object"==typeof l&&(o=l,l=t.pop()),function(t,e=`expected a function, instead received ${typeof t}`){if("function"!=typeof t)throw TypeError(e)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:u,memoizeOptions:c=[],argsMemoize:s=x,argsMemoizeOptions:f=[],devModeChecks:h={}}={...r,...o},d=n(c),p=n(f),y=function(t){let e=Array.isArray(t[0])?t[0]:t;return!function(t,e="expected all items to be functions, instead received the following types: "){if(!t.every(t=>"function"==typeof t)){let r=t.map(t=>"function"==typeof t?`function ${t.name||"unnamed"}()`:typeof t).join(", ");throw TypeError(`${e}[${r}]`)}}(e,"createSelector expects all input-selectors to be functions, but received the following types: "),e}(t),v=u(function(){return i++,l.apply(null,arguments)},...d);return Object.assign(s(function(){a++;let t=function(t,e){let r=[],{length:n}=t;for(let i=0;i<n;i++)r.push(t[i].apply(null,e));return r}(y,arguments);return e=v.apply(null,t)},...p),{resultFunc:l,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>e,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:u,argsMemoize:s})};return Object.assign(i,{withTypes:()=>i}),i}(x),O=Object.assign((t,e=w)=>{!function(t,e=`expected an object, instead received ${typeof t}`){if("object"!=typeof t)throw TypeError(e)}(t,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof t}`);let r=Object.keys(t);return e(r.map(e=>t[e]),(...t)=>t.reduce((t,e,n)=>(t[r[n]]=e,t),{}))},{withTypes:()=>O})},9033:(t,e,r)=>{"use strict";t.exports=r(2436)},9035:(t,e,r)=>{"use strict";r.d(e,{f:()=>p});var n=r(6377),i=r(6605),a=r(1643);function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class u{static create(t){return new u(t)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(t){var{bandAware:e,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==t){if(r)switch(r){case"start":default:return this.scale(t);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(e){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}isInRange(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}constructor(t){this.scale=t}}l(u,"EPS",1e-4);var c=function(t){var{width:e,height:r}=t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/e);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):e/Math.cos(i))};function s(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e)if(void 0!==r&&!0!==r(t[i]))return;else n.push(t[i]);return n}function f(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var a=r();return t*(e-t*a/2-n)>=0&&t*(e+t*a/2-i)<=0}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function p(t,e,r){var o,{tick:l,ticks:u,viewBox:h,minTickGap:p,orientation:y,interval:v,tickFormatter:g,unit:m,angle:b}=t;if(!u||!u.length||!l)return[];if((0,n.Et)(v)||a.m.isSsr)return null!=(o=s(u,((0,n.Et)(v)?v:0)+1))?o:[];var x=[],w="top"===y||"bottom"===y?"width":"height",O=m&&"width"===w?(0,i.P)(m,{fontSize:e,letterSpacing:r}):{width:0,height:0},j=(t,n)=>{var a,o="function"==typeof g?g(t.value,n):t.value;return"width"===w?(a=(0,i.P)(o,{fontSize:e,letterSpacing:r}),c({width:a.width+O.width,height:a.height+O.height},b)):(0,i.P)(o,{fontSize:e,letterSpacing:r})[w]},P=u.length>=2?(0,n.sA)(u[1].coordinate-u[0].coordinate):1,E=function(t,e,r){var n="width"===r,{x:i,y:a,width:o,height:l}=t;return 1===e?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(h,P,w);return"equidistantPreserveStart"===v?function(t,e,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:u}=e,c=0,h=1,d=l;h<=o.length;)if(a=function(){var e,a=null==n?void 0:n[c];if(void 0===a)return{v:s(n,h)};var o=c,p=()=>(void 0===e&&(e=r(a,o)),e),y=a.coordinate,v=0===c||f(t,y,p,d,u);v||(c=0,d=l,h+=1),v&&(d=y+t*(p()/2+i),c+=h)}())return a.v;return[]}(P,E,j,u,p):("preserveStart"===v||"preserveStartEnd"===v?function(t,e,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:u,end:c}=e;if(a){var s=n[l-1],h=r(s,l-1),p=t*(s.coordinate+t*h/2-c);o[l-1]=s=d(d({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),f(t,s.tickCoord,()=>h,u,c)&&(c=s.tickCoord-t*(h/2+i),o[l-1]=d(d({},s),{},{isShow:!0}))}for(var y=a?l-1:l,v=function(e){var n,a=o[e],l=()=>(void 0===n&&(n=r(a,e)),n);if(0===e){var s=t*(a.coordinate-t*l()/2-u);o[e]=a=d(d({},a),{},{tickCoord:s<0?a.coordinate-s*t:a.coordinate})}else o[e]=a=d(d({},a),{},{tickCoord:a.coordinate});f(t,a.tickCoord,l,u,c)&&(u=a.tickCoord+t*(l()/2+i),o[e]=d(d({},a),{},{isShow:!0}))},g=0;g<y;g++)v(g);return o}(P,E,j,u,p,"preserveStartEnd"===v):function(t,e,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=e,{end:u}=e,c=function(e){var n,c=a[e],s=()=>(void 0===n&&(n=r(c,e)),n);if(e===o-1){var h=t*(c.coordinate+t*s()/2-u);a[e]=c=d(d({},c),{},{tickCoord:h>0?c.coordinate-h*t:c.coordinate})}else a[e]=c=d(d({},c),{},{tickCoord:c.coordinate});f(t,c.tickCoord,s,l,u)&&(u=c.tickCoord-t*(s()/2+i),a[e]=d(d({},c),{},{isShow:!0}))},s=o-1;s>=0;s--)c(s);return a}(P,E,j,u,p)).filter(t=>t.isShow)}},9095:(t,e,r)=>{"use strict";r.d(e,{E:()=>_});var n=r(2115),i=r(2596),a=r(6377),o=r(1643),l=r(788),u=r(6605),c=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,h=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,d={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},p=Object.keys(d);class y{static parse(t){var e,[,r,n]=null!=(e=h.exec(t))?e:[];return new y(parseFloat(r),null!=n?n:"")}add(t){return this.unit!==t.unit?new y(NaN,""):new y(this.num+t.num,this.unit)}subtract(t){return this.unit!==t.unit?new y(NaN,""):new y(this.num-t.num,this.unit)}multiply(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new y(NaN,""):new y(this.num*t.num,this.unit||t.unit)}divide(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new y(NaN,""):new y(this.num/t.num,this.unit||t.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,a.M8)(this.num)}constructor(t,e){this.num=t,this.unit=e,this.num=t,this.unit=e,(0,a.M8)(t)&&(this.unit=""),""===e||f.test(e)||(this.num=NaN,this.unit=""),p.includes(e)&&(this.num=t*d[e],this.unit="px")}}function v(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,[,n,i,a]=null!=(r=c.exec(e))?r:[],o=y.parse(null!=n?n:""),l=y.parse(null!=a?a:""),u="*"===i?o.multiply(l):o.divide(l);if(u.isNaN())return"NaN";e=e.replace(c,u.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var f,[,h,d,p]=null!=(f=s.exec(e))?f:[],v=y.parse(null!=h?h:""),g=y.parse(null!=p?p:""),m="+"===d?v.add(g):v.subtract(g);if(m.isNaN())return"NaN";e=e.replace(s,m.toString())}return e}var g=/\(([^()]*)\)/;function m(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e,r=t;null!=(e=g.exec(r));){var[,n]=e;r=r.replace(g,v(n))}return r}(e),e=v(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],x=["dx","dy","angle","className","breakAll"];function w(){return(w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function O(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var j=/[ \f\n\r\t\v\u2028\u2029]+/,P=t=>{var{children:e,breakAll:r,style:n}=t;try{var i=[];(0,a.uy)(e)||(i=r?e.toString().split(""):e.toString().split(j));var o=i.map(t=>({word:t,width:(0,u.P)(t,n).width})),l=r?0:(0,u.P)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:l}}catch(t){return null}},E=(t,e,r,n,i)=>{var o,{maxLines:l,children:u,style:c,breakAll:s}=t,f=(0,a.Et)(l),h=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce((t,e)=>{var{word:a,width:o}=e,l=t[t.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):t.push({words:[a],width:o}),t},[])},d=h(e),p=t=>t.reduce((t,e)=>t.width>e.width?t:e);if(!f||i||!(d.length>l||p(d).width>Number(n)))return d;for(var y=t=>{var e=h(P({breakAll:s,style:c,children:u.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>l||p(e).width>Number(n),e]},v=0,g=u.length-1,m=0;v<=g&&m<=u.length-1;){var b=Math.floor((v+g)/2),[x,w]=y(b-1),[O]=y(b);if(x||O||(v=b+1),x&&O&&(g=b-1),!x&&O){o=w;break}m++}return o||d},A=t=>[{words:(0,a.uy)(t)?[]:t.toString().split(j)}],M=t=>{var{width:e,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:l}=t;if((e||r)&&!o.m.isSsr){var u=P({breakAll:a,children:n,style:i});if(!u)return A(n);var{wordsWithComputedWidth:c,spaceWidth:s}=u;return E({breakAll:a,children:n,maxLines:l,style:i},c,s,e,r)}return A(n)},S="#808080",_=(0,n.forwardRef)((t,e)=>{var r,{x:o=0,y:u=0,lineHeight:c="1em",capHeight:s="0.71em",scaleToFit:f=!1,textAnchor:h="start",verticalAnchor:d="end",fill:p=S}=t,y=O(t,b),v=(0,n.useMemo)(()=>M({breakAll:y.breakAll,children:y.children,maxLines:y.maxLines,scaleToFit:f,style:y.style,width:y.width}),[y.breakAll,y.children,y.maxLines,f,y.style,y.width]),{dx:g,dy:j,angle:P,className:E,breakAll:A}=y,_=O(y,x);if(!(0,a.vh)(o)||!(0,a.vh)(u))return null;var k=o+((0,a.Et)(g)?g:0),C=u+((0,a.Et)(j)?j:0);switch(d){case"start":r=m("calc(".concat(s,")"));break;case"middle":r=m("calc(".concat((v.length-1)/2," * -").concat(c," + (").concat(s," / 2))"));break;default:r=m("calc(".concat(v.length-1," * -").concat(c,")"))}var T=[];if(f){var D=v[0].width,{width:N}=y;T.push("scale(".concat((0,a.Et)(N)?N/D:1,")"))}return P&&T.push("rotate(".concat(P,", ").concat(k,", ").concat(C,")")),T.length&&(_.transform=T.join(" ")),n.createElement("text",w({},(0,l.J9)(_,!0),{ref:e,x:k,y:C,className:(0,i.$)("recharts-text",E),textAnchor:h,fill:p.includes("url")?S:p}),v.map((t,e)=>{var i=t.words.join(A?"":" ");return n.createElement("tspan",{x:k,dy:0===e?r:c,key:"".concat(i,"-").concat(e)},i)}))});_.displayName="Text"},9279:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isObjectLike=function(t){return"object"==typeof t&&null!==t}},9449:(t,e,r)=>{"use strict";r.d(e,{c2:()=>g,GO:()=>y,Ds:()=>v});var n=r(8924),i=r(5672),a=r.n(i),o=r(241),l=r.n(o),u=t=>t.legend.settings;(0,n.Mz)([t=>t.legend.payload,u],(t,e)=>{var{itemSorter:r}=e,n=t.flat(1);return r?l()(n,r):n});var c=r(9827),s=r(2589),f=r(6908),h=r(4421);function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var y=(0,n.Mz)([s.Lp,s.A$,s.HK,t=>t.brush.height,f.h,f.W,u,t=>t.legend.size],(t,e,r,n,i,o,l,u)=>{var s=o.reduce((t,e)=>{var{orientation:r}=e;if(!e.mirror&&!e.hide){var n="number"==typeof e.width?e.width:h.tQ;return p(p({},t),{},{[r]:t[r]+n})}return t},{left:r.left||0,right:r.right||0}),f=i.reduce((t,e)=>{var{orientation:r}=e;return e.mirror||e.hide?t:p(p({},t),{},{[r]:a()(t,"".concat(r))+e.height})},{top:r.top||0,bottom:r.bottom||0}),d=p(p({},f),s),y=d.bottom;d.bottom+=n;var v=t-(d=(0,c.s0)(d,l,u)).left-d.right,g=e-d.top-d.bottom;return p(p({brushBottom:y},d),{},{width:Math.max(v,0),height:Math.max(g,0)})}),v=(0,n.Mz)(y,t=>({x:t.left,y:t.top,width:t.width,height:t.height})),g=(0,n.Mz)(s.Lp,s.A$,(t,e)=>({x:0,y:0,width:t,height:e}))},9452:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isLength=function(t){return Number.isSafeInteger(t)&&t>=0}},9584:(t,e,r)=>{"use strict";r.d(e,{u:()=>O});var n=r(2115),i=r(5672),a=r.n(i),o=r(2596);function l(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}var u=r(2348),c=r(9095),s=r(379),f=r(6377),h=r(3597),d=r(788),p=r(9035),y=["viewBox"],v=["viewBox"];function g(){return(g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach(function(e){w(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function x(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function w(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class O extends n.Component{shouldComponentUpdate(t,e){var{viewBox:r}=t,n=x(t,y),i=this.props,{viewBox:a}=i,o=x(i,v);return!l(r,a)||!l(n,o)||!l(e,this.state)}getTickLineCoord(t){var e,r,n,i,a,o,{x:l,y:u,width:c,height:s,orientation:h,tickSize:d,mirror:p,tickMargin:y}=this.props,v=p?-1:1,g=t.tickSize||d,m=(0,f.Et)(t.tickCoord)?t.tickCoord:t.coordinate;switch(h){case"top":e=r=t.coordinate,o=(n=(i=u+!p*s)-v*g)-v*y,a=m;break;case"left":n=i=t.coordinate,a=(e=(r=l+!p*c)-v*g)-v*y,o=m;break;case"right":n=i=t.coordinate,a=(e=(r=l+p*c)+v*g)+v*y,o=m;break;default:e=r=t.coordinate,o=(n=(i=u+p*s)+v*g)+v*y,a=m}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var t,{orientation:e,mirror:r}=this.props;switch(e){case"left":t=r?"start":"end";break;case"right":t=r?"end":"start";break;default:t="middle"}return t}getTickVerticalAnchor(){var{orientation:t,mirror:e}=this.props;switch(t){case"left":case"right":return"middle";case"top":return e?"start":"end";default:return e?"end":"start"}}renderAxisLine(){var{x:t,y:e,width:r,height:i,orientation:l,mirror:u,axisLine:c}=this.props,s=b(b(b({},(0,d.J9)(this.props,!1)),(0,d.J9)(c,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!u||"bottom"===l&&u);s=b(b({},s),{},{x1:t,y1:e+f*i,x2:t+r,y2:e+f*i})}else{var h=+("left"===l&&!u||"right"===l&&u);s=b(b({},s),{},{x1:t+h*r,y1:e,x2:t+h*r,y2:e+i})}return n.createElement("line",g({},s,{className:(0,o.$)("recharts-cartesian-axis-line",a()(c,"className"))}))}static renderTickItem(t,e,r){var i,a=(0,o.$)(e.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(t))i=n.cloneElement(t,b(b({},e),{},{className:a}));else if("function"==typeof t)i=t(b(b({},e),{},{className:a}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof t&&(l=(0,o.$)(l,t.className)),i=n.createElement(c.E,g({},e,{className:l}),r)}return i}renderTicks(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:l,tick:c,tickFormatter:s,unit:f}=this.props,y=(0,p.f)(b(b({},this.props),{},{ticks:r}),t,e),v=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),x=(0,d.J9)(this.props,!1),w=(0,d.J9)(c,!1),j=b(b({},x),{},{fill:"none"},(0,d.J9)(i,!1)),P=y.map((t,e)=>{var{line:r,tick:d}=this.getTickLineCoord(t),p=b(b(b(b({textAnchor:v,verticalAnchor:m},x),{},{stroke:"none",fill:l},w),d),{},{index:e,payload:t,visibleTicksCount:y.length,tickFormatter:s});return n.createElement(u.W,g({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,h.XC)(this.props,t,e)),i&&n.createElement("line",g({},j,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),c&&O.renderTickItem(c,p,"".concat("function"==typeof s?s(t.value,e):t.value).concat(f||"")))});return P.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},P):null}render(){var{axisLine:t,width:e,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:l}=this.props;return null!=e&&e<=0||null!=r&&r<=0?null:n.createElement(u.W,{className:(0,o.$)("recharts-cartesian-axis",i),ref:t=>{if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(e);var r=e[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},t&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),s.J.renderCallByParent(this.props))}constructor(t){super(t),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}w(O,"displayName","CartesianAxis"),w(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},9641:t=>{!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=u(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,a=u(t),o=a[0],l=a[1],c=new i((o+l)*3/4-l),s=0,f=l>0?o-4:o;for(r=0;r<f;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],c[s++]=e>>16&255,c[s++]=e>>8&255,c[s++]=255&e;return 2===l&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,c[s++]=255&e),1===l&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,c[s++]=e>>8&255,c[s++]=255&e),c},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,a=[],o=0,l=n-i;o<l;o+=16383)a.push(function(t,e,n){for(var i,a=[],o=e;o<n;o+=3)i=(t[o]<<16&0xff0000)+(t[o+1]<<8&65280)+(255&t[o+2]),a.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return a.join("")}(t,o,o+16383>l?l:o+16383));return 1===i?a.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&a.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),a.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,l=a.length;o<l;++o)r[o]=a[o],n[a.charCodeAt(o)]=o;function u(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(t,e,r){"use strict";var n=r(675),i=r(783),a="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,l.prototype),e}function l(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return s(t)}return u(t,e,r)}function u(t,e,r){if("string"==typeof t){var n=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!l.isEncoding(i))throw TypeError("Unknown encoding: "+i);var a=0|d(n,i),u=o(a),c=u.write(n,i);return c!==a&&(u=u.slice(0,c)),u}if(ArrayBuffer.isView(t))return f(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(_(t,ArrayBuffer)||t&&_(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(_(t,SharedArrayBuffer)||t&&_(t.buffer,SharedArrayBuffer)))return function(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),l.prototype),n}(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var s=t.valueOf&&t.valueOf();if(null!=s&&s!==t)return l.from(s,e,r);var p=function(t){if(l.isBuffer(t)){var e=0|h(t.length),r=o(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?o(0):f(t):"Buffer"===t.type&&Array.isArray(t.data)?f(t.data):void 0}(t);if(p)return p;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return l.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function c(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function s(t){return c(t),o(t<0?0:0|h(t))}function f(t){for(var e=t.length<0?0:0|h(t.length),r=o(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}e.Buffer=l,e.SlowBuffer=function(t){return+t!=t&&(t=0),l.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,l.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),l.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(l.prototype,"parent",{enumerable:!0,get:function(){if(l.isBuffer(this))return this.buffer}}),Object.defineProperty(l.prototype,"offset",{enumerable:!0,get:function(){if(l.isBuffer(this))return this.byteOffset}}),l.poolSize=8192,l.from=function(t,e,r){return u(t,e,r)},Object.setPrototypeOf(l.prototype,Uint8Array.prototype),Object.setPrototypeOf(l,Uint8Array),l.alloc=function(t,e,r){return(c(t),t<=0)?o(t):void 0!==e?"string"==typeof r?o(t).fill(e,r):o(t).fill(e):o(t)},l.allocUnsafe=function(t){return s(t)},l.allocUnsafeSlow=function(t){return s(t)};function h(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function d(t,e){if(l.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||_(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return E(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return M(t).length;default:if(i)return n?-1:E(t).length;e=(""+e).toLowerCase(),i=!0}}function p(t,e,r){var i,a,o,l=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",a=e;a<r;++a)i+=k[t[a]];return i}(this,e,r);case"utf8":case"utf-8":return m(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=this,a=e,o=r,0===a&&o===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(a,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}(this,e,r);default:if(l)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),l=!0}}function y(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function v(t,e,r,n,i){var a;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(a=r*=1)!=a&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(i)return -1;else r=t.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof e&&(e=l.from(e,n)),l.isBuffer(e))return 0===e.length?-1:g(t,e,r,n,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return g(t,[e],r,n,i)}throw TypeError("val must be string, number or Buffer")}function g(t,e,r,n,i){var a,o=1,l=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;o=2,l/=2,u/=2,r/=2}function c(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}if(i){var s=-1;for(a=r;a<l;a++)if(c(t,a)===c(e,-1===s?0:a-s)){if(-1===s&&(s=a),a-s+1===u)return s*o}else -1!==s&&(a-=a-s),s=-1}else for(r+u>l&&(r=l-u),a=r;a>=0;a--){for(var f=!0,h=0;h<u;h++)if(c(t,a+h)!==c(e,h)){f=!1;break}if(f)return a}return -1}l.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==l.prototype},l.compare=function(t,e){if(_(t,Uint8Array)&&(t=l.from(t,t.offset,t.byteLength)),_(e,Uint8Array)&&(e=l.from(e,e.offset,e.byteLength)),!l.isBuffer(t)||!l.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,a=Math.min(r,n);i<a;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:+(n<r)},l.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},l.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return l.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=l.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var a=t[r];if(_(a,Uint8Array)&&(a=l.from(a)),!l.isBuffer(a))throw TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},l.byteLength=d,l.prototype._isBuffer=!0,l.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)y(this,e,e+1);return this},l.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},l.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},l.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?m(this,0,t):p.apply(this,arguments)},l.prototype.toLocaleString=l.prototype.toString,l.prototype.equals=function(t){if(!l.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===l.compare(this,t)},l.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},a&&(l.prototype[a]=l.prototype.inspect),l.prototype.compare=function(t,e,r,n,i){if(_(t,Uint8Array)&&(t=l.from(t,t.offset,t.byteLength)),!l.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var a=i-n,o=r-e,u=Math.min(a,o),c=this.slice(n,i),s=t.slice(e,r),f=0;f<u;++f)if(c[f]!==s[f]){a=c[f],o=s[f];break}return a<o?-1:+(o<a)},l.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},l.prototype.indexOf=function(t,e,r){return v(this,t,e,r,!0)},l.prototype.lastIndexOf=function(t,e,r){return v(this,t,e,r,!1)};function m(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var a,o,l,u,c=t[i],s=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(s=c);break;case 2:(192&(a=t[i+1]))==128&&(u=(31&c)<<6|63&a)>127&&(s=u);break;case 3:a=t[i+1],o=t[i+2],(192&a)==128&&(192&o)==128&&(u=(15&c)<<12|(63&a)<<6|63&o)>2047&&(u<55296||u>57343)&&(s=u);break;case 4:a=t[i+1],o=t[i+2],l=t[i+3],(192&a)==128&&(192&o)==128&&(192&l)==128&&(u=(15&c)<<18|(63&a)<<12|(63&o)<<6|63&l)>65535&&u<1114112&&(s=u)}null===s?(s=65533,f=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|1023&s),n.push(s),i+=f}var h=n,d=h.length;if(d<=4096)return String.fromCharCode.apply(String,h);for(var p="",y=0;y<d;)p+=String.fromCharCode.apply(String,h.slice(y,y+=4096));return p}function b(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function x(t,e,r,n,i,a){if(!l.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<a)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function w(t,e,r,n,i,a){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function O(t,e,r,n,a){return e*=1,r>>>=0,a||w(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function j(t,e,r,n,a){return e*=1,r>>>=0,a||w(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}l.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,a,o,l,u,c,s,f,h=this.length-e;if((void 0===r||r>h)&&(r=h),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var d=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=e.length;n>a/2&&(n=a/2);for(var o=0;o<n;++o){var l,u=parseInt(e.substr(2*o,2),16);if((l=u)!=l)break;t[r+o]=u}return o}(this,t,e,r);case"utf8":case"utf-8":return i=e,a=r,S(E(t,this.length-i),this,i,a);case"ascii":return o=e,l=r,S(A(t),this,o,l);case"latin1":case"binary":return function(t,e,r,n){return S(A(e),t,r,n)}(this,t,e,r);case"base64":return u=e,c=r,S(M(t),this,u,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return s=e,f=r,S(function(t,e){for(var r,n,i=[],a=0;a<t.length&&!((e-=2)<0);++a)n=(r=t.charCodeAt(a))>>8,i.push(r%256),i.push(n);return i}(t,this.length-s),this,s,f);default:if(d)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),d=!0}},l.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},l.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,l.prototype),n},l.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,a=0;++a<e&&(i*=256);)n+=this[t+a]*i;return n},l.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},l.prototype.readUInt8=function(t,e){return t>>>=0,e||b(t,1,this.length),this[t]},l.prototype.readUInt16LE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]|this[t+1]<<8},l.prototype.readUInt16BE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]<<8|this[t+1]},l.prototype.readUInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},l.prototype.readUInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},l.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,a=0;++a<e&&(i*=256);)n+=this[t+a]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},l.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=e,i=1,a=this[t+--n];n>0&&(i*=256);)a+=this[t+--n]*i;return a>=(i*=128)&&(a-=Math.pow(2,8*e)),a},l.prototype.readInt8=function(t,e){return(t>>>=0,e||b(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},l.prototype.readInt16LE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},l.prototype.readInt16BE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},l.prototype.readInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},l.prototype.readInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},l.prototype.readFloatLE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!0,23,4)},l.prototype.readFloatBE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!1,23,4)},l.prototype.readDoubleLE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!0,52,8)},l.prototype.readDoubleBE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!1,52,8)},l.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}var a=1,o=0;for(this[e]=255&t;++o<r&&(a*=256);)this[e+o]=t/a&255;return e+r},l.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}var a=r-1,o=1;for(this[e+a]=255&t;--a>=0&&(o*=256);)this[e+a]=t/o&255;return e+r},l.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,1,255,0),this[e]=255&t,e+1},l.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},l.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},l.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},l.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},l.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}var a=0,o=1,l=0;for(this[e]=255&t;++a<r&&(o*=256);)t<0&&0===l&&0!==this[e+a-1]&&(l=1),this[e+a]=(t/o|0)-l&255;return e+r},l.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}var a=r-1,o=1,l=0;for(this[e+a]=255&t;--a>=0&&(o*=256);)t<0&&0===l&&0!==this[e+a+1]&&(l=1),this[e+a]=(t/o|0)-l&255;return e+r},l.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},l.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},l.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},l.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},l.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},l.prototype.writeFloatLE=function(t,e,r){return O(this,t,e,!0,r)},l.prototype.writeFloatBE=function(t,e,r){return O(this,t,e,!1,r)},l.prototype.writeDoubleLE=function(t,e,r){return j(this,t,e,!0,r)},l.prototype.writeDoubleBE=function(t,e,r){return j(this,t,e,!1,r)},l.prototype.copy=function(t,e,r,n){if(!l.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var a=i-1;a>=0;--a)t[a+e]=this[a+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},l.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!l.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,a=t.charCodeAt(0);("utf8"===n&&a<128||"latin1"===n)&&(t=a)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var o=l.isBuffer(t)?t:l.from(t,n),u=o.length;if(0===u)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=o[i%u]}return this};var P=/[^+/0-9A-Za-z-_]/g;function E(t,e){e=e||1/0;for(var r,n=t.length,i=null,a=[],o=0;o<n;++o){if((r=t.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(e-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&a.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;a.push(r)}else if(r<2048){if((e-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return a}function A(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function M(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(P,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function S(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function _(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var k=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},783:function(t,e){e.read=function(t,e,r,n,i){var a,o,l=8*i-n-1,u=(1<<l)-1,c=u>>1,s=-7,f=r?i-1:0,h=r?-1:1,d=t[e+f];for(f+=h,a=d&(1<<-s)-1,d>>=-s,s+=l;s>0;a=256*a+t[e+f],f+=h,s-=8);for(o=a&(1<<-s)-1,a>>=-s,s+=n;s>0;o=256*o+t[e+f],f+=h,s-=8);if(0===a)a=1-c;else{if(a===u)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,n),a-=c}return(d?-1:1)*o*Math.pow(2,a-n)},e.write=function(t,e,r,n,i,a){var o,l,u,c=8*a-i-1,s=(1<<c)-1,f=s>>1,h=5960464477539062e-23*(23===i),d=n?0:a-1,p=n?1:-1,y=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(l=+!!isNaN(e),o=s):(o=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+f>=1?e+=h/u:e+=h*Math.pow(2,1-f),e*u>=2&&(o++,u/=2),o+f>=s?(l=0,o=s):o+f>=1?(l=(e*u-1)*Math.pow(2,i),o+=f):(l=e*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;t[r+d]=255&l,d+=p,l/=256,i-=8);for(o=o<<i|l,c+=i;c>0;t[r+d]=255&o,d+=p,o/=256,c-=8);t[r+d-p]|=128*y}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var a=r[t]={exports:{}},o=!0;try{e[t](a,a.exports,n),o=!1}finally{o&&delete r[t]}return a.exports}n.ab="//",t.exports=n(72)}()},9738:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(4117),i=r(2721);e.cloneDeepWith=function(t,e){return n.cloneDeepWith(t,(r,a,o,l)=>{let u=e?.(r,a,o,l);if(null!=u)return u;if("object"==typeof t)switch(Object.prototype.toString.call(t)){case i.numberTag:case i.stringTag:case i.booleanTag:{let e=new t.constructor(t?.valueOf());return n.copyProperties(e,t),e}case i.argumentsTag:{let e={};return n.copyProperties(e,t),e.length=t.length,e[Symbol.iterator]=t[Symbol.iterator],e}default:return}})}},9819:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}r.d(e,{A:()=>n}),Array.prototype.slice},9827:(t,e,r)=>{"use strict";r.d(e,{qx:()=>T,IH:()=>C,s0:()=>b,gH:()=>m,SW:()=>B,YB:()=>j,bk:()=>L,Hj:()=>D,nb:()=>M,PW:()=>w,Mk:()=>k,$8:()=>A,yy:()=>E,Rh:()=>O,GF:()=>N,uM:()=>I,kr:()=>g,r4:()=>z,_L:()=>x});var n=r(241),i=r.n(n),a=r(5672),o=r.n(a);function l(t,e){if((i=t.length)>1)for(var r,n,i,a=1,o=t[e[0]],l=o.length;a<i;++a)for(n=o,o=t[e[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var u=r(9819),c=r(5654);function s(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function f(t,e){return t[e]}function h(t){let e=[];return e.key=t,e}var d=r(6377),p=r(5641);function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function g(t,e,r){return(0,d.uy)(t)||(0,d.uy)(e)?r:(0,d.vh)(e)?o()(t,e,r):"function"==typeof e?e(t):r}var m=(t,e,r,n,i)=>{var a,o=-1,l=null!=(a=null==e?void 0:e.length)?a:0;if(l<=1||null==t)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var u=0;u<l;u++){var c=u>0?r[u-1].coordinate:r[l-1].coordinate,s=r[u].coordinate,f=u>=l-1?r[0].coordinate:r[u+1].coordinate,h=void 0;if((0,d.sA)(s-c)!==(0,d.sA)(f-s)){var p=[];if((0,d.sA)(f-s)===(0,d.sA)(i[1]-i[0])){h=f;var y=s+i[1]-i[0];p[0]=Math.min(y,(y+c)/2),p[1]=Math.max(y,(y+c)/2)}else{h=c;var v=f+i[1]-i[0];p[0]=Math.min(s,(v+s)/2),p[1]=Math.max(s,(v+s)/2)}var g=[Math.min(s,(h+s)/2),Math.max(s,(h+s)/2)];if(t>g[0]&&t<=g[1]||t>=p[0]&&t<=p[1]){({index:o}=r[u]);break}}else{var m=Math.min(c,f),b=Math.max(c,f);if(t>(m+s)/2&&t<=(b+s)/2){({index:o}=r[u]);break}}}else if(e){for(var x=0;x<l;x++)if(0===x&&t<=(e[x].coordinate+e[x+1].coordinate)/2||x>0&&x<l-1&&t>(e[x].coordinate+e[x-1].coordinate)/2&&t<=(e[x].coordinate+e[x+1].coordinate)/2||x===l-1&&t>(e[x].coordinate+e[x-1].coordinate)/2){({index:o}=e[x]);break}}return o},b=(t,e,r)=>{if(e&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=e;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&(0,d.Et)(t[a]))return v(v({},t),{},{[a]:t[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&(0,d.Et)(t[o]))return v(v({},t),{},{[o]:t[o]+(i||0)})}return t},x=(t,e)=>"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e,w=(t,e,r,n)=>{if(n)return t.map(t=>t.coordinate);var i,a,o=t.map(t=>(t.coordinate===e&&(i=!0),t.coordinate===r&&(a=!0),t.coordinate));return i||o.push(e),a||o.push(r),o},O=(t,e,r)=>{if(!t)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:u,categoricalDomain:c,tickCount:s,ticks:f,niceTicks:h,axisType:p}=t;if(!o)return null;var y="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,v=(e||r)&&"category"===i&&o.bandwidth?o.bandwidth()/y:0;return(v="angleAxis"===p&&a&&a.length>=2?2*(0,d.sA)(a[0]-a[1])*v:v,e&&(f||h))?(f||h||[]).map((t,e)=>({coordinate:o(n?n.indexOf(t):t)+v,value:t,offset:v,index:e})).filter(t=>!(0,d.M8)(t.coordinate)):u&&c?c.map((t,e)=>({coordinate:o(t)+v,value:t,index:e,offset:v})):o.ticks&&!r&&null!=s?o.ticks(s).map((t,e)=>({coordinate:o(t)+v,value:t,offset:v,index:e})):o.domain().map((t,e)=>({coordinate:o(t)+v,value:n?n[t]:t,index:e,offset:v}))},j=t=>{var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=t(e[0]),l=t(e[r-1]);(o<i||o>a||l<i||l>a)&&t.domain([e[0],e[r-1]])}},P={sign:t=>{var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,a=0,o=0;o<e;++o){var l=(0,d.M8)(t[o][r][1])?t[o][r][0]:t[o][r][1];l>=0?(t[o][r][0]=i,t[o][r][1]=i+l,i=t[o][r][1]):(t[o][r][0]=a,t[o][r][1]=a+l,a=t[o][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,a=0,o=t[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=t[r][a][1]||0;if(i)for(r=0;r<n;++r)t[r][a][1]/=i}l(t,e)}},none:l,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],a=i.length;n<a;++n){for(var o=0,u=0;o<r;++o)u+=t[o][n][1]||0;i[n][1]+=i[n][0]=-u/2}l(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var u=0,c=0,s=0;u<i;++u){for(var f=t[e[u]],h=f[o][1]||0,d=(h-(f[o-1][1]||0))/2,p=0;p<u;++p){var y=t[e[p]];d+=(y[o][1]||0)-(y[o-1][1]||0)}c+=h,s+=d*h}r[o-1][1]+=r[o-1][0]=a,c&&(a-=s/c)}r[o-1][1]+=r[o-1][0]=a,l(t,e)}},positive:t=>{var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,a=0;a<e;++a){var o=(0,d.M8)(t[a][r][1])?t[a][r][0]:t[a][r][1];o>=0?(t[a][r][0]=i,t[a][r][1]=i+o,i=t[a][r][1]):(t[a][r][0]=0,t[a][r][1]=0)}}},E=(t,e,r)=>{var n=P[r];return(function(){var t=(0,c.A)([]),e=s,r=l,n=f;function i(i){var a,o,l=Array.from(t.apply(this,arguments),h),c=l.length,s=-1;for(let t of i)for(a=0,++s;a<c;++a)(l[a][s]=[0,+n(t,l[a].key,s,i)]).data=t;for(a=0,o=(0,u.A)(e(l));a<c;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,c.A)(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,c.A)(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?s:"function"==typeof t?t:(0,c.A)(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?l:t,i):r},i})().keys(e).value((t,e)=>+g(t,e,0)).order(s).offset(n)(t)};function A(t){return null==t?void 0:String(t)}function M(t){var{axis:e,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=t;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!(0,d.uy)(i[e.dataKey])){var l=(0,d.eP)(r,"value",i[e.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var u=g(i,(0,d.uy)(o)?e.dataKey:o);return(0,d.uy)(u)?null:e.scale(u)}var S=t=>{var e=t.flat(2).filter(d.Et);return[Math.min(...e),Math.max(...e)]},_=t=>[t[0]===1/0?0:t[0],t[1]===-1/0?0:t[1]],k=(t,e,r)=>{if(null!=t)return _(Object.keys(t).reduce((n,i)=>{var{stackedData:a}=t[i],o=a.reduce((t,n)=>{var i=S(n.slice(e,r+1));return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},C=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,T=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,D=(t,e,r)=>{if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var a=i()(e,t=>t.coordinate),o=1/0,l=1,u=a.length;l<u;l++){var c=a[l],s=a[l-1];o=Math.min((c.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function N(t){var{tooltipEntrySettings:e,dataKey:r,payload:n,value:i,name:a}=t;return v(v({},e),{},{dataKey:r,payload:n,value:i,name:a})}function I(t,e){return t?String(t):"string"==typeof e?e:void 0}function z(t,e,r,n,i){return"horizontal"===r||"vertical"===r?t>=i.left&&t<=i.left+i.width&&e>=i.top&&e<=i.top+i.height?{x:t,y:e}:null:n?(0,p.yy)({x:t,y:e},n):null}var L=(t,e,r,n)=>{var i=e.find(t=>t&&t.index===r);if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var a=i.coordinate,{radius:o}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:u}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,l,u)),{},{angle:u,radius:l})}return{x:0,y:0}},B=(t,e)=>"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius},9901:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(4373),i=r(4664);e.range=function(t,e,r){r&&"number"!=typeof r&&n.isIterateeCall(t,e,r)&&(e=r=void 0),t=i.toFinite(t),void 0===e?(e=t,t=0):e=i.toFinite(e),r=void 0===r?t<e?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((e-t)/(r||1)),0),o=Array(a);for(let e=0;e<a;e++)o[e]=t,t+=r;return o}}}]);