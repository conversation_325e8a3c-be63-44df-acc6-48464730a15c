(()=>{var e={};e.id=974,e.ids=[974],e.modules={15:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},144:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7509);t.property=function(e){return function(t){return n.get(t,e)}}},175:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},415:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2066),i=r(657),a=r(9138),o=r(7509),l=r(7841);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},561:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},657:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},830:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(6349),i=r(9899);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},833:(e,t,r)=>{Promise.resolve().then(r.bind(r,1320))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},907:(e,t,r)=>{"use strict";var n=r(3210),i=r(7379),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,c=n.useEffect,u=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=o(e,(f=u(function(){function e(e){if(!c){if(c=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,c=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,r,n,i]))[0],f[1]);return c(function(){d.hasValue=!0,d.value=p},[p]),s(p),p}},911:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5100);t.debounce=function(e,t=0,r={}){let i;"object"!=typeof r&&(r={});let{leading:a=!1,trailing:o=!0,maxWait:l}=r,c=[,,];a&&(c[0]="leading"),o&&(c[1]="trailing");let u=null,s=n.debounce(function(...t){i=e.apply(this,t),u=null},t,{edges:c}),f=function(...t){return null!=l&&(null===u&&(u=Date.now()),Date.now()-u>=l)?(i=e.apply(this,t),u=Date.now(),s.cancel(),s.schedule(),i):(s.apply(this,t),i)};return f.cancel=s.cancel,f.flush=()=>(s.flush(),i),f}},921:(e,t,r)=>{e.exports=r(1337).range},1117:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],l]:e._events[c].push(l):(e._events[c]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var c,u,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(u=1,c=Array(f-1);u<f;u++)c[u-1]=arguments[u];s.fn.apply(s.context,c)}else{var d,p=s.length;for(u=0;u<p;u++)switch(s[u].once&&this.removeListener(e,s[u].fn,void 0,!0),f){case 1:s[u].fn.call(s[u].context);break;case 2:s[u].fn.call(s[u].context,t);break;case 3:s[u].fn.call(s[u].context,t,n);break;case 4:s[u].fn.call(s[u].context,t,n,i);break;default:if(!c)for(d=1,c=Array(f-1);d<f;d++)c[d-1]=arguments[d];s[u].fn.apply(s[u].context,c)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var c=0,u=[],s=l.length;c<s;c++)(l[c].fn!==t||i&&!l[c].once||n&&l[c].context!==n)&&u.push(l[c]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},1183:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>d,tree:()=>u});var n=r(5239),i=r(8088),a=r(8170),o=r.n(a),l=r(893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(t,c);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,s=["C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\app\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\projectapp\\\\vet-dashboard\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\projectapp\\vet-dashboard\\src\\app\\page.tsx","default")},1251:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},1320:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>mt});var n={};r.r(n),r.d(n,{scaleBand:()=>iu,scaleDiverging:()=>function e(){var t=aP(lU()(ao));return t.copy=function(){return lR(t,e())},ir.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=aC(lU()).domain([.1,1,10]);return t.copy=function(){return lR(t,e()).base(t.base())},ir.apply(t,arguments)},scaleDivergingPow:()=>lz,scaleDivergingSqrt:()=>lF,scaleDivergingSymlog:()=>function e(){var t=aI(lU());return t.copy=function(){return lR(t,e()).constant(t.constant())},ir.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,ai),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,ai):[0,1],aP(n)},scaleImplicit:()=>il,scaleLinear:()=>function e(){var t=ad();return t.copy=function(){return as(t,e())},it.apply(t,arguments),aP(t)},scaleLog:()=>function e(){let t=aC(af()).domain([1,10]);return t.copy=()=>as(t,e()).base(t.base()),it.apply(t,arguments),t},scaleOrdinal:()=>ic,scalePoint:()=>is,scalePow:()=>az,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=ij){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[iE(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(ib),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},it.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[iE(a,e,0,i)]:t}function c(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,c()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,c()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},it.apply(aP(l),arguments)},scaleRadial:()=>function e(){var t,r=ad(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(aB(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,ai)).map(aB)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},it.apply(a,arguments),aP(a)},scaleSequential:()=>function e(){var t=aP(lI()(ao));return t.copy=function(){return lR(t,e())},ir.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=aC(lI()).domain([1,10]);return t.copy=function(){return lR(t,e()).base(t.base())},ir.apply(t,arguments)},scaleSequentialPow:()=>l$,scaleSequentialQuantile:()=>function e(){var t=[],r=ao;function n(e){if(null!=e&&!isNaN(e*=1))return r((iE(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(ib),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return aq(e);if(t>=1)return aK(e);var n,i=(n-1)*t,a=Math.floor(i),o=aK((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?aW:function(e=ib){if(e===ib)return aW;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,c=Math.log(o),u=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*u*(o-u)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*u/o+s)),d=Math.min(i,Math.floor(r+(o-l)*u/o+s));e(t,r,f,d,a)}let o=t[r],l=n,c=i;for(aH(t,n,r),a(t[i],o)>0&&aH(t,n,i);l<c;){for(aH(t,l,c),++l,--c;0>a(t[l],o);)++l;for(;a(t[c],o)>0;)--c}0===a(t[n],o)?aH(t,n,c):aH(t,++c,i),c<=r&&(n=c+1),r<=c&&(i=c-1)}return t})(e,a).subarray(0,a+1));return o+(aq(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},ir.apply(n,arguments)},scaleSequentialSqrt:()=>lL,scaleSequentialSymlog:()=>function e(){var t=aI(lI());return t.copy=function(){return lR(t,e()).constant(t.constant())},ir.apply(t,arguments)},scaleSqrt:()=>aF,scaleSymlog:()=>function e(){var t=aI(af());return t.copy=function(){return as(t,e()).constant(t.constant())},it.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[iE(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},it.apply(a,arguments)},scaleTime:()=>lN,scaleUtc:()=>lD,tickFormat:()=>aj});var i=r(687),a=r(3210),o=r(4780);function l({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function c({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function u({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",e),...t})}function s({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",e),...t})}function f(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var d=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){var i;let e,o,l=(i=r,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let n in t){let i=e[n],a=t[n];/^on[A-Z]/.test(n)?i&&a?r[n]=(...e)=>{let t=a(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...a}:"className"===n&&(r[n]=[i,a].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==a.Fragment&&(c.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=f(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():f(e[t],null)}}}}(t,l):l),a.cloneElement(r,c)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:n,...o}=e,l=a.Children.toArray(n),c=l.find(h);if(c){let e=c.props.children,n=l.map(t=>t!==c?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,i.jsx)(t,{...o,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}("Slot"),p=Symbol("radix.slottable");function h(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===p}var y=r(9384);let v=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,g=y.$,m=((e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return g(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:a}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let o=v(t)||v(n);return i[e][o]}),l=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return g(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...l}[t]):({...a,...l})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)})("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function b({className:e,variant:t,asChild:r=!1,...n}){return(0,i.jsx)(r?d:"span",{"data-slot":"badge",className:(0,o.cn)(m({variant:t}),e),...n})}function x({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,i.jsx)("table",{"data-slot":"table",className:(0,o.cn)("w-full caption-bottom text-sm",e),...t})})}function w({className:e,...t}){return(0,i.jsx)("thead",{"data-slot":"table-header",className:(0,o.cn)("[&_tr]:border-b",e),...t})}function O({className:e,...t}){return(0,i.jsx)("tbody",{"data-slot":"table-body",className:(0,o.cn)("[&_tr:last-child]:border-0",e),...t})}function j({className:e,...t}){return(0,i.jsx)("tr",{"data-slot":"table-row",className:(0,o.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function P({className:e,...t}){return(0,i.jsx)("th",{"data-slot":"table-head",className:(0,o.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function E({className:e,...t}){return(0,i.jsx)("td",{"data-slot":"table-cell",className:(0,o.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}var S=r(7766),A=r.n(S),_=r(5664),k=r.n(_),M=e=>0===e?0:e>0?1:-1,T=e=>"number"==typeof e&&e!=+e,C=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,N=e=>("number"==typeof e||e instanceof Number)&&!T(e),D=e=>N(e)||"string"==typeof e,I=0,R=e=>{var t=++I;return"".concat(e||"").concat(t)},$=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!N(e)&&"string"!=typeof e)return n;if(C(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return T(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},L=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},U=(e,t)=>N(e)&&N(t)?r=>e+r*(t-e):()=>t;function z(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):k()(e,t))===r)}var F=e=>null==e,B=e=>F(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),K=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]};function q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function W(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?q(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var H=(0,a.forwardRef)((e,t)=>{var{aspect:r,initialDimension:n={width:-1,height:-1},width:i="100%",height:o="100%",minWidth:l=0,minHeight:c,maxHeight:u,children:s,debounce:f=0,id:d,className:p,onResize:h,style:v={}}=e,g=(0,a.useRef)(null),m=(0,a.useRef)();m.current=h,(0,a.useImperativeHandle)(t,()=>g.current);var[b,x]=(0,a.useState)({containerWidth:n.width,containerHeight:n.height}),w=(0,a.useCallback)((e,t)=>{x(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,a.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;w(r,n),null==(t=m.current)||t.call(m,r,n)};f>0&&(e=A()(e,f,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=g.current.getBoundingClientRect();return w(r,n),t.observe(g.current),()=>{t.disconnect()}},[w,f]);var O=(0,a.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=b;if(e<0||t<0)return null;K(C(i)||C(o),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",i,o),K(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=C(i)?e:i,f=C(o)?t:o;return r&&r>0&&(n?f=n/r:f&&(n=f*r),u&&f>u&&(f=u)),K(n>0||f>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,f,i,o,l,c,r),a.Children.map(s,e=>(0,a.cloneElement)(e,{width:n,height:f,style:W({height:"100%",width:"100%",maxHeight:f,maxWidth:n},e.props.style)}))},[r,s,o,u,c,l,b,i]);return a.createElement("div",{id:d?"".concat(d):void 0,className:(0,y.$)("recharts-responsive-container",p),style:W(W({},v),{},{width:i,height:o,minWidth:l,minHeight:c,maxHeight:u}),ref:g},O)});function V(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var Y="function"==typeof Symbol&&Symbol.observable||"@@observable",X=()=>Math.random().toString(36).substring(7).split("").join("."),G={INIT:`@@redux/INIT${X()}`,REPLACE:`@@redux/REPLACE${X()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${X()}`};function Z(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function Q(e){let t,r=Object.keys(e),n={};for(let t=0;t<r.length;t++){let i=r[t];"function"==typeof e[i]&&(n[i]=e[i])}let i=Object.keys(n);try{Object.keys(n).forEach(e=>{let t=n[e];if(void 0===t(void 0,{type:G.INIT}))throw Error(V(12));if(void 0===t(void 0,{type:G.PROBE_UNKNOWN_ACTION()}))throw Error(V(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let a=!1,o={};for(let t=0;t<i.length;t++){let l=i[t],c=n[l],u=e[l],s=c(u,r);if(void 0===s)throw r&&r.type,Error(V(14));o[l]=s,a=a||s!==u}return(a=a||i.length!==Object.keys(e).length)?o:e}}function J(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function ee(e){return Z(e)&&"type"in e&&"string"==typeof e.type}function et(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var er=et(),en=Symbol.for("immer-nothing"),ei=Symbol.for("immer-draftable"),ea=Symbol.for("immer-state");function eo(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var el=Object.getPrototypeOf;function ec(e){return!!e&&!!e[ea]}function eu(e){return!!e&&(ef(e)||Array.isArray(e)||!!e[ei]||!!e.constructor?.[ei]||ev(e)||eg(e))}var es=Object.prototype.constructor.toString();function ef(e){if(!e||"object"!=typeof e)return!1;let t=el(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===es}function ed(e,t){0===ep(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function ep(e){let t=e[ea];return t?t.type_:Array.isArray(e)?1:ev(e)?2:3*!!eg(e)}function eh(e,t){return 2===ep(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function ey(e,t,r){let n=ep(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function ev(e){return e instanceof Map}function eg(e){return e instanceof Set}function em(e){return e.copy_||e.base_}function eb(e,t){if(ev(e))return new Map(e);if(eg(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=ef(e);if(!0!==t&&("class_only"!==t||r)){let t=el(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[ea];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(el(e),t)}}function ex(e,t=!1){return eO(e)||ec(e)||!eu(e)||(ep(e)>1&&(e.set=e.add=e.clear=e.delete=ew),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>ex(t,!0))),e}function ew(){eo(2)}function eO(e){return Object.isFrozen(e)}var ej={};function eP(e){let t=ej[e];return t||eo(0,e),t}function eE(e,t){t&&(eP("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function eS(e){eA(e),e.drafts_.forEach(ek),e.drafts_=null}function eA(e){e===lX&&(lX=e.parent_)}function e_(e){return lX={drafts_:[],parent_:lX,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function ek(e){let t=e[ea];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function eM(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[ea].modified_&&(eS(t),eo(4)),eu(e)&&(e=eT(t,e),t.parent_||eN(t,e)),t.patches_&&eP("Patches").generateReplacementPatches_(r[ea].base_,e,t.patches_,t.inversePatches_)):e=eT(t,r,[]),eS(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==en?e:void 0}function eT(e,t,r){if(eO(t))return t;let n=t[ea];if(!n)return ed(t,(i,a)=>eC(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return eN(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),ed(i,(i,o)=>eC(e,n,t,i,o,r,a)),eN(e,t,!1),r&&e.patches_&&eP("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function eC(e,t,r,n,i,a,o){if(ec(i)){let o=eT(e,i,a&&t&&3!==t.type_&&!eh(t.assigned_,n)?a.concat(n):void 0);if(ey(r,n,o),!ec(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(eu(i)&&!eO(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;eT(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&eN(e,i)}}function eN(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&ex(t,r)}var eD={get(e,t){if(t===ea)return e;let r=em(e);if(!eh(r,t)){var n=e,i=r,a=t;let o=e$(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let o=r[t];return e.finalized_||!eu(o)?o:o===eR(e.base_,t)?(eU(e),e.copy_[t]=ez(o,e)):o},has:(e,t)=>t in em(e),ownKeys:e=>Reflect.ownKeys(em(e)),set(e,t,r){let n=e$(em(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=eR(em(e),t),i=n?.[ea];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||eh(e.base_,t)))return!0;eU(e),eL(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==eR(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,eU(e),eL(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=em(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){eo(11)},getPrototypeOf:e=>el(e.base_),setPrototypeOf(){eo(12)}},eI={};function eR(e,t){let r=e[ea];return(r?em(r):e)[t]}function e$(e,t){if(!(t in e))return;let r=el(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=el(r)}}function eL(e){!e.modified_&&(e.modified_=!0,e.parent_&&eL(e.parent_))}function eU(e){e.copy_||(e.copy_=eb(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function ez(e,t){let r=ev(e)?eP("MapSet").proxyMap_(e,t):eg(e)?eP("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),n={type_:+!!r,scope_:t?t.scope_:lX,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},i=n,a=eD;r&&(i=[n],a=eI);let{revoke:o,proxy:l}=Proxy.revocable(i,a);return n.draft_=l,n.revoke_=o,l}(e,t);return(t?t.scope_:lX).drafts_.push(r),r}function eF(e){return ec(e)||eo(10,e),function e(t){let r;if(!eu(t)||eO(t))return t;let n=t[ea];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=eb(t,n.scope_.immer_.useStrictShallowCopy_)}else r=eb(t,!0);return ed(r,(t,n)=>{ey(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}ed(eD,(e,t)=>{eI[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),eI.deleteProperty=function(e,t){return eI.set.call(this,e,t,void 0)},eI.set=function(e,t,r){return eD.set.call(this,e[0],t,r,e[0])};var eB=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&eo(6),void 0!==r&&"function"!=typeof r&&eo(7),eu(e)){let i=e_(this),a=ez(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?eS(i):eA(i)}return eE(i,r),eM(n,i)}if(e&&"object"==typeof e)eo(1,e);else{if(void 0===(n=t(e))&&(n=e),n===en&&(n=void 0),this.autoFreeze_&&ex(n,!0),r){let t=[],i=[];eP("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){eu(e)||eo(8),ec(e)&&(e=eF(e));let t=e_(this),r=ez(e,void 0);return r[ea].isManual_=!0,eA(t),r}finishDraft(e,t){let r=e&&e[ea];r&&r.isManual_||eo(9);let{scope_:n}=r;return eE(n,t),eM(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=eP("Patches").applyPatches_;return ec(e)?n(e,t):this.produce(e,e=>n(e,t))}},eK=eB.produce;eB.produceWithPatches.bind(eB),eB.setAutoFreeze.bind(eB),eB.setUseStrictShallowCopy.bind(eB),eB.applyPatches.bind(eB),eB.createDraft.bind(eB),eB.finishDraft.bind(eB);var eq="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?J:J.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var eW=e=>e&&"function"==typeof e.match;function eH(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(tV(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>ee(t)&&t.type===e,r}function eV(e){return["type","payload","error","meta"].indexOf(e)>-1}var eY=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function eX(e){return eu(e)?eK(e,()=>{}):e}function eG(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var eZ=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:i=!0}=e??{},a=new eY;return t&&("boolean"==typeof t?a.push(er):a.push(et(t.extraArgument))),a},eQ=e=>t=>{setTimeout(t,e)},eJ=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:eQ(10):"callback"===e.type?e.queueNotification:eQ(e.timeout),u=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,c(u)),n.dispatch(e)}finally{i=!0}}})},e0=e=>function(t){let{autoBatch:r=!0}=t??{},n=new eY(e);return r&&n.push(eJ("object"==typeof r?r:void 0)),n};function e1(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(tV(28));if(n in r)throw Error(tV(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var e2=(e,t)=>eW(e)?e.match(t):e(t);function e3(...e){return t=>e.some(e=>e2(e,t))}var e5=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},e6=["name","message","stack","code"],e4=class{constructor(e,t){this.payload=e,this.meta=t}_type},e8=class{constructor(e,t){this.payload=e,this.meta=t}_type},e7=e=>{if("object"==typeof e&&null!==e){let t={};for(let r of e6)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},e9="External signal was aborted";function te(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var tt=Symbol.for("rtk-slice-createasyncthunk"),tr=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(tr||{}),tn=function({creators:e}={}){let t=e?.asyncThunk?.[tt];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(tV(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},o=Object.keys(a),l={},c={},u={},s=[],f={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(tV(12));if(r in c)throw Error(tV(13));return c[r]=t,f},addMatcher:(e,t)=>(s.push({matcher:e,reducer:t}),f),exposeAction:(e,t)=>(u[e]=t,f),exposeCaseReducer:(e,t)=>(l[e]=t,f)};function d(){let[t={},r=[],n]="function"==typeof e.extraReducers?e1(e.extraReducers):[e.extraReducers],i={...t,...c};return function(e,t){let r,[n,i,a]=e1(t);if("function"==typeof e)r=()=>eX(e());else{let t=eX(e);r=()=>t}function o(e=r(),t){let l=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===l.filter(e=>!!e).length&&(l=[a]),l.reduce((e,r)=>{if(r)if(ec(e)){let n=r(e,t);return void 0===n?e:n}else{if(eu(e))return eK(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return o.getInitialState=r,o}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of s)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}o.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(tV(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:c,settled:u,options:s}=r,f=i(e,a,s);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),c&&n.addCase(f.rejected,c),u&&n.addMatcher(f.settled,u),n.exposeCaseReducer(t,{fulfilled:o||ti,pending:l||ti,rejected:c||ti,settled:u||ti})}(o,i,f,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(tV(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?eH(e,o):eH(e))}(o,i,f)});let p=e=>e,h=new Map,y=new WeakMap;function v(e,t){return r||(r=d()),r(e,t)}function g(){return r||(r=d()),r.getInitialState()}function m(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=eG(y,n,g)),i}function i(t=p){let n=eG(h,r,()=>new WeakMap);return eG(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>eG(y,t,g),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let b={name:n,reducer:v,actions:u,caseReducers:l,getInitialState:g,...m(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:v},r),{...b,...m(n,!0)}}};return b}}();function ti(){}function ta(e){return function(t,r){let n=t=>{isAction(r)&&Object.keys(r).every(eV)?e(r.payload,t):e(r,t)};return(null)(t)?(n(t),t):createNextState3(t,n)}}function to(e,t){return t(e)}function tl(e){return Array.isArray(e)||(e=Object.values(e)),e}var tc="listener",tu="completed",ts="cancelled",tf=`task-${ts}`,td=`task-${tu}`,tp=`${tc}-${ts}`,th=`${tc}-${tu}`,ty=class{constructor(e){this.code=e,this.message=`task ${ts} (reason: ${e})`}name="TaskAbortError";message},tv=(e,t)=>{if("function"!=typeof e)throw TypeError(tV(32))},tg=()=>{},tm=(e,t=tg)=>(e.catch(t),e),tb=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),tx=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},tw=e=>{if(e.aborted){let{reason:t}=e;throw new ty(t)}};function tO(e,t){let r=tg;return new Promise((n,i)=>{let a=()=>i(new ty(e.reason));if(e.aborted)return void a();r=tb(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=tg})}var tj=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof ty?"cancelled":"rejected",error:e}}finally{t?.()}},tP=e=>t=>tm(tO(e,t).then(t=>(tw(e),t))),tE=e=>{let t=tP(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:tS}=Object,tA={},t_="listenerMiddleware",tk=(e,t)=>{let r=t=>tb(e,()=>tx(t,e.reason));return(n,i)=>{tv(n,"taskExecutor");let a=new AbortController;r(a);let o=tj(async()=>{tw(e),tw(a.signal);let t=await n({pause:tP(a.signal),delay:tE(a.signal),signal:a.signal});return tw(a.signal),t},()=>tx(a,td));return i?.autoJoin&&t.push(o.catch(tg)),{result:tP(e)(o),cancel(){tx(a,tf)}}}},tM=(e,t)=>{let r=async(r,n)=>{tw(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await tO(t,Promise.race(a));return tw(t),e}finally{i()}};return(e,t)=>tm(r(e,t))},tT=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=eH(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(tV(21));return tv(a,"options.listener"),{predicate:i,type:t,effect:a}},tC=tS(e=>{let{type:t,predicate:r,effect:n}=tT(e);return{id:e5(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(tV(22))}}},{withTypes:()=>tC}),tN=(e,t)=>{let{type:r,effect:n,predicate:i}=tT(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},tD=e=>{e.pending.forEach(e=>{tx(e,tp)})},tI=e=>()=>{e.forEach(tD),e.clear()},tR=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},t$=tS(eH(`${t_}/add`),{withTypes:()=>t$}),tL=eH(`${t_}/removeAll`),tU=tS(eH(`${t_}/remove`),{withTypes:()=>tU}),tz=(...e)=>{console.error(`${t_}/error`,...e)},tF=(e={})=>{let t=new Map,{extra:r,onError:n=tz}=e;tv(n,"onError");let i=e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&tD(e)}),a=e=>i(tN(t,e)??tC(e));tS(a,{withTypes:()=>a});let o=e=>{let r=tN(t,e);return r&&(r.unsubscribe(),e.cancelActive&&tD(r)),!!r};tS(o,{withTypes:()=>o});let l=async(e,i,o,l)=>{let c=new AbortController,u=tM(a,c.signal),s=[];try{e.pending.add(c),await Promise.resolve(e.effect(i,tS({},o,{getOriginalState:l,condition:(e,t)=>u(e,t).then(Boolean),take:u,delay:tE(c.signal),pause:tP(c.signal),extra:r,signal:c.signal,fork:tk(c.signal,s),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==c&&(tx(e,tp),r.delete(e))})},cancel:()=>{tx(c,tp),e.pending.delete(c)},throwIfCancelled:()=>{tw(c.signal)}})))}catch(e){e instanceof ty||tR(n,e,{raisedBy:"effect"})}finally{await Promise.all(s),tx(c,th),e.pending.delete(c)}},c=tI(t);return{middleware:e=>r=>i=>{let u;if(!ee(i))return r(i);if(t$.match(i))return a(i.payload);if(tL.match(i))return void c();if(tU.match(i))return o(i.payload);let s=e.getState(),f=()=>{if(s===tA)throw Error(tV(23));return s};try{if(u=r(i),t.size>0){let r=e.getState();for(let a of Array.from(t.values())){let t=!1;try{t=a.predicate(i,r,s)}catch(e){t=!1,tR(n,e,{raisedBy:"predicate"})}t&&l(a,i,e,f)}}}finally{s=tA}return u},startListening:a,stopListening:o,clearListeners:c}},tB=e=>"reducerPath"in e&&"string"==typeof e.reducerPath,tK=Symbol.for("rtk-state-proxy-original"),tq=e=>!!e&&!!e[tK],tW=new WeakMap,tH={};function tV(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}function tY(e,t){if(t){var r=Number.parseInt(t,10);if(!T(r))return null==e?void 0:e[r]}}var tX=tn({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),tG=tX.reducer,{createEventEmitter:tZ}=tX.actions;r(6895);var tQ=Symbol.for("react.forward_ref"),tJ=Symbol.for("react.memo");function t0(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var t1={notify(){},get:()=>[]},t2="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,t3="undefined"!=typeof navigator&&"ReactNative"===navigator.product,t5=t2||t3?a.useLayoutEffect:a.useEffect;function t6(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}var t4={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},t8={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},t7={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},t9={[tQ]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[tJ]:t7};function re(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case tQ:case null:case tJ:case null:return e;default:return t}}case null:return t}}}(e)===tJ?t7:t9[e.$$typeof]||t4}var rt=Object.defineProperty,rr=Object.getOwnPropertyNames,rn=Object.getOwnPropertySymbols,ri=Object.getOwnPropertyDescriptor,ra=Object.getPrototypeOf,ro=Object.prototype,rl=Symbol.for("react-redux-context"),rc="undefined"!=typeof globalThis?globalThis:{},ru=function(){if(!a.createContext)return{};let e=rc[rl]??=new Map,t=e.get(a.createContext);return t||(t=a.createContext(null),e.set(a.createContext,t)),t}(),rs=function(e){let{children:t,context:r,serverState:n,store:i}=e,o=a.useMemo(()=>{let e=function(e,t){let r,n=t1,i=0,a=!1;function o(){u.onStateChange&&u.onStateChange()}function l(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function c(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=t1)}let u={addNestedSub:function(e){l();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,l())},tryUnsubscribe:function(){a&&(a=!1,c())},getListeners:()=>n};return u}(i);return{store:i,subscription:e,getServerState:n?()=>n:void 0}},[i,n]),l=a.useMemo(()=>i.getState(),[i]);return t5(()=>{let{subscription:e}=o;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),l!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[o,l]),a.createElement((r||ru).Provider,{value:o},t)},rf={active:!1,index:null,dataKey:void 0,coordinate:void 0},rd=tn({name:"tooltip",initialState:{itemInteraction:{click:rf,hover:rf},axisInteraction:{click:rf,hover:rf},keyboardInteraction:rf,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=eF(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:rp,removeTooltipEntrySettings:rh,setTooltipSettingsState:ry,setActiveMouseOverItemIndex:rv,mouseLeaveItem:rg,mouseLeaveChart:rm,setActiveClickItemIndex:rb,setMouseOverAxisIndex:rx,setMouseClickAxisIndex:rw,setSyncInteraction:rO,setKeyboardInteraction:rj}=rd.actions,rP=rd.reducer,rE=tn({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:rS,setDataStartEndIndexes:rA,setComputedData:r_}=rE.actions,rk=rE.reducer,rM=tn({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:rT,setLayout:rC,setChartSize:rN,setScale:rD}=rM.actions,rI=rM.reducer,rR=e=>Array.isArray(e)?e:[e],r$=0,rL=null,rU=class{revision=r$;_value;_lastValue;_isEqual=rz;constructor(e,t=rz){this._value=this._lastValue=e,this._isEqual=t}get value(){return rL?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++r$)}};function rz(e,t){return e===t}function rF(e){return e instanceof rU||console.warn("Not a valid cell! ",e),e.value}var rB=(e,t)=>!1;function rK(){return function(e,t=rz){return new rU(null,t)}(0,rB)}var rq=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=rK()),rF(t)};Symbol();var rW=0,rH=Object.getPrototypeOf({}),rV=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,rY);tag=rK();tags={};children={};collectionTag=null;id=rW++},rY={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in rH)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=function(e){return Array.isArray(e)?new rX(e):new rV(e)}(n)),r.tag&&rF(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=rK()).value=n),rF(r),n}})(),ownKeys:e=>(rq(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},rX=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],rG);tag=rK();tags={};children={};collectionTag=null;id=rW++},rG={get:([e],t)=>("length"===t&&rq(e),rY.get(e,t)),ownKeys:([e])=>rY.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>rY.getOwnPropertyDescriptor(e,t),has:([e],t)=>rY.has(e,t)},rZ="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function rQ(){return{s:0,v:void 0,o:null,p:null}}function rJ(e,t={}){let r,n=rQ(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=rQ(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=rQ(),e.set(t,o)):o=r}}let c=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new rZ(t):t}return c.s=1,c.v=t,t}return o.clearCache=()=>{n=rQ(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var r0=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,i=0,a={},o=e.pop();"object"==typeof o&&(a=o,o=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);let{memoize:l,memoizeOptions:c=[],argsMemoize:u=rJ,argsMemoizeOptions:s=[],devModeChecks:f={}}={...r,...a},d=rR(c),p=rR(s),h=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),y=l(function(){return n++,o.apply(null,arguments)},...d);return Object.assign(u(function(){i++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(h,arguments);return t=y.apply(null,e)},...p),{resultFunc:o,memoizedResultFunc:y,dependencies:h,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:l,argsMemoize:u})};return Object.assign(n,{withTypes:()=>n}),n}(rJ),r1=Object.assign((e,t=r0)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>r1}),r2=r(9733),r3=(0,a.createContext)(null),r5=e=>e,r6=()=>{var e=(0,a.useContext)(r3);return e?e.store.dispatch:r5},r4=()=>{},r8=()=>r4,r7=(e,t)=>e===t;function r9(e){var t=(0,a.useContext)(r3);return(0,r2.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:r8,t?t.store.getState:r4,t?t.store.getState:r4,t?e:r4,r7)}var ne=r(3068),nt=r.n(ne),nr=e=>e.legend.settings;function nn(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function ni(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function na(e){return function(){return e}}function no(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function nl(e,t){return e[t]}function nc(e){let t=[];return t.key=e,t}function nu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ns(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nu(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nu(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r0([e=>e.legend.payload,nr],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?nt()(n,r):n}),Array.prototype.slice;var nf=Math.PI/180,nd=e=>180*e/Math.PI,np=(e,t,r,n)=>({x:e+Math.cos(-nf*n)*r,y:t+Math.sin(-nf*n)*r}),nh=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},ny=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=nh({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=Math.acos((r-i)/o);return n>a&&(l=2*Math.PI-l),{radius:o,angle:nd(l),angleInRadian:l}},nv=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},ng=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},nm=(e,t)=>{var r,{x:n,y:i}=e,{radius:a,angle:o}=ny({x:n,y:i},t),{innerRadius:l,outerRadius:c}=t;if(a<l||a>c||0===a)return null;var{startAngle:u,endAngle:s}=nv(t),f=o;if(u<=s){for(;f>s;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=s}else{for(;f>u;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=u}return r?ns(ns({},t),{},{radius:a,angle:ng(f,t)}):null};function nb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nb(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nb(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nw(e,t,r){return F(e)||F(t)?r:D(t)?k()(e,t,r):"function"==typeof t?t(e):r}var nO=(e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var c=0;c<l;c++){var u=c>0?r[c-1].coordinate:r[l-1].coordinate,s=r[c].coordinate,f=c>=l-1?r[0].coordinate:r[c+1].coordinate,d=void 0;if(M(s-u)!==M(f-s)){var p=[];if(M(f-s)===M(i[1]-i[0])){d=f;var h=s+i[1]-i[0];p[0]=Math.min(h,(h+u)/2),p[1]=Math.max(h,(h+u)/2)}else{d=u;var y=f+i[1]-i[0];p[0]=Math.min(s,(y+s)/2),p[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(d+s)/2),Math.max(s,(d+s)/2)];if(e>v[0]&&e<=v[1]||e>=p[0]&&e<=p[1]){({index:o}=r[c]);break}}else{var g=Math.min(u,f),m=Math.max(u,f);if(e>(g+s)/2&&e<=(m+s)/2){({index:o}=r[c]);break}}}else if(t){for(var b=0;b<l;b++)if(0===b&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b>0&&b<l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b===l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2){({index:o}=t[b]);break}}return o},nj=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&N(e[a]))return nx(nx({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&N(e[o]))return nx(nx({},e),{},{[o]:e[o]+(i||0)})}return e},nP=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,nE=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},nS=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:u,tickCount:s,ticks:f,niceTicks:d,axisType:p}=e;if(!o)return null;var h="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,y=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/h:0;return(y="angleAxis"===p&&a&&a.length>=2?2*M(a[0]-a[1])*y:y,t&&(f||d))?(f||d||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+y,value:e,offset:y,index:t})).filter(e=>!T(e.coordinate)):c&&u?u.map((e,t)=>({coordinate:o(e)+y,value:e,index:t,offset:y})):o.ticks&&!r&&null!=s?o.ticks(s).map((e,t)=>({coordinate:o(e)+y,value:e,offset:y,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+y,value:n?n[e]:e,index:t,offset:y}))},nA=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},n_={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=T(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}nn(e,t)}},none:nn,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}nn(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,c=0,u=0;l<i;++l){for(var s=e[t[l]],f=s[o][1]||0,d=(f-(s[o-1][1]||0))/2,p=0;p<l;++p){var h=e[t[p]];d+=(h[o][1]||0)-(h[o-1][1]||0)}c+=f,u+=d*f}r[o-1][1]+=r[o-1][0]=a,c&&(a-=u/c)}r[o-1][1]+=r[o-1][0]=a,nn(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=T(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},nk=(e,t,r)=>{var n=n_[r];return(function(){var e=na([]),t=no,r=nn,n=nl;function i(i){var a,o,l=Array.from(e.apply(this,arguments),nc),c=l.length,u=-1;for(let e of i)for(a=0,++u;a<c;++a)(l[a][u]=[0,+n(e,l[a].key,u,i)]).data=e;for(a=0,o=ni(t(l));a<c;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:na(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:na(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?no:"function"==typeof e?e:na(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?nn:e,i):r},i})().keys(t).value((e,t)=>+nw(e,t,0)).order(no).offset(n)(e)};function nM(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!F(i[t.dataKey])){var l=z(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=nw(i,F(o)?t.dataKey:o);return F(c)?null:t.scale(c)}var nT=e=>{var t=e.flat(2).filter(N);return[Math.min(...t),Math.max(...t)]},nC=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],nN=(e,t,r)=>{if(null!=e)return nC(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=nT(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},nD=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,nI=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,nR=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=nt()(t,e=>e.coordinate),a=1/0,o=1,l=i.length;o<l;o++){var c=i[o],u=i[o-1];a=Math.min((c.coordinate||0)-(u.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function n$(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return nx(nx({},t),{},{dataKey:r,payload:n,value:i,name:a})}function nL(e,t){return e?String(e):"string"==typeof t?t:void 0}var nU=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return nx(nx(nx({},n),np(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:c}=n;return nx(nx(nx({},n),np(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}},nz=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius,nF=e=>e.layout.width,nB=e=>e.layout.height,nK=e=>e.layout.scale,nq=e=>e.layout.margin,nW=r0(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),nH=r0(e=>e.cartesianAxis.yAxis,e=>Object.values(e));function nV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nV(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nX=r0([nF,nB,nq,e=>e.brush.height,nW,nH,nr,e=>e.legend.size],(e,t,r,n,i,a,o,l)=>{var c=a.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:60;return nY(nY({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),u=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:nY(nY({},e),{},{[r]:k()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),s=nY(nY({},u),c),f=s.bottom;s.bottom+=n;var d=e-(s=nj(s,o,l)).left-s.right,p=t-s.top-s.bottom;return nY(nY({brushBottom:f},s),{},{width:Math.max(d,0),height:Math.max(p,0)})}),nG=r0(nX,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),nZ=r0(nF,nB,(e,t)=>({x:0,y:0,width:e,height:t})),nQ=(0,a.createContext)(null),nJ=()=>null!=(0,a.useContext)(nQ),n0=e=>e.brush,n1=r0([n0,nX,nq],(e,t,r)=>({height:e.height,x:N(e.x)?e.x:t.left,y:N(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:N(e.width)?e.width:t.width})),n2=()=>{var e,t=nJ(),r=r9(nG),n=r9(n1),i=null==(e=r9(n0))?void 0:e.padding;return t&&n&&i?{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}:r},n3={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},n5=()=>{var e;return null!=(e=r9(nX))?e:n3},n6=()=>r9(nF),n4=()=>r9(nB),n8=e=>e.layout.layoutType,n7=()=>r9(n8),n9=r(921),ie=r.n(n9);function it(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function ir(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class ii extends Map{constructor(e,t=io){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(ia(this,e))}has(e){return super.has(ia(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function ia({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function io(e){return null!==e&&"object"==typeof e?e.valueOf():e}let il=Symbol("implicit");function ic(){var e=new ii,t=[],r=[],n=il;function i(i){let a=e.get(i);if(void 0===a){if(n!==il)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new ii,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return ic(t,r).unknown(n)},it.apply(i,arguments),i}function iu(){var e,t,r=ic().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,c=0,u=0,s=.5;function f(){var r=n().length,f=o<a,d=f?o:a,p=f?a:o;e=(p-d)/Math.max(1,r-c+2*u),l&&(e=Math.floor(e)),d+=(p-d-e*(r-c))*s,t=e*(1-c),l&&(d=Math.round(d),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return d+e*t});return i(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(c=Math.min(1,u=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(u=+e,f()):u},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return iu(n(),[a,o]).round(l).paddingInner(c).paddingOuter(u).align(s)},it.apply(f(),arguments)}function is(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(iu.apply(null,arguments).paddingInner(1))}let id=Math.sqrt(50),ip=Math.sqrt(10),ih=Math.sqrt(2);function iy(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),c=o/Math.pow(10,l),u=c>=id?10:c>=ip?5:c>=ih?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/u)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*u)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?iy(e,t,2*r):[n,i,a]}function iv(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?iy(t,e,r):iy(e,t,r);if(!(a>=i))return[];let l=a-i+1,c=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)c[e]=-((a-e)/o);else for(let e=0;e<l;++e)c[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)c[e]=-((i+e)/o);else for(let e=0;e<l;++e)c[e]=(i+e)*o;return c}function ig(e,t,r){return iy(e*=1,t*=1,r*=1)[2]}function im(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?ig(t,e,r):ig(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function ib(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ix(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function iw(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=ib,r=(t,r)=>ib(e(t),r),n=(t,r)=>e(t)-r):(t=e===ib||e===ix?e:iO,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function iO(){return 0}function ij(e){return null===e?NaN:+e}let iP=iw(ib),iE=iP.right;function iS(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function iA(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function i_(){}iP.left,iw(ij).center;var ik="\\s*([+-]?\\d+)\\s*",iM="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",iT="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",iC=/^#([0-9a-f]{3,8})$/,iN=RegExp(`^rgb\\(${ik},${ik},${ik}\\)$`),iD=RegExp(`^rgb\\(${iT},${iT},${iT}\\)$`),iI=RegExp(`^rgba\\(${ik},${ik},${ik},${iM}\\)$`),iR=RegExp(`^rgba\\(${iT},${iT},${iT},${iM}\\)$`),i$=RegExp(`^hsl\\(${iM},${iT},${iT}\\)$`),iL=RegExp(`^hsla\\(${iM},${iT},${iT},${iM}\\)$`),iU={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function iz(){return this.rgb().formatHex()}function iF(){return this.rgb().formatRgb()}function iB(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=iC.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?iK(t):3===r?new iH(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?iq(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?iq(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=iN.exec(e))?new iH(t[1],t[2],t[3],1):(t=iD.exec(e))?new iH(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=iI.exec(e))?iq(t[1],t[2],t[3],t[4]):(t=iR.exec(e))?iq(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=i$.exec(e))?iQ(t[1],t[2]/100,t[3]/100,1):(t=iL.exec(e))?iQ(t[1],t[2]/100,t[3]/100,t[4]):iU.hasOwnProperty(e)?iK(iU[e]):"transparent"===e?new iH(NaN,NaN,NaN,0):null}function iK(e){return new iH(e>>16&255,e>>8&255,255&e,1)}function iq(e,t,r,n){return n<=0&&(e=t=r=NaN),new iH(e,t,r,n)}function iW(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof i_||(i=iB(i)),i)?new iH((i=i.rgb()).r,i.g,i.b,i.opacity):new iH:new iH(e,t,r,null==n?1:n)}function iH(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function iV(){return`#${iZ(this.r)}${iZ(this.g)}${iZ(this.b)}`}function iY(){let e=iX(this.opacity);return`${1===e?"rgb(":"rgba("}${iG(this.r)}, ${iG(this.g)}, ${iG(this.b)}${1===e?")":`, ${e})`}`}function iX(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function iG(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function iZ(e){return((e=iG(e))<16?"0":"")+e.toString(16)}function iQ(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new i0(e,t,r,n)}function iJ(e){if(e instanceof i0)return new i0(e.h,e.s,e.l,e.opacity);if(e instanceof i_||(e=iB(e)),!e)return new i0;if(e instanceof i0)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,c=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=c<.5?a+i:2-a-i,o*=60):l=c>0&&c<1?0:o,new i0(o,l,c,e.opacity)}function i0(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function i1(e){return(e=(e||0)%360)<0?e+360:e}function i2(e){return Math.max(0,Math.min(1,e||0))}function i3(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function i5(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}iS(i_,iB,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:iz,formatHex:iz,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return iJ(this).formatHsl()},formatRgb:iF,toString:iF}),iS(iH,iW,iA(i_,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new iH(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new iH(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new iH(iG(this.r),iG(this.g),iG(this.b),iX(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:iV,formatHex:iV,formatHex8:function(){return`#${iZ(this.r)}${iZ(this.g)}${iZ(this.b)}${iZ((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:iY,toString:iY})),iS(i0,function(e,t,r,n){return 1==arguments.length?iJ(e):new i0(e,t,r,null==n?1:n)},iA(i_,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new i0(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new i0(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new iH(i3(e>=240?e-240:e+120,i,n),i3(e,i,n),i3(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new i0(i1(this.h),i2(this.s),i2(this.l),iX(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=iX(this.opacity);return`${1===e?"hsl(":"hsla("}${i1(this.h)}, ${100*i2(this.s)}%, ${100*i2(this.l)}%${1===e?")":`, ${e})`}`}}));let i6=e=>()=>e;function i4(e,t){var r,n,i=t-e;return i?(r=e,n=i,function(e){return r+e*n}):i6(isNaN(e)?t:e)}let i8=function e(t){var r,n=1==(r=+t)?i4:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):i6(isNaN(e)?t:e)};function i(e,t){var r=n((e=iW(e)).r,(t=iW(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=i4(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function i7(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=iW(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}i7(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return i5((r-n/t)*t,o,i,a,l)}}),i7(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return i5((r-n/t)*t,i,a,o,l)}});function i9(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var ae=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,at=RegExp(ae.source,"g");function ar(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?i6(t):("number"===i?i9:"string"===i?(n=iB(t))?(t=n,i8):function(e,t){var r,n,i,a,o,l=ae.lastIndex=at.lastIndex=0,c=-1,u=[],s=[];for(e+="",t+="";(i=ae.exec(e))&&(a=at.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),u[c]?u[c]+=o:u[++c]=o),(i=i[0])===(a=a[0])?u[c]?u[c]+=a:u[++c]=a:(u[++c]=null,s.push({i:c,x:i9(i,a)})),l=at.lastIndex;return l<t.length&&(o=t.slice(l),u[c]?u[c]+=o:u[++c]=o),u.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)u[(r=s[n]).i]=r.x(e);return u.join("")})}:t instanceof iB?i8:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=ar(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=ar(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:i9:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function an(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function ai(e){return+e}var aa=[0,1];function ao(e){return e}function al(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function ac(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=al(i,n),a=r(o,a)):(n=al(n,i),a=r(a,o)),function(e){return a(n(e))}}function au(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=al(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=iE(e,t,1,n)-1;return a[r](i[r](t))}}function as(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function af(){var e,t,r,n,i,a,o=aa,l=aa,c=ar,u=ao;function s(){var e,t,r,c=Math.min(o.length,l.length);return u!==ao&&(e=o[0],t=o[c-1],e>t&&(r=e,e=t,t=r),u=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?au:ac,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,c)))(e(u(t)))}return f.invert=function(r){return u(t((a||(a=n(l,o.map(e),i9)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,ai),s()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),c=an,s()},f.clamp=function(e){return arguments.length?(u=!!e||ao,s()):u!==ao},f.interpolate=function(e){return arguments.length?(c=e,s()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function ad(){return af()(ao,ao)}var ap=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ah(e){var t;if(!(t=ap.exec(e)))throw Error("invalid format: "+e);return new ay({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function ay(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function av(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function ag(e){return(e=av(Math.abs(e)))?e[1]:NaN}function am(e,t){var r=av(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}ah.prototype=ay.prototype,ay.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ab={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>am(100*e,t),r:am,s:function(e,t){var r=av(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(lG=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+Array(1-a).join("0")+av(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function ax(e){return e}var aw=Array.prototype.map,aO=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function aj(e,t,r,n){var i,a,o,l=im(e,t,r);switch((n=ah(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(o=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ag(c)/3)))-ag(Math.abs(l))))||(n.precision=o),lJ(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(o=Math.max(0,ag(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=l)))-ag(i))+1)||(n.precision=o-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(o=Math.max(0,-ag(Math.abs(l))))||(n.precision=o-("%"===n.type)*2)}return lQ(n)}function aP(e){var t=e.domain;return e.ticks=function(e){var r=t();return iv(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return aj(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,c=a[o],u=a[l],s=10;for(u<c&&(i=c,c=u,u=i,i=o,o=l,l=i);s-- >0;){if((i=ig(c,u,r))===n)return a[o]=c,a[l]=u,t(a);if(i>0)c=Math.floor(c/i)*i,u=Math.ceil(u/i)*i;else if(i<0)c=Math.ceil(c*i)/i,u=Math.floor(u*i)/i;else break;n=i}return e},e}function aE(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function aS(e){return Math.log(e)}function aA(e){return Math.exp(e)}function a_(e){return-Math.log(-e)}function ak(e){return-Math.exp(-e)}function aM(e){return isFinite(e)?+("1e"+e):e<0?0:e}function aT(e){return(t,r)=>-e(-t,r)}function aC(e){let t,r,n=e(aS,aA),i=n.domain,a=10;function o(){var o,l;return t=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),e=>Math.log(e)/o),r=10===(l=a)?aM:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=aT(t),r=aT(r),e(a_,ak)):e(aS,aA),n}return n.base=function(e){return arguments.length?(a=+e,o()):a},n.domain=function(e){return arguments.length?(i(e),o()):i()},n.ticks=e=>{let n,o,l=i(),c=l[0],u=l[l.length-1],s=u<c;s&&([c,u]=[u,c]);let f=t(c),d=t(u),p=null==e?10:+e,h=[];if(!(a%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),c>0){for(;f<=d;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<c)){if(o>u)break;h.push(o)}}else for(;f<=d;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<c)){if(o>u)break;h.push(o)}2*h.length<p&&(h=iv(c,u,p))}else h=iv(f,d,Math.min(d-f,p)).map(r);return s?h.reverse():h},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=ah(i)).precision||(i.trim=!0),i=lQ(i)),e===1/0)return i;let o=Math.max(1,a*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*a<a-.5&&(n*=a),n<=o?i(e):""}},n.nice=()=>i(aE(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function aN(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function aD(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function aI(e){var t=1,r=e(aN(1),aD(t));return r.constant=function(r){return arguments.length?e(aN(t=+r),aD(t)):t},aP(r)}function aR(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function a$(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function aL(e){return e<0?-e*e:e*e}function aU(e){var t=e(ao,ao),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(ao,ao):.5===r?e(a$,aL):e(aR(r),aR(1/r)):r},aP(t)}function az(){var e=aU(af());return e.copy=function(){return as(e,az()).exponent(e.exponent())},it.apply(e,arguments),e}function aF(){return az.apply(null,arguments).exponent(.5)}function aB(e){return Math.sign(e)*e*e}function aK(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function aq(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}lQ=(lZ=function(e){var t,r,n,i=void 0===e.grouping||void 0===e.thousands?ax:(t=aw.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],c=0;i>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),a.push(e.substring(i-=l,i+l)),!((c+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),a=void 0===e.currency?"":e.currency[0]+"",o=void 0===e.currency?"":e.currency[1]+"",l=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?ax:(n=aw.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),u=void 0===e.percent?"%":e.percent+"",s=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=ah(e)).fill,r=e.align,n=e.sign,d=e.symbol,p=e.zero,h=e.width,y=e.comma,v=e.precision,g=e.trim,m=e.type;"n"===m?(y=!0,m="g"):ab[m]||(void 0===v&&(v=12),g=!0,m="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var b="$"===d?a:"#"===d&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",x="$"===d?o:/[%p]/.test(m)?u:"",w=ab[m],O=/[defgprs%]/.test(m);function j(e){var a,o,u,d=b,j=x;if("c"===m)j=w(e)+j,e="";else{var P=(e*=1)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),v),g&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),P&&0==+e&&"+"!==n&&(P=!1),d=(P?"("===n?n:s:"-"===n||"("===n?"":n)+d,j=("s"===m?aO[8+lG/3]:"")+j+(P&&"("===n?")":""),O){for(a=-1,o=e.length;++a<o;)if(48>(u=e.charCodeAt(a))||u>57){j=(46===u?l+e.slice(a+1):e.slice(a))+j,e=e.slice(0,a);break}}}y&&!p&&(e=i(e,1/0));var E=d.length+e.length+j.length,S=E<h?Array(h-E+1).join(t):"";switch(y&&p&&(e=i(S+e,S.length?h-j.length:1/0),S=""),r){case"<":e=d+e+j+S;break;case"=":e=d+S+e+j;break;case"^":e=S.slice(0,E=S.length>>1)+d+e+j+S.slice(E);break;default:e=S+d+e+j}return c(e)}return v=void 0===v?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return e+""},j}return{format:d,formatPrefix:function(e,t){var r=d(((e=ah(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(ag(t)/3))),i=Math.pow(10,-n),a=aO[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,lJ=lZ.formatPrefix;function aW(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function aH(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let aV=new Date,aY=new Date;function aX(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>aX(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(aV.setTime(+t),aY.setTime(+n),e(aV),e(aY),Math.floor(r(aV,aY))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let aG=aX(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);aG.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?aX(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):aG:null,aG.range;let aZ=aX(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());aZ.range;let aQ=aX(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());aQ.range;let aJ=aX(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());aJ.range;let a0=aX(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());a0.range;let a1=aX(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());a1.range;let a2=aX(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);a2.range;let a3=aX(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);a3.range;let a5=aX(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function a6(e){return aX(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}a5.range;let a4=a6(0),a8=a6(1),a7=a6(2),a9=a6(3),oe=a6(4),ot=a6(5),or=a6(6);function on(e){return aX(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}a4.range,a8.range,a7.range,a9.range,oe.range,ot.range,or.range;let oi=on(0),oa=on(1),oo=on(2),ol=on(3),oc=on(4),ou=on(5),os=on(6);oi.range,oa.range,oo.range,ol.range,oc.range,ou.range,os.range;let of=aX(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());of.range;let od=aX(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());od.range;let op=aX(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());op.every=e=>isFinite(e=Math.floor(e))&&e>0?aX(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,op.range;let oh=aX(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function oy(e,t,r,n,i,a){let o=[[aZ,1,1e3],[aZ,5,5e3],[aZ,15,15e3],[aZ,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=iw(([,,e])=>e).right(o,i);if(a===o.length)return e.every(im(t/31536e6,r/31536e6,n));if(0===a)return aG.every(Math.max(im(t,r,n),1));let[l,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(c)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}oh.every=e=>isFinite(e=Math.floor(e))&&e>0?aX(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,oh.range;let[ov,og]=oy(oh,od,oi,a5,a1,aJ),[om,ob]=oy(op,of,a4,a2,a0,aQ);function ox(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function ow(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function oO(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var oj={"-":"",_:" ",0:"0"},oP=/^\s*\d+/,oE=/^%/,oS=/[\\^$*+?|[\]().{}]/g;function oA(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function o_(e){return e.replace(oS,"\\$&")}function ok(e){return RegExp("^(?:"+e.map(o_).join("|")+")","i")}function oM(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function oT(e,t,r){var n=oP.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function oC(e,t,r){var n=oP.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function oN(e,t,r){var n=oP.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function oD(e,t,r){var n=oP.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function oI(e,t,r){var n=oP.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function oR(e,t,r){var n=oP.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function o$(e,t,r){var n=oP.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function oL(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function oU(e,t,r){var n=oP.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function oz(e,t,r){var n=oP.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function oF(e,t,r){var n=oP.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function oB(e,t,r){var n=oP.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function oK(e,t,r){var n=oP.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function oq(e,t,r){var n=oP.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function oW(e,t,r){var n=oP.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function oH(e,t,r){var n=oP.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function oV(e,t,r){var n=oP.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function oY(e,t,r){var n=oE.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function oX(e,t,r){var n=oP.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function oG(e,t,r){var n=oP.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function oZ(e,t){return oA(e.getDate(),t,2)}function oQ(e,t){return oA(e.getHours(),t,2)}function oJ(e,t){return oA(e.getHours()%12||12,t,2)}function o0(e,t){return oA(1+a2.count(op(e),e),t,3)}function o1(e,t){return oA(e.getMilliseconds(),t,3)}function o2(e,t){return o1(e,t)+"000"}function o3(e,t){return oA(e.getMonth()+1,t,2)}function o5(e,t){return oA(e.getMinutes(),t,2)}function o6(e,t){return oA(e.getSeconds(),t,2)}function o4(e){var t=e.getDay();return 0===t?7:t}function o8(e,t){return oA(a4.count(op(e)-1,e),t,2)}function o7(e){var t=e.getDay();return t>=4||0===t?oe(e):oe.ceil(e)}function o9(e,t){return e=o7(e),oA(oe.count(op(e),e)+(4===op(e).getDay()),t,2)}function le(e){return e.getDay()}function lt(e,t){return oA(a8.count(op(e)-1,e),t,2)}function lr(e,t){return oA(e.getFullYear()%100,t,2)}function ln(e,t){return oA((e=o7(e)).getFullYear()%100,t,2)}function li(e,t){return oA(e.getFullYear()%1e4,t,4)}function la(e,t){var r=e.getDay();return oA((e=r>=4||0===r?oe(e):oe.ceil(e)).getFullYear()%1e4,t,4)}function lo(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+oA(t/60|0,"0",2)+oA(t%60,"0",2)}function ll(e,t){return oA(e.getUTCDate(),t,2)}function lc(e,t){return oA(e.getUTCHours(),t,2)}function lu(e,t){return oA(e.getUTCHours()%12||12,t,2)}function ls(e,t){return oA(1+a3.count(oh(e),e),t,3)}function lf(e,t){return oA(e.getUTCMilliseconds(),t,3)}function ld(e,t){return lf(e,t)+"000"}function lp(e,t){return oA(e.getUTCMonth()+1,t,2)}function lh(e,t){return oA(e.getUTCMinutes(),t,2)}function ly(e,t){return oA(e.getUTCSeconds(),t,2)}function lv(e){var t=e.getUTCDay();return 0===t?7:t}function lg(e,t){return oA(oi.count(oh(e)-1,e),t,2)}function lm(e){var t=e.getUTCDay();return t>=4||0===t?oc(e):oc.ceil(e)}function lb(e,t){return e=lm(e),oA(oc.count(oh(e),e)+(4===oh(e).getUTCDay()),t,2)}function lx(e){return e.getUTCDay()}function lw(e,t){return oA(oa.count(oh(e)-1,e),t,2)}function lO(e,t){return oA(e.getUTCFullYear()%100,t,2)}function lj(e,t){return oA((e=lm(e)).getUTCFullYear()%100,t,2)}function lP(e,t){return oA(e.getUTCFullYear()%1e4,t,4)}function lE(e,t){var r=e.getUTCDay();return oA((e=r>=4||0===r?oc(e):oc.ceil(e)).getUTCFullYear()%1e4,t,4)}function lS(){return"+0000"}function lA(){return"%"}function l_(e){return+e}function lk(e){return Math.floor(e/1e3)}function lM(e){return new Date(e)}function lT(e){return e instanceof Date?+e:+new Date(+e)}function lC(e,t,r,n,i,a,o,l,c,u){var s=ad(),f=s.invert,d=s.domain,p=u(".%L"),h=u(":%S"),y=u("%I:%M"),v=u("%I %p"),g=u("%a %d"),m=u("%b %d"),b=u("%B"),x=u("%Y");function w(e){return(c(e)<e?p:l(e)<e?h:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?d(Array.from(e,lT)):d().map(lM)},s.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?w:u(t)},s.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(aE(r,e)):s},s.copy=function(){return as(s,lC(e,t,r,n,i,a,o,l,c,u))},s}function lN(){return it.apply(lC(om,ob,op,of,a4,a2,a0,aQ,aZ,l1).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function lD(){return it.apply(lC(ov,og,oh,od,oi,a3,a1,aJ,aZ,l2).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function lI(){var e,t,r,n,i,a=0,o=1,l=ao,c=!1;function u(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),u):[l(0),l(1)]}}return u.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),u):[a,o]},u.clamp=function(e){return arguments.length?(c=!!e,u):c},u.interpolator=function(e){return arguments.length?(l=e,u):l},u.range=s(ar),u.rangeRound=s(an),u.unknown=function(e){return arguments.length?(i=e,u):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),u}}function lR(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function l$(){var e=aU(lI());return e.copy=function(){return lR(e,l$()).exponent(e.exponent())},ir.apply(e,arguments)}function lL(){return l$.apply(null,arguments).exponent(.5)}function lU(){var e,t,r,n,i,a,o,l=0,c=.5,u=1,s=1,f=ao,d=!1;function p(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=ar);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),p):[f(0),f(.5),f(1)]}}return p.domain=function(o){return arguments.length?([l,c,u]=o,e=a(l*=1),t=a(c*=1),r=a(u*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,p):[l,c,u]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(ar),p.rangeRound=h(an),p.unknown=function(e){return arguments.length?(o=e,p):o},function(o){return a=o,e=o(l),t=o(c),r=o(u),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,p}}function lz(){var e=aU(lU());return e.copy=function(){return lR(e,lz()).exponent(e.exponent())},ir.apply(e,arguments)}function lF(){return lz.apply(null,arguments).exponent(.5)}l1=(l0=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,c=e.shortMonths,u=ok(i),s=oM(i),f=ok(a),d=oM(a),p=ok(o),h=oM(o),y=ok(l),v=oM(l),g=ok(c),m=oM(c),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:oZ,e:oZ,f:o2,g:ln,G:la,H:oQ,I:oJ,j:o0,L:o1,m:o3,M:o5,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:l_,s:lk,S:o6,u:o4,U:o8,V:o9,w:le,W:lt,x:null,X:null,y:lr,Y:li,Z:lo,"%":lA},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:ll,e:ll,f:ld,g:lj,G:lE,H:lc,I:lu,j:ls,L:lf,m:lp,M:lh,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:l_,s:lk,S:ly,u:lv,U:lg,V:lb,w:lx,W:lw,x:null,X:null,y:lO,Y:lP,Z:lS,"%":lA},w={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return P(e,t,r,n)},d:oF,e:oF,f:oV,g:o$,G:oR,H:oK,I:oK,j:oB,L:oH,m:oz,M:oq,p:function(e,t,r){var n=u.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:oU,Q:oX,s:oG,S:oW,u:oC,U:oN,V:oD,w:oT,W:oI,x:function(e,t,n){return P(e,r,t,n)},X:function(e,t,r){return P(e,n,t,r)},y:o$,Y:oR,Z:oL,"%":oY};function O(e,t){return function(r){var n,i,a,o=[],l=-1,c=0,u=e.length;for(r instanceof Date||(r=new Date(+r));++l<u;)37===e.charCodeAt(l)&&(o.push(e.slice(c,l)),null!=(i=oj[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),c=l+1);return o.push(e.slice(c,l)),o.join("")}}function j(e,t){return function(r){var n,i,a=oO(1900,void 0,1);if(P(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=ow(oO(a.y,0,1))).getUTCDay())>4||0===i?oa.ceil(n):oa(n),n=a3.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=ox(oO(a.y,0,1))).getDay())>4||0===i?a8.ceil(n):a8(n),n=a2.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?ow(oO(a.y,0,1)).getUTCDay():ox(oO(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,ow(a)):ox(a)}}function P(e,t,r,n){for(var i,a,o=0,l=t.length,c=r.length;o<l;){if(n>=c)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in oj?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l0.parse,l2=l0.utcFormat,l0.utcParse;var lB=e=>e.chartData,lK=r0([lB],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),lq=(e,t,r,n)=>n?lK(e):lB(e);function lW(e){return Number.isFinite(e)}function lH(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}function lV(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if(lW(t)&&lW(r))return!0}return!1}function lY(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var lX,lG,lZ,lQ,lJ,l0,l1,l2,l3,l5,l6=!0,l4="[DecimalError] ",l8=l4+"Invalid argument: ",l7=l4+"Exponent out of range: ",l9=Math.floor,ce=Math.pow,ct=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,cr=l9(1286742750677284.5),cn={};function ci(e,t){var r,n,i,a,o,l,c,u,s=e.constructor,f=s.precision;if(!e.s||!t.s)return t.s||(t=new s(e)),l6?ch(t,f):t;if(c=e.d,u=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,l=u.length):(n=u,i=o,l=c.length),a>(l=(o=Math.ceil(f/7))>l?o+1:l+1)&&(a=l,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((l=c.length)-(a=u.length)<0&&(a=l,n=u,u=c,c=n),r=0;a;)r=(c[--a]=c[a]+u[a]+r)/1e7|0,c[a]%=1e7;for(r&&(c.unshift(r),++i),l=c.length;0==c[--l];)c.pop();return t.d=c,t.e=i,l6?ch(t,f):t}function ca(e,t,r){if(e!==~~e||e<t||e>r)throw Error(l8+e)}function co(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=cf(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=cf(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}cn.absoluteValue=cn.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},cn.comparedTo=cn.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},cn.decimalPlaces=cn.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},cn.dividedBy=cn.div=function(e){return cl(this,new this.constructor(e))},cn.dividedToIntegerBy=cn.idiv=function(e){var t=this.constructor;return ch(cl(this,new t(e),0,1),t.precision)},cn.equals=cn.eq=function(e){return!this.cmp(e)},cn.exponent=function(){return cu(this)},cn.greaterThan=cn.gt=function(e){return this.cmp(e)>0},cn.greaterThanOrEqualTo=cn.gte=function(e){return this.cmp(e)>=0},cn.isInteger=cn.isint=function(){return this.e>this.d.length-2},cn.isNegative=cn.isneg=function(){return this.s<0},cn.isPositive=cn.ispos=function(){return this.s>0},cn.isZero=function(){return 0===this.s},cn.lessThan=cn.lt=function(e){return 0>this.cmp(e)},cn.lessThanOrEqualTo=cn.lte=function(e){return 1>this.cmp(e)},cn.logarithm=cn.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(l5))throw Error(l4+"NaN");if(this.s<1)throw Error(l4+(this.s?"NaN":"-Infinity"));return this.eq(l5)?new r(0):(l6=!1,t=cl(cd(this,i),cd(e,i),i),l6=!0,ch(t,n))},cn.minus=cn.sub=function(e){return e=new this.constructor(e),this.s==e.s?cy(this,e):ci(this,(e.s=-e.s,e))},cn.modulo=cn.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(l4+"NaN");return this.s?(l6=!1,t=cl(this,e,0,1).times(e),l6=!0,this.minus(t)):ch(new r(this),n)},cn.naturalExponential=cn.exp=function(){return cc(this)},cn.naturalLogarithm=cn.ln=function(){return cd(this)},cn.negated=cn.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},cn.plus=cn.add=function(e){return e=new this.constructor(e),this.s==e.s?ci(this,e):cy(this,(e.s=-e.s,e))},cn.precision=cn.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(l8+e);if(t=cu(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},cn.squareRoot=cn.sqrt=function(){var e,t,r,n,i,a,o,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(l4+"NaN")}for(e=cu(this),l6=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=co(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=l9((e+1)/2)-(e<0||e%2),n=new l(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new l(i.toString()),i=o=(r=l.precision)+3;;)if(n=(a=n).plus(cl(this,a,o+2)).times(.5),co(a.d).slice(0,o)===(t=co(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(ch(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return l6=!0,ch(n,r)},cn.times=cn.mul=function(e){var t,r,n,i,a,o,l,c,u,s=this.constructor,f=this.d,d=(e=new s(e)).d;if(!this.s||!e.s)return new s(0);for(e.s*=this.s,r=this.e+e.e,(c=f.length)<(u=d.length)&&(a=f,f=d,d=a,o=c,c=u,u=o),a=[],n=o=c+u;n--;)a.push(0);for(n=u;--n>=0;){for(t=0,i=c+n;i>n;)l=a[i]+d[n]*f[i-n-1]+t,a[i--]=l%1e7|0,t=l/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,l6?ch(e,s.precision):e},cn.toDecimalPlaces=cn.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(ca(e,0,1e9),void 0===t?t=n.rounding:ca(t,0,8),ch(r,e+cu(r)+1,t))},cn.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=cv(n,!0):(ca(e,0,1e9),void 0===t?t=i.rounding:ca(t,0,8),r=cv(n=ch(new i(n),e+1,t),!0,e+1)),r},cn.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?cv(this):(ca(e,0,1e9),void 0===t?t=i.rounding:ca(t,0,8),r=cv((n=ch(new i(this),e+cu(this)+1,t)).abs(),!1,e+cu(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},cn.toInteger=cn.toint=function(){var e=this.constructor;return ch(new e(this),cu(this)+1,e.rounding)},cn.toNumber=function(){return+this},cn.toPower=cn.pow=function(e){var t,r,n,i,a,o,l=this,c=l.constructor,u=+(e=new c(e));if(!e.s)return new c(l5);if(!(l=new c(l)).s){if(e.s<1)throw Error(l4+"Infinity");return l}if(l.eq(l5))return l;if(n=c.precision,e.eq(l5))return ch(l,n);if(o=(t=e.e)>=(r=e.d.length-1),a=l.s,o){if((r=u<0?-u:u)<=0x1fffffffffffff){for(i=new c(l5),t=Math.ceil(n/7+4),l6=!1;r%2&&cg((i=i.times(l)).d,t),0!==(r=l9(r/2));)cg((l=l.times(l)).d,t);return l6=!0,e.s<0?new c(l5).div(i):ch(i,n)}}else if(a<0)throw Error(l4+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,l.s=1,l6=!1,i=e.times(cd(l,n+12)),l6=!0,(i=cc(i)).s=a,i},cn.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=cu(i),n=cv(i,r<=a.toExpNeg||r>=a.toExpPos)):(ca(e,1,1e9),void 0===t?t=a.rounding:ca(t,0,8),r=cu(i=ch(new a(i),e,t)),n=cv(i,e<=r||r<=a.toExpNeg,e)),n},cn.toSignificantDigits=cn.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(ca(e,1,1e9),void 0===t?t=r.rounding:ca(t,0,8)),ch(new r(this),e,t)},cn.toString=cn.valueOf=cn.val=cn.toJSON=cn[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=cu(this),t=this.constructor;return cv(this,e<=t.toExpNeg||e>=t.toExpPos)};var cl=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,c,u,s,f,d,p,h,y,v,g,m,b,x,w,O,j,P,E=n.constructor,S=n.s==i.s?1:-1,A=n.d,_=i.d;if(!n.s)return new E(n);if(!i.s)throw Error(l4+"Division by zero");for(u=0,c=n.e-i.e,j=_.length,w=A.length,h=(p=new E(S)).d=[];_[u]==(A[u]||0);)++u;if(_[u]>(A[u]||0)&&--c,(m=null==a?a=E.precision:o?a+(cu(n)-cu(i))+1:a)<0)return new E(0);if(m=m/7+2|0,u=0,1==j)for(s=0,_=_[0],m++;(u<w||s)&&m--;u++)b=1e7*s+(A[u]||0),h[u]=b/_|0,s=b%_|0;else{for((s=1e7/(_[0]+1)|0)>1&&(_=e(_,s),A=e(A,s),j=_.length,w=A.length),x=j,v=(y=A.slice(0,j)).length;v<j;)y[v++]=0;(P=_.slice()).unshift(0),O=_[0],_[1]>=1e7/2&&++O;do s=0,(l=t(_,y,j,v))<0?(g=y[0],j!=v&&(g=1e7*g+(y[1]||0)),(s=g/O|0)>1?(s>=1e7&&(s=1e7-1),d=(f=e(_,s)).length,v=y.length,1==(l=t(f,y,d,v))&&(s--,r(f,j<d?P:_,d))):(0==s&&(l=s=1),f=_.slice()),(d=f.length)<v&&f.unshift(0),r(y,f,v),-1==l&&(v=y.length,(l=t(_,y,j,v))<1&&(s++,r(y,j<v?P:_,v))),v=y.length):0===l&&(s++,y=[0]),h[u++]=s,l&&y[0]?y[v++]=A[x]||0:(y=[A[x]],v=1);while((x++<w||void 0!==y[0])&&m--)}return h[0]||h.shift(),p.e=c,ch(p,o?a+cu(p)+1:a)}}();function cc(e,t){var r,n,i,a,o,l=0,c=0,u=e.constructor,s=u.precision;if(cu(e)>16)throw Error(l7+cu(e));if(!e.s)return new u(l5);for(null==t?(l6=!1,o=s):o=t,a=new u(.03125);e.abs().gte(.1);)e=e.times(a),c+=5;for(o+=Math.log(ce(2,c))/Math.LN10*2+5|0,r=n=i=new u(l5),u.precision=o;;){if(n=ch(n.times(e),o),r=r.times(++l),co((a=i.plus(cl(n,r,o))).d).slice(0,o)===co(i.d).slice(0,o)){for(;c--;)i=ch(i.times(i),o);return u.precision=s,null==t?(l6=!0,ch(i,s)):i}i=a}}function cu(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function cs(e,t,r){if(t>e.LN10.sd())throw l6=!0,r&&(e.precision=r),Error(l4+"LN10 precision limit exceeded");return ch(new e(e.LN10),t)}function cf(e){for(var t="";e--;)t+="0";return t}function cd(e,t){var r,n,i,a,o,l,c,u,s,f=1,d=e,p=d.d,h=d.constructor,y=h.precision;if(d.s<1)throw Error(l4+(d.s?"NaN":"-Infinity"));if(d.eq(l5))return new h(0);if(null==t?(l6=!1,u=y):u=t,d.eq(10))return null==t&&(l6=!0),cs(h,u);if(h.precision=u+=10,n=(r=co(p)).charAt(0),!(15e14>Math.abs(a=cu(d))))return c=cs(h,u+2,y).times(a+""),d=cd(new h(n+"."+r.slice(1)),u-10).plus(c),h.precision=y,null==t?(l6=!0,ch(d,y)):d;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=co((d=d.times(e)).d)).charAt(0),f++;for(a=cu(d),n>1?(d=new h("0."+r),a++):d=new h(n+"."+r.slice(1)),l=o=d=cl(d.minus(l5),d.plus(l5),u),s=ch(d.times(d),u),i=3;;){if(o=ch(o.times(s),u),co((c=l.plus(cl(o,new h(i),u))).d).slice(0,u)===co(l.d).slice(0,u))return l=l.times(2),0!==a&&(l=l.plus(cs(h,u+2,y).times(a+""))),l=cl(l,new h(f),u),h.precision=y,null==t?(l6=!0,ch(l,y)):l;l=c,i+=2}}function cp(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=l9((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l6&&(e.e>cr||e.e<-cr))throw Error(l7+r)}else e.s=0,e.e=0,e.d=[0];return e}function ch(e,t,r){var n,i,a,o,l,c,u,s,f=e.d;for(o=1,a=f[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,u=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(a=f.length))return e;for(o=1,u=a=f[s];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(l=u/(a=ce(10,o-i-1))%10|0,c=t<0||void 0!==f[s+1]||u%a,c=r<4?(l||c)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||c||6==r&&(n>0?i>0?u/ce(10,o-i):0:f[s-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return c?(a=cu(e),f.length=1,t=t-a-1,f[0]=ce(10,(7-t%7)%7),e.e=l9(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=s,a=1,s--):(f.length=s+1,a=ce(10,7-n),f[s]=i>0?(u/ce(10,o-i)%ce(10,i)|0)*a:0),c)for(;;)if(0==s){1e7==(f[0]+=a)&&(f[0]=1,++e.e);break}else{if(f[s]+=a,1e7!=f[s])break;f[s--]=0,a=1}for(n=f.length;0===f[--n];)f.pop();if(l6&&(e.e>cr||e.e<-cr))throw Error(l7+cu(e));return e}function cy(e,t){var r,n,i,a,o,l,c,u,s,f,d=e.constructor,p=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),l6?ch(t,p):t;if(c=e.d,f=t.d,n=t.e,u=e.e,c=c.slice(),o=u-n){for((s=o<0)?(r=c,o=-o,l=f.length):(r=f,n=u,l=c.length),o>(i=Math.max(Math.ceil(p/7),l)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((s=(i=c.length)<(l=f.length))&&(l=i),i=0;i<l;i++)if(c[i]!=f[i]){s=c[i]<f[i];break}o=0}for(s&&(r=c,c=f,f=r,t.s=-t.s),l=c.length,i=f.length-l;i>0;--i)c[l++]=0;for(i=f.length;i>o;){if(c[--i]<f[i]){for(a=i;a&&0===c[--a];)c[a]=1e7-1;--c[a],c[i]+=1e7}c[i]-=f[i]}for(;0===c[--l];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,l6?ch(t,p):t):new d(0)}function cv(e,t,r){var n,i=cu(e),a=co(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+cf(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+cf(-i-1)+a,r&&(n=r-o)>0&&(a+=cf(n))):i>=o?(a+=cf(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+cf(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=cf(n))),e.s<0?"-"+a:a}function cg(e,t){if(e.length>t)return e.length=t,!0}function cm(e){if(!e||"object"!=typeof e)throw Error(l4+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(l9(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(l8+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(l8+r+": "+n);return this}var l3=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(l8+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return cp(this,e.toString())}if("string"!=typeof e)throw Error(l8+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,ct.test(e))cp(this,e);else throw Error(l8+e)}if(a.prototype=cn,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=cm,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});l5=new l3(1);let cb=l3;var cx=e=>e,cw={},cO=e=>e===cw,cj=e=>function t(){return 0==arguments.length||1==arguments.length&&cO(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},cP=(e,t)=>1===e?t:cj(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==cw).length;return a>=e?t(...n):cP(e-a,cj(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>cO(e)?r.shift():e),...r)}))}),cE=e=>cP(e.length,e),cS=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},cA=cE((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),c_=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return cx;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},ck=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),cM=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function cT(e){var t;return 0===e?1:Math.floor(new cb(e).abs().log(10).toNumber())+1}function cC(e,t,r){for(var n=new cb(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}cE((e,t,r)=>{var n=+e;return n+r*(t-n)}),cE((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),cE((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var cN=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},cD=(e,t,r)=>{if(e.lte(0))return new cb(0);var n=cT(e.toNumber()),i=new cb(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new cb(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new cb(t?l.toNumber():Math.ceil(l.toNumber()))},cI=(e,t,r)=>{var n=new cb(1),i=new cb(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new cb(10).pow(cT(e)-1),i=new cb(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new cb(Math.floor(e)))}else 0===e?i=new cb(Math.floor((t-1)/2)):r||(i=new cb(Math.floor(e)));var o=Math.floor((t-1)/2);return c_(cA(e=>i.add(new cb(e-o).mul(n)).toNumber()),cS)(0,t)},cR=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new cb(0),tickMin:new cb(0),tickMax:new cb(0)};var o=cD(new cb(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new cb(0):(i=new cb(e).add(t).div(2)).sub(new cb(i).mod(o))).sub(e).div(o).toNumber()),c=Math.ceil(new cb(t).sub(i).div(o).toNumber()),u=l+c+1;return u>r?cR(e,t,r,n,a+1):(u<r&&(c=t>0?c+(r-u):c,l=t>0?l:l+(r-u)),{step:o,tickMin:i.sub(new cb(l).mul(o)),tickMax:i.add(new cb(c).mul(o))})},c$=cM(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=cN([t,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...cS(0,n-1).map(()=>1/0)]:[...cS(0,n-1).map(()=>-1/0),l];return t>r?ck(c):c}if(o===l)return cI(o,n,i);var{step:u,tickMin:s,tickMax:f}=cR(o,l,a,i,0),d=cC(s,f.add(new cb(.1).mul(u)),u);return t>r?ck(d):d}),cL=cM(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=cN([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),c=cD(new cb(o).sub(a).div(l-1),i,0),u=[...cC(new cb(a),new cb(o).sub(new cb(.99).mul(c)),c),o];return r>n?ck(u):u}),cU=e=>e.rootProps.stackOffset,cz=e=>e.options.chartName,cF=e=>e.rootProps.syncId,cB=e=>e.rootProps.syncMethod,cK=e=>e.options.eventEmitter,cq={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},cW={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},cH=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t},cV={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:cq.angleAxisId,includeHidden:!1,name:void 0,reversed:cq.reversed,scale:cq.scale,tick:cq.tick,tickCount:void 0,ticks:void 0,type:cq.type,unit:void 0},cY={allowDataOverflow:cW.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:cW.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:cW.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:cW.scale,tick:cW.tick,tickCount:cW.tickCount,ticks:void 0,type:cW.type,unit:void 0},cX={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:cq.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:cq.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:cq.scale,tick:cq.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},cG={allowDataOverflow:cW.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:cW.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:cW.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:cW.scale,tick:cW.tick,tickCount:cW.tickCount,ticks:void 0,type:"category",unit:void 0},cZ=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?cX:cV,cQ=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?cG:cY,cJ=e=>e.polarOptions,c0=r0([nF,nB,nX],function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2}),c1=r0([cJ,c0],(e,t)=>{if(null!=e)return $(e.innerRadius,t,0)}),c2=r0([cJ,c0],(e,t)=>{if(null!=e)return $(e.outerRadius,t,.8*t)}),c3=r0([cJ],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});r0([cZ,c3],cH);var c5=r0([c0,c1,c2],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});r0([cQ,c5],cH);var c6=r0([n8,cJ,c1,c2,nF,nB],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:c,endAngle:u}=t;return{cx:$(o,i,i/2),cy:$(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:u,clockWise:!1}}}),c4=(e,t)=>t,c8=(e,t,r)=>r;function c7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c9(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c7(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ue=[0,"auto"],ut={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},ur=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?ut:r},un={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:ue,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},ui=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?un:r},ua={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},uo=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?ua:r},ul=(e,t,r)=>{switch(t){case"xAxis":return ur(e,r);case"yAxis":return ui(e,r);case"zAxis":return uo(e,r);case"angleAxis":return cZ(e,r);case"radiusAxis":return cQ(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},uc=(e,t,r)=>{switch(t){case"xAxis":return ur(e,r);case"yAxis":return ui(e,r);case"angleAxis":return cZ(e,r);case"radiusAxis":return cQ(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},uu=e=>e.graphicalItems.countOfBars>0;function us(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var uf=e=>e.graphicalItems.cartesianItems,ud=r0([c4,c8],us),up=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),uh=r0([uf,ul,ud],up),uy=e=>e.filter(e=>void 0===e.stackId),uv=r0([uh],uy),ug=e=>e.map(e=>e.data).filter(Boolean).flat(1),um=r0([uh],ug),ub=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},ux=r0([um,lq],ub),uw=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:nw(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:nw(e,t)}))):e.map(e=>({value:e})),uO=r0([ux,ul,uh],uw);function uj(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function uP(e){return e.filter(e=>D(e)||e instanceof Date).map(Number).filter(e=>!1===T(e))}var uE=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t;return[n,{stackedData:nk(e,i.map(e=>e.dataKey),r),graphicalItems:i}]})),uS=r0([ux,uh,cU],uE),uA=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=nN(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},u_=r0([uS,lB,c4],uA),uk=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(e=>uj(n,e)),l=nw(e,null!=(a=t.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||T(t)||!r.length?[]:uP(r.flatMap(r=>{var n,i,a=nw(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,lW(n)&&lW(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:nw(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),uM=r0(ux,ul,uv,c4,uk);function uT(e){var{value:t}=e;if(D(t)||t instanceof Date)return t}var uC=e=>{var t=uP(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},uN=(e,t,r)=>{var n=e.map(uT).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&L(n))?ie()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},uD=e=>{var t;if(null==e||!("domain"in e))return ue;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=uP(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:ue},uI=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},uR=e=>e.referenceElements.dots,u$=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),uL=r0([uR,c4,c8],u$),uU=e=>e.referenceElements.areas,uz=r0([uU,c4,c8],u$),uF=e=>e.referenceElements.lines,uB=r0([uF,c4,c8],u$),uK=(e,t)=>{var r=uP(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},uq=r0(uL,c4,uK),uW=(e,t)=>{var r=uP(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},uH=r0([uz,c4],uW),uV=(e,t)=>{var r=uP(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},uY=r0(uB,c4,uV),uX=r0(uq,uY,uH,(e,t,r)=>uI(e,r,t)),uG=r0([ul],uD),uZ=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if(lW(i))r=i;else if("function"==typeof i)return;if(lW(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(lV(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(lV(n))return lY(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if(N(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&nD.test(o)){var c=nD.exec(o);if(null==c||null==t)i=void 0;else{var u=+c[1];i=t[0]-u}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if(N(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&nI.test(l)){var s=nI.exec(l);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var d=[i,a];if(lV(d))return null==t?d:lY(d,t,r)}}}(t,uI(r,i,uC(n)),e.allowDataOverflow)},uQ=r0([ul,uG,u_,uM,uX],uZ),uJ=[0,1],u0=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:c}=e,u=nP(t,a);return u&&null==l?ie()(0,r.length):"category"===c?uN(n,e,u):"expand"===i?uJ:o}},u1=r0([ul,n8,ux,uO,cU,c4,uQ],u0),u2=(e,t,r,i,a)=>{if(null!=e){var{scale:o,type:l}=e;if("auto"===o)return"radial"===t&&"radiusAxis"===a?"band":"radial"===t&&"angleAxis"===a?"linear":"category"===l&&i&&(i.indexOf("LineChart")>=0||i.indexOf("AreaChart")>=0||i.indexOf("ComposedChart")>=0&&!r)?"point":"category"===l?"band":"linear";if("string"==typeof o){var c="scale".concat(B(o));return c in n?c:"point"}}},u3=r0([ul,n8,uu,cz,c4],u2);function u5(e,t,r,i){if(null!=r&&null!=i){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(i);var a=function(e){if(null!=e){if(e in n)return n[e]();var t="scale".concat(B(e));if(t in n)return n[t]()}}(t);if(null!=a){var o=a.domain(r).range(i);return nA(o),o}}}var u6=(e,t,r)=>{var n=uD(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&lV(e))return c$(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&lV(e))return cL(e,t.tickCount,t.allowDecimals)}},u4=r0([u1,uc,u3],u6),u8=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&lV(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,u7=r0([ul,u1,u4,c4],u8),u9=r0(uO,ul,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(uP(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),se=r0(u9,n8,e=>e.rootProps.barCategoryGap,nX,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!lW(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=$(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),st=r0(ur,(e,t)=>{var r=ur(e,t);return null==r||"string"!=typeof r.padding?0:se(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),sr=r0(ui,(e,t)=>{var r=ui(e,t);return null==r||"string"!=typeof r.padding?0:se(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),sn=r0([nX,st,n1,n0,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),si=r0([nX,n8,sr,n1,n0,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),sa=(e,t,r,n)=>{var i;switch(t){case"xAxis":return sn(e,r,n);case"yAxis":return si(e,r,n);case"zAxis":return null==(i=uo(e,r))?void 0:i.range;case"angleAxis":return c3(e);case"radiusAxis":return c5(e,r);default:return}},so=r0([ul,sa],cH),sl=r0([ul,u3,u7,so],u5);function sc(e,t){return e.id<t.id?-1:+(e.id>t.id)}r0(uh,c4,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>uj(t,e)));var su=(e,t)=>t,ss=(e,t,r)=>r,sf=r0(nW,su,ss,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(sc)),sd=r0(nH,su,ss,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(sc)),sp=(e,t)=>({width:e.width,height:t.height}),sh=(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}),sy=r0(nX,ur,sp),sv=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},sg=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},sm=r0(nB,nX,sf,su,ss,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=sp(t,r);null==a&&(a=sv(t,n,e));var c="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(c)*l.height,a+=(c?-1:1)*l.height}),o}),sb=r0(nF,nX,sd,su,ss,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=sh(t,r);null==a&&(a=sg(t,n,e));var c="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(c)*l.width,a+=(c?-1:1)*l.width}),o}),sx=(e,t)=>{var r=nX(e),n=ur(e,t);if(null!=n){var i=sm(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},sw=(e,t)=>{var r=nX(e),n=ui(e,t);if(null!=n){var i=sb(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},sO=r0(nX,ui,(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height})),sj=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=nP(e,n),c=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&L(c))return c}},sP=r0([n8,uO,ul,c4],sj),sE=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if(nP(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},sS=r0([n8,uO,uc,c4],sE),sA=r0([n8,(e,t,r)=>{switch(t){case"xAxis":return ur(e,r);case"yAxis":return ui(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},u3,sl,sP,sS,sa,u4,c4],(e,t,r,n,i,a,o,l,c)=>{if(null==t)return null;var u=nP(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:u,niceTicks:l,range:o,realScaleType:r,scale:n}}),s_=r0([n8,uc,u3,sl,u4,sa,sP,sS,c4],(e,t,r,n,i,a,o,l,c)=>{if(null!=t&&null!=n){var u=nP(e,c),{type:s,ticks:f,tickCount:d}=t,p="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,h="category"===s&&n.bandwidth?n.bandwidth()/p:0;h="angleAxis"===c&&null!=a&&a.length>=2?2*M(a[0]-a[1])*h:h;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+h,value:e,offset:h})).filter(e=>!T(e.coordinate)):u&&l?l.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+h,value:e,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:o?o[e]:e,index:t,offset:h}))}}),sk=r0([n8,uc,sl,sa,sP,sS,c4],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=nP(e,o),{tickCount:c}=t,u=0;return(u="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*M(n[0]-n[1])*u:u,l&&a)?a.map((e,t)=>({coordinate:r(e)+u,value:e,index:t,offset:u})):r.ticks?r.ticks(c).map(e=>({coordinate:r(e)+u,value:e,offset:u})):r.domain().map((e,t)=>({coordinate:r(e)+u,value:i?i[e]:e,index:t,offset:u}))}}),sM=r0(ul,sl,(e,t)=>{if(null!=e&&null!=t)return c9(c9({},e),{},{scale:t})}),sT=r0([ul,u3,u1,so],u5);r0((e,t,r)=>uo(e,r),sT,(e,t)=>{if(null!=e&&null!=t)return c9(c9({},e),{},{scale:t})});var sC=r0([n8,nW,nH],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),sN=e=>e.options.defaultTooltipEventType,sD=e=>e.options.validateTooltipEventTypes;function sI(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function sR(e,t){return sI(t,sN(e),sD(e))}var s$=(e,t)=>{var r,n=Number(t);if(!T(n)&&null!=t)return n>=0?null==e||null==(r=e[n])?void 0:r.value:void 0};function sL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sL(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var sz=(e,t,r,n)=>{if(null==t)return rf;var i=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==i)return rf;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var a=!0===e.settings.active;if(null!=i.index){if(a)return sU(sU({},i),{},{active:!0})}else if(null!=n)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return sU(sU({},rf),{},{coordinate:i.coordinate})},sF=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var n=Number(r);if(!lW(n))return r;var i=Infinity;return t.length>0&&(i=t.length-1),String(Math.max(0,Math.min(n,i)))},sB=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var c=o[0],u=null==c?void 0:l(c.positions,a);if(null!=u)return u;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:s.coordinate}}},sK=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})},sq=e=>e.options.tooltipPayloadSearcher,sW=e=>e.tooltip,sH=e=>{var t=n8(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},sV=e=>e.tooltip.settings.axisId,sY=e=>{var t=sH(e),r=sV(e);return uc(e,t,r)},sX=r0([sY,n8,uu,cz,sH],u2),sG=r0([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),sZ=r0([sH,sV],us),sQ=r0([sG,sY,sZ],up),sJ=r0([sQ],ug),s0=r0([sJ,lB],ub),s1=r0([s0,sY,sQ],uw),s2=r0([sY],uD),s3=r0([s0,sQ,cU],uE),s5=r0([s3,lB,sH],uA),s6=r0([sQ],uy),s4=r0([s0,sY,s6,sH],uk),s8=r0([uR,sH,sV],u$),s7=r0([s8,sH],uK),s9=r0([uU,sH,sV],u$),fe=r0([s9,sH],uW),ft=r0([uF,sH,sV],u$),fr=r0([ft,sH],uV),fn=r0([s7,fr,fe],uI),fi=r0([sY,s2,s5,s4,fn],uZ),fa=r0([sY,n8,s0,s1,cU,sH,fi],u0),fo=r0([fa,sY,sX],u6),fl=r0([sY,fa,fo,sH],u8),fc=e=>{var t=sH(e),r=sV(e);return sa(e,t,r,!1)},fu=r0([sY,fc],cH),fs=r0([sY,sX,fl,fu],u5),ff=r0([n8,s1,sY,sH],sj),fd=r0([n8,s1,sY,sH],sE),fp=r0([n8,sY,sX,fs,fc,ff,fd,sH],(e,t,r,n,i,a,o,l)=>{if(t){var{type:c}=t,u=nP(e,l);if(n){var s="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,f="category"===c&&n.bandwidth?n.bandwidth()/s:0;return(f="angleAxis"===l&&null!=i&&(null==i?void 0:i.length)>=2?2*M(i[0]-i[1])*f:f,u&&o)?o.map((e,t)=>({coordinate:n(e)+f,value:e,index:t,offset:f})):n.domain().map((e,t)=>({coordinate:n(e)+f,value:a?a[e]:e,index:t,offset:f}))}}}),fh=r0([sN,sD,e=>e.tooltip.settings],(e,t,r)=>sI(r.shared,e,t)),fy=e=>e.tooltip.settings.trigger,fv=e=>e.tooltip.settings.defaultIndex,fg=r0([sW,fh,fy,fv],sz),fm=r0([fg,s0],sF),fb=r0([fp,fm],s$),fx=r0([fg],e=>{if(e)return e.dataKey}),fw=r0([sW,fh,fy,fv],sK),fO=r0([nF,nB,n8,nX,fp,fv,fw,sq],sB),fj=r0([fg,fO],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),fP=r0([fg],e=>e.active);function fE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fE(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fE(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var fA=()=>r9(cz),f_=(e,t)=>t,fk=(e,t,r)=>r,fM=(e,t,r,n)=>n,fT=r0(fp,e=>nt()(e,e=>e.coordinate)),fC=r0([sW,f_,fk,fM],sz),fN=r0([fC,s0],sF),fD=(e,t,r)=>{if(null!=t){var n=sW(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},fI=r0([sW,f_,fk,fM],sK),fR=r0([nF,nB,n8,nX,fp,fM,fI,sq],sB),f$=r0([fC,fR],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),fL=r0(fp,fN,s$),fU=r0([fI,fN,lB,sY,fL,sq,f_],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:l,computedData:c,dataStartIndex:u,dataEndIndex:s}=r;return e.reduce((e,r)=>{var f,d,p,h,y,{dataDefinedOnItem:v,settings:g}=r,m=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((f=v,d=l,null!=f?f:d),u,s),b=null!=(p=null==g?void 0:g.dataKey)?p:null==n?void 0:n.dataKey,x=null==g?void 0:g.nameKey;return Array.isArray(h=null!=n&&n.dataKey&&!(null!=n&&n.allowDuplicatedCategory)&&Array.isArray(m)&&"axis"===o?z(m,n.dataKey,i):a(m,t,c,x))?h.forEach(t=>{var r=fS(fS({},g),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push(n$({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:nw(t.payload,t.dataKey),name:t.name}))}):e.push(n$({tooltipEntrySettings:g,dataKey:b,payload:h,value:nw(h,b),name:null!=(y=nw(h,x))?y:null==g?void 0:g.name})),e},[])}}),fz=r0([fC],e=>({isActive:e.active,activeIndex:e.index})),fF=r0([(e,t)=>t,n8,c6,sH,fu,fp,fT,nX],(e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var c=function(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?nm({x:e,y:t},n):null}(e.chartX,e.chartY,t,r,l);if(c){var u=nO(nz(c,t),o,a,n,i),s=nU(t,a,u,c);return{activeIndex:String(u),activeCoordinate:s}}}}),fB=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},fK=eH("mouseClick"),fq=tF();fq.startListening({actionCreator:fK,effect:(e,t)=>{var r=e.payload,n=fF(t.getState(),fB(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch(rw({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var fW=eH("mouseMove"),fH=tF();function fV(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}function fY(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fY(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fY(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}fH.startListening({actionCreator:fW,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=sR(n,n.tooltip.settings.shared),a=fF(n,fB(r));"axis"===i&&((null==a?void 0:a.activeIndex)!=null?t.dispatch(rx({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch(rm()))}});var fG=tn({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=fX(fX({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:fZ,removeXAxis:fQ,addYAxis:fJ,removeYAxis:f0,addZAxis:f1,removeZAxis:f2,updateYAxisWidth:f3}=fG.actions,f5=fG.reducer,f6=tn({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},removeCartesianGraphicalItem(e,t){var r=eF(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=eF(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addBar:f4,removeBar:f8,addCartesianGraphicalItem:f7,removeCartesianGraphicalItem:f9,addPolarGraphicalItem:de,removePolarGraphicalItem:dt}=f6.actions,dr=f6.reducer,dn=tn({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=eF(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=eF(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=eF(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:di,removeDot:da,addArea:dl,removeArea:dc,addLine:du,removeLine:ds}=dn.actions,df=dn.reducer,dd={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},dp=tn({name:"brush",initialState:dd,reducers:{setBrushSettings:(e,t)=>null==t.payload?dd:t.payload}}),{setBrushSettings:dh}=dp.actions,dy=dp.reducer,dv=tn({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=eF(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:dg,setLegendSettings:dm,addLegendPayload:db,removeLegendPayload:dx}=dv.actions,dw=dv.reducer,dO={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},dj=tn({name:"rootProps",initialState:dO,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:dO.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),dP=dj.reducer,{updateOptions:dE}=dj.actions,dS=tn({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:dA,removeRadiusAxis:d_,addAngleAxis:dk,removeAngleAxis:dM}=dS.actions,dT=dS.reducer,dC=tn({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:dN}=dC.actions,dD=dC.reducer,dI=eH("keyDown"),dR=eH("focus"),d$=tF();d$.startListening({actionCreator:dI,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number(sF(n,s0(r))),o=fp(r);if("Enter"===i){var l=fR(r,"axis","hover",String(n.index));t.dispatch(rj({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:l}));return}var c=a+("ArrowRight"===i?1:-1)*("left-to-right"===sC(r)?1:-1);if(null!=o&&!(c>=o.length)&&!(c<0)){var u=fR(r,"axis","hover",String(c));t.dispatch(rj({active:!0,activeIndex:c.toString(),activeDataKey:void 0,activeCoordinate:u}))}}}}}),d$.startListening({actionCreator:dR,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=fR(r,"axis","hover",String("0"));t.dispatch(rj({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var dL=eH("externalEvent"),dU=tF();dU.startListening({actionCreator:dL,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:fj(r),activeDataKey:fx(r),activeIndex:fm(r),activeLabel:fb(r),activeTooltipIndex:fm(r),isTooltipActive:fP(r)};e.payload.handler(n,e.payload.reactEvent)}}});var dz=r0([sW],e=>e.tooltipItemPayloads),dF=r0([dz,sq,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),dB=eH("touchMove"),dK=tF();dK.startListening({actionCreator:dB,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=sR(n,n.tooltip.settings.shared);if("axis"===i){var a=fF(n,fB({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==a?void 0:a.activeIndex)!=null&&t.dispatch(rx({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],c=document.elementFromPoint(l.clientX,l.clientY);if(!c||!c.getAttribute)return;var u=c.getAttribute("data-recharts-item-index"),s=null!=(o=c.getAttribute("data-recharts-item-data-key"))?o:void 0,f=dF(t.getState(),u,s);t.dispatch(rv({activeDataKey:s,activeIndex:u,activeCoordinate:f}))}}});var dq=Q({brush:dy,cartesianAxis:f5,chartData:rk,graphicalItems:dr,layout:rI,legend:dw,options:tG,polarAxis:dT,polarOptions:dD,referenceElements:df,rootProps:dP,tooltip:rP}),dW=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(e){let t,r,n=eZ(),{reducer:i,middleware:a,devTools:o=!0,duplicateMiddlewareCheck:l=!0,preloadedState:c,enhancers:u}=e||{};if("function"==typeof i)t=i;else if(Z(i))t=Q(i);else throw Error(tV(1));r="function"==typeof a?a(n):n();let s=J;o&&(s=eq({trace:!1,..."object"==typeof o&&o}));let f=e0(function(...e){return t=>(r,n)=>{let i=t(r,n),a=()=>{throw Error(V(15))},o={getState:i.getState,dispatch:(e,...t)=>a(e,...t)};return a=J(...e.map(e=>e(o)))(i.dispatch),{...i,dispatch:a}}}(...r));return function e(t,r,n){if("function"!=typeof t)throw Error(V(2));if("function"==typeof r&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw Error(V(0));if("function"==typeof r&&void 0===n&&(n=r,r=void 0),void 0!==n){if("function"!=typeof n)throw Error(V(1));return n(e)(t,r)}let i=t,a=r,o=new Map,l=o,c=0,u=!1;function s(){l===o&&(l=new Map,o.forEach((e,t)=>{l.set(t,e)}))}function f(){if(u)throw Error(V(3));return a}function d(e){if("function"!=typeof e)throw Error(V(4));if(u)throw Error(V(5));let t=!0;s();let r=c++;return l.set(r,e),function(){if(t){if(u)throw Error(V(6));t=!1,s(),l.delete(r),o=null}}}function p(e){if(!Z(e))throw Error(V(7));if(void 0===e.type)throw Error(V(8));if("string"!=typeof e.type)throw Error(V(17));if(u)throw Error(V(9));try{u=!0,a=i(a,e)}finally{u=!1}return(o=l).forEach(e=>{e()}),e}return p({type:G.INIT}),{dispatch:p,subscribe:d,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw Error(V(10));i=e,p({type:G.REPLACE})},[Y]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(V(11));function t(){e.next&&e.next(f())}return t(),{unsubscribe:d(t)}},[Y](){return this}}}}}(t,c,s(..."function"==typeof u?u(f):f()))}({reducer:dq,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([fq.middleware,fH.middleware,d$.middleware,dU.middleware,dK.middleware]),devTools:{serialize:{replacer:fV},name:"recharts-".concat(t)}})};function dH(e){var{preloadedState:t,children:r,reduxStoreName:n}=e,i=nJ(),o=(0,a.useRef)(null);return i?r:(null==o.current&&(o.current=dW(t,n)),a.createElement(rs,{context:r3,store:o.current},r))}var dV=e=>{var{chartData:t}=e,r=r6(),n=nJ();return(0,a.useEffect)(()=>n?()=>{}:(r(rS(t)),()=>{r(rS(void 0))}),[t,r,n]),null};function dY(e){var{layout:t,width:r,height:n,margin:i}=e;return r6(),nJ(),null}function dX(e){return r6(),null}var dG=r(9632),dZ=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],dQ=["points","pathLength"],dJ={svg:["viewBox","children"],polygon:dQ,polyline:dQ},d0=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],d1=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,a.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var n={};return Object.keys(r).forEach(e=>{d0.includes(e)&&(n[e]=t||(t=>r[e](r,t)))}),n},d2=(e,t,r)=>n=>(e(t,r,n),null),d3=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];d0.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=d2(a,t,r))}),n},d5=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",d6=null,d4=null,d8=e=>{if(e===d6&&Array.isArray(d4))return d4;var t=[];return a.Children.forEach(e,e=>{F(e)||((0,dG.isFragment)(e)?t=t.concat(d8(e.props.children)):t.push(e))}),d4=t,d6=e,t};function d7(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>d5(e)):[d5(t)],d8(e).forEach(e=>{var t=k()(e,"type.displayName")||k()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var d9=e=>!e||"object"!=typeof e||!("clipDot"in e)||!!e.clipDot,pe=(e,t,r,n)=>{var i,a=null!=(i=n&&(null==dJ?void 0:dJ[n]))?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||dZ.includes(t))||r&&d0.includes(t)},pt=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;pe(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i},pr=()=>r9(e=>e.rootProps.accessibilityLayer),pn=["children","width","height","viewBox","className","style","title","desc"];function pi(){return(pi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var pa=(0,a.forwardRef)((e,t)=>{var{children:r,width:n,height:i,viewBox:o,className:l,style:c,title:u,desc:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,pn),d=o||{width:n,height:i,x:0,y:0},p=(0,y.$)("recharts-surface",l);return a.createElement("svg",pi({},pt(f,!0,"svg"),{className:p,width:n,height:i,style:c,viewBox:"".concat(d.x," ").concat(d.y," ").concat(d.width," ").concat(d.height),ref:t}),a.createElement("title",null,u),a.createElement("desc",null,s),r)}),po=["children"];function pl(){return(pl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var pc={width:"100%",height:"100%"},pu=(0,a.forwardRef)((e,t)=>{var r,n,i=n6(),o=n4(),l=pr();if(!lH(i)||!lH(o))return null;var{children:c,otherAttributes:u,title:s,desc:f}=e;return r="number"==typeof u.tabIndex?u.tabIndex:l?0:void 0,n="string"==typeof u.role?u.role:l?"application":void 0,a.createElement(pa,pl({},u,{title:s,desc:f,role:n,tabIndex:r,width:i,height:o,style:pc,ref:t}),c)}),ps=e=>{var{children:t}=e,r=r9(n1);if(!r)return null;var{width:n,height:i,y:o,x:l}=r;return a.createElement(pa,{width:n,height:i,x:l,y:o},t)},pf=(0,a.forwardRef)((e,t)=>{var{children:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,po);return nJ()?a.createElement(ps,null,r):a.createElement(pu,pl({ref:t},n),r)});function pd(e){return e.tooltip.syncInteraction}new(r(1117));var pp=(0,a.createContext)(null),ph=()=>(0,a.useContext)(pp),py=(0,a.createContext)(null);function pv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var pg=(0,a.forwardRef)((e,t)=>{var{children:r,className:n,height:i,onClick:o,onContextMenu:l,onDoubleClick:c,onMouseDown:u,onMouseEnter:s,onMouseLeave:f,onMouseMove:d,onMouseUp:p,onTouchEnd:h,onTouchMove:v,onTouchStart:g,style:m,width:b}=e,x=r6(),[w,O]=(0,a.useState)(null),[j,P]=(0,a.useState)(null);r6(),r9(cF),r9(cK),r6(),r9(cB),r9(fp),n7(),n2(),r9(e=>e.rootProps.className),r9(cF),r9(cK),r6();var E=function(){r6();var[e,t]=(0,a.useState)(null);return r9(nK),t}(),S=(0,a.useCallback)(e=>{E(e),"function"==typeof t&&t(e),O(e),P(e)},[E,t,O,P]),A=(0,a.useCallback)(e=>{x(fK(e)),x(dL({handler:o,reactEvent:e}))},[x,o]),_=(0,a.useCallback)(e=>{x(fW(e)),x(dL({handler:s,reactEvent:e}))},[x,s]),k=(0,a.useCallback)(e=>{x(rm()),x(dL({handler:f,reactEvent:e}))},[x,f]),M=(0,a.useCallback)(e=>{x(fW(e)),x(dL({handler:d,reactEvent:e}))},[x,d]),T=(0,a.useCallback)(()=>{x(dR())},[x]),C=(0,a.useCallback)(e=>{x(dI(e.key))},[x]),N=(0,a.useCallback)(e=>{x(dL({handler:l,reactEvent:e}))},[x,l]),D=(0,a.useCallback)(e=>{x(dL({handler:c,reactEvent:e}))},[x,c]),I=(0,a.useCallback)(e=>{x(dL({handler:u,reactEvent:e}))},[x,u]),R=(0,a.useCallback)(e=>{x(dL({handler:p,reactEvent:e}))},[x,p]),$=(0,a.useCallback)(e=>{x(dL({handler:g,reactEvent:e}))},[x,g]),L=(0,a.useCallback)(e=>{x(dB(e)),x(dL({handler:v,reactEvent:e}))},[x,v]),U=(0,a.useCallback)(e=>{x(dL({handler:h,reactEvent:e}))},[x,h]);return a.createElement(pp.Provider,{value:w},a.createElement(py.Provider,{value:j},a.createElement("div",{className:(0,y.$)("recharts-wrapper",n),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pv(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pv(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:b,height:i},m),role:"application",onClick:A,onContextMenu:N,onDoubleClick:D,onFocus:T,onKeyDown:C,onMouseDown:I,onMouseEnter:_,onMouseLeave:k,onMouseMove:M,onMouseUp:R,onTouchEnd:U,onTouchMove:L,onTouchStart:$,ref:S},r)))}),pm=(0,a.createContext)(void 0),pb=e=>{var{children:t}=e,[r]=(0,a.useState)("".concat(R("recharts"),"-clip")),n=n5();if(null==n)return null;var{left:i,top:o,height:l,width:c}=n;return a.createElement(pm.Provider,{value:r},a.createElement("defs",null,a.createElement("clipPath",{id:r},a.createElement("rect",{x:i,y:o,height:l,width:c}))),t)},px=["children","className","width","height","style","compact","title","desc"],pw=(0,a.forwardRef)((e,t)=>{var{children:r,className:n,width:i,height:o,style:l,compact:c,title:u,desc:s}=e,f=pt(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,px),!1);return c?a.createElement(pf,{otherAttributes:f,title:u,desc:s},r):a.createElement(pg,{className:n,style:l,width:i,height:o,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},a.createElement(pf,{otherAttributes:f,title:u,desc:s,ref:t},a.createElement(pb,null,r)))});function pO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pj(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pO(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}var pP=["width","height"];function pE(){return(pE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var pS={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},pA=(0,a.forwardRef)(function(e,t){var r,n=pj(e.categoricalChartProps,pS),{width:i,height:o}=n,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,pP);if(!lH(i)||!lH(o))return null;var{chartName:c,defaultTooltipEventType:u,validateTooltipEventTypes:s,tooltipPayloadSearcher:f,categoricalChartProps:d}=e;return a.createElement(dH,{preloadedState:{options:{chartName:c,defaultTooltipEventType:u,validateTooltipEventTypes:s,tooltipPayloadSearcher:f,eventEmitter:void 0}},reduxStoreName:null!=(r=d.id)?r:c},a.createElement(dV,{chartData:d.data}),a.createElement(dY,{width:i,height:o,layout:n.layout,margin:n.margin}),a.createElement(dX,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),a.createElement(pw,pE({},l,{width:i,height:o,ref:t})))}),p_=["axis"],pk=(0,a.forwardRef)((e,t)=>a.createElement(pA,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:p_,tooltipPayloadSearcher:tY,categoricalChartProps:e,ref:t})),pM={isSsr:!0};function pT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pT(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var pN={widthCache:{},cacheCount:0},pD={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},pI="recharts_measurement_span",pR=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||pM.isSsr)return{width:0,height:0};var n=(Object.keys(t=pC({},r)).forEach(e=>{t[e]||delete t[e]}),t),i=JSON.stringify({text:e,copyStyle:n});if(pN.widthCache[i])return pN.widthCache[i];try{var a=document.getElementById(pI);a||((a=document.createElement("span")).setAttribute("id",pI),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=pC(pC({},pD),n);Object.assign(a.style,o),a.textContent="".concat(e);var l=a.getBoundingClientRect(),c={width:l.width,height:l.height};return pN.widthCache[i]=c,++pN.cacheCount>2e3&&(pN.cacheCount=0,pN.widthCache={}),c}catch(e){return{width:0,height:0}}};function p$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pL(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class pU{static create(e){return new pU(e)}constructor(e){this.scale=e}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}pL(pU,"EPS",1e-4);var pz=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function pF(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function pB(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function pK(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pq(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pK(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pK(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pW(e,t,r){var n,{tick:i,ticks:a,viewBox:o,minTickGap:l,orientation:c,interval:u,tickFormatter:s,unit:f,angle:d}=e;if(!a||!a.length||!i)return[];if(N(u)||pM.isSsr)return null!=(n=pF(a,(N(u)?u:0)+1))?n:[];var p=[],h="top"===c||"bottom"===c?"width":"height",y=f&&"width"===h?pR(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=(e,n)=>{var i,a="function"==typeof s?s(e.value,n):e.value;return"width"===h?(i=pR(a,{fontSize:t,letterSpacing:r}),pz({width:i.width+y.width,height:i.height+y.height},d)):pR(a,{fontSize:t,letterSpacing:r})[h]},g=a.length>=2?M(a[1].coordinate-a[0].coordinate):1,m=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(o,g,h);return"equidistantPreserveStart"===u?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:c}=t,u=0,s=1,f=l;s<=o.length;)if(a=function(){var t,a=null==n?void 0:n[u];if(void 0===a)return{v:pF(n,s)};var o=u,d=()=>(void 0===t&&(t=r(a,o)),t),p=a.coordinate,h=0===u||pB(e,p,d,f,c);h||(u=0,f=l,s+=1),h&&(f=p+e*(d()/2+i),u+=s)}())return a.v;return[]}(g,m,v,a,l):("preserveStart"===u||"preserveStartEnd"===u?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:c,end:u}=t;if(a){var s=n[l-1],f=r(s,l-1),d=e*(s.coordinate+e*f/2-u);o[l-1]=s=pq(pq({},s),{},{tickCoord:d>0?s.coordinate-d*e:s.coordinate}),pB(e,s.tickCoord,()=>f,c,u)&&(u=s.tickCoord-e*(f/2+i),o[l-1]=pq(pq({},s),{},{isShow:!0}))}for(var p=a?l-1:l,h=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var s=e*(a.coordinate-e*l()/2-c);o[t]=a=pq(pq({},a),{},{tickCoord:s<0?a.coordinate-s*e:a.coordinate})}else o[t]=a=pq(pq({},a),{},{tickCoord:a.coordinate});pB(e,a.tickCoord,l,c,u)&&(c=a.tickCoord+e*(l()/2+i),o[t]=pq(pq({},a),{},{isShow:!0}))},y=0;y<p;y++)h(y);return o}(g,m,v,a,l,"preserveStartEnd"===u):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:c}=t,u=function(t){var n,u=a[t],s=()=>(void 0===n&&(n=r(u,t)),n);if(t===o-1){var f=e*(u.coordinate+e*s()/2-c);a[t]=u=pq(pq({},u),{},{tickCoord:f>0?u.coordinate-f*e:u.coordinate})}else a[t]=u=pq(pq({},u),{},{tickCoord:u.coordinate});pB(e,u.tickCoord,s,l,c)&&(c=u.tickCoord-e*(s()/2+i),a[t]=pq(pq({},u),{},{isShow:!0}))},s=o-1;s>=0;s--)u(s);return a}(g,m,v,a,l)).filter(e=>e.isShow)}function pH(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var pV=["children","className"];function pY(){return(pY=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var pX=a.forwardRef((e,t)=>{var{children:r,className:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,pV),o=(0,y.$)("recharts-layer",n);return a.createElement("g",pY({className:o},pt(i,!0),{ref:t}),r)}),pG=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,pZ=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,pQ=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,pJ=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,p0={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},p1=Object.keys(p0);class p2{static parse(e){var t,[,r,n]=null!=(t=pJ.exec(e))?t:[];return new p2(parseFloat(r),null!=n?n:"")}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,T(e)&&(this.unit=""),""===t||pQ.test(t)||(this.num=NaN,this.unit=""),p1.includes(t)&&(this.num=e*p0[t],this.unit="px")}add(e){return this.unit!==e.unit?new p2(NaN,""):new p2(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new p2(NaN,""):new p2(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new p2(NaN,""):new p2(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new p2(NaN,""):new p2(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return T(this.num)}}function p3(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=pG.exec(t))?r:[],o=p2.parse(null!=n?n:""),l=p2.parse(null!=a?a:""),c="*"===i?o.multiply(l):o.divide(l);if(c.isNaN())return"NaN";t=t.replace(pG,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var u,[,s,f,d]=null!=(u=pZ.exec(t))?u:[],p=p2.parse(null!=s?s:""),h=p2.parse(null!=d?d:""),y="+"===f?p.add(h):p.subtract(h);if(y.isNaN())return"NaN";t=t.replace(pZ,y.toString())}return t}var p5=/\(([^()]*)\)/;function p6(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=p5.exec(r));){var[,n]=t;r=r.replace(p5,p3(n))}return r}(t),t=p3(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var p4=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],p8=["dx","dy","angle","className","breakAll"];function p7(){return(p7=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function p9(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var he=/[ \f\n\r\t\v\u2028\u2029]+/,ht=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];F(t)||(i=r?t.toString().split(""):t.toString().split(he));var a=i.map(e=>({word:e,width:pR(e,n).width})),o=r?0:pR("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:o}}catch(e){return null}},hr=(e,t,r,n,i)=>{var a,{maxLines:o,children:l,style:c,breakAll:u}=e,s=N(o),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},d=f(t),p=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!s||i||!(d.length>o||p(d).width>Number(n)))return d;for(var h=e=>{var t=f(ht({breakAll:u,style:c,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>o||p(t).width>Number(n),t]},y=0,v=l.length-1,g=0;y<=v&&g<=l.length-1;){var m=Math.floor((y+v)/2),[b,x]=h(m-1),[w]=h(m);if(b||w||(y=m+1),b&&w&&(v=m-1),!b&&w){a=x;break}g++}return a||d},hn=e=>[{words:F(e)?[]:e.toString().split(he)}],hi=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!pM.isSsr){var l=ht({breakAll:a,children:n,style:i});if(!l)return hn(n);var{wordsWithComputedWidth:c,spaceWidth:u}=l;return hr({breakAll:a,children:n,maxLines:o,style:i},c,u,t,r)}return hn(n)},ha="#808080",ho=(0,a.forwardRef)((e,t)=>{var r,{x:n=0,y:i=0,lineHeight:o="1em",capHeight:l="0.71em",scaleToFit:c=!1,textAnchor:u="start",verticalAnchor:s="end",fill:f=ha}=e,d=p9(e,p4),p=(0,a.useMemo)(()=>hi({breakAll:d.breakAll,children:d.children,maxLines:d.maxLines,scaleToFit:c,style:d.style,width:d.width}),[d.breakAll,d.children,d.maxLines,c,d.style,d.width]),{dx:h,dy:v,angle:g,className:m,breakAll:b}=d,x=p9(d,p8);if(!D(n)||!D(i))return null;var w=n+(N(h)?h:0),O=i+(N(v)?v:0);switch(s){case"start":r=p6("calc(".concat(l,")"));break;case"middle":r=p6("calc(".concat((p.length-1)/2," * -").concat(o," + (").concat(l," / 2))"));break;default:r=p6("calc(".concat(p.length-1," * -").concat(o,")"))}var j=[];if(c){var P=p[0].width,{width:E}=d;j.push("scale(".concat(N(E)?E/P:1,")"))}return g&&j.push("rotate(".concat(g,", ").concat(w,", ").concat(O,")")),j.length&&(x.transform=j.join(" ")),a.createElement("text",p7({},pt(x,!0),{ref:t,x:w,y:O,className:(0,y.$)("recharts-text",m),textAnchor:u,fill:f.includes("url")?ha:f}),p.map((e,t)=>{var n=e.words.join(b?"":" ");return a.createElement("tspan",{x:w,dy:0===t?r:o,key:"".concat(n,"-").concat(t)},n)}))});ho.displayName="Text";var hl=["offset"],hc=["labelRef"];function hu(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function hs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hf(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hs(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hs(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hd(){return(hd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var hp=e=>{var{value:t,formatter:r}=e,n=F(e.children)?t:e.children;return"function"==typeof r?r(n):n},hh=e=>null!=e&&"function"==typeof e,hy=(e,t)=>M(t-e)*Math.min(Math.abs(t-e),360),hv=(e,t,r)=>{var n,i,{position:o,viewBox:l,offset:c,className:u}=e,{cx:s,cy:f,innerRadius:d,outerRadius:p,startAngle:h,endAngle:v,clockWise:g}=l,m=(d+p)/2,b=hy(h,v),x=b>=0?1:-1;"insideStart"===o?(n=h+x*c,i=g):"insideEnd"===o?(n=v-x*c,i=!g):"end"===o&&(n=v+x*c,i=g),i=b<=0?i:!i;var w=np(s,f,m,n),O=np(s,f,m,n+(i?1:-1)*359),j="M".concat(w.x,",").concat(w.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(+!i,",\n    ").concat(O.x,",").concat(O.y),P=F(e.id)?R("recharts-radial-line-"):e.id;return a.createElement("text",hd({},r,{dominantBaseline:"central",className:(0,y.$)("recharts-radial-bar-label",u)}),a.createElement("defs",null,a.createElement("path",{id:P,d:j})),a.createElement("textPath",{xlinkHref:"#".concat(P)},t))},hg=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:c,endAngle:u}=t,s=(c+u)/2;if("outside"===n){var{x:f,y:d}=np(i,a,l+r,s);return{x:f,y:d,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:p,y:h}=np(i,a,(o+l)/2,s);return{x:p,y:h,textAnchor:"middle",verticalAnchor:"middle"}},hm=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:l,height:c}=t,u=c>=0?1:-1,s=u*n,f=u>0?"end":"start",d=u>0?"start":"end",p=l>=0?1:-1,h=p*n,y=p>0?"end":"start",v=p>0?"start":"end";if("top"===i)return hf(hf({},{x:a+l/2,y:o-u*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===i)return hf(hf({},{x:a+l/2,y:o+c+s,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(o+c),0),width:l}:{});if("left"===i){var g={x:a-h,y:o+c/2,textAnchor:y,verticalAnchor:"middle"};return hf(hf({},g),r?{width:Math.max(g.x-r.x,0),height:c}:{})}if("right"===i){var m={x:a+l+h,y:o+c/2,textAnchor:v,verticalAnchor:"middle"};return hf(hf({},m),r?{width:Math.max(r.x+r.width-m.x,0),height:c}:{})}var b=r?{width:l,height:c}:{};return"insideLeft"===i?hf({x:a+h,y:o+c/2,textAnchor:v,verticalAnchor:"middle"},b):"insideRight"===i?hf({x:a+l-h,y:o+c/2,textAnchor:y,verticalAnchor:"middle"},b):"insideTop"===i?hf({x:a+l/2,y:o+s,textAnchor:"middle",verticalAnchor:d},b):"insideBottom"===i?hf({x:a+l/2,y:o+c-s,textAnchor:"middle",verticalAnchor:f},b):"insideTopLeft"===i?hf({x:a+h,y:o+s,textAnchor:v,verticalAnchor:d},b):"insideTopRight"===i?hf({x:a+l-h,y:o+s,textAnchor:y,verticalAnchor:d},b):"insideBottomLeft"===i?hf({x:a+h,y:o+c-s,textAnchor:v,verticalAnchor:f},b):"insideBottomRight"===i?hf({x:a+l-h,y:o+c-s,textAnchor:y,verticalAnchor:f},b):i&&"object"==typeof i&&(N(i.x)||C(i.x))&&(N(i.y)||C(i.y))?hf({x:a+$(i.x,l),y:o+$(i.y,c),textAnchor:"end",verticalAnchor:"end"},b):hf({x:a+l/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},b)},hb=e=>"cx"in e&&N(e.cx);function hx(e){var t,{offset:r=5}=e,n=hf({offset:r},hu(e,hl)),{viewBox:i,position:o,value:l,children:c,content:u,className:s="",textBreakAll:f,labelRef:d}=n,p=n2(),h=i||p;if(!h||F(l)&&F(c)&&!(0,a.isValidElement)(u)&&"function"!=typeof u)return null;if((0,a.isValidElement)(u)){var{labelRef:v}=n,g=hu(n,hc);return(0,a.cloneElement)(u,g)}if("function"==typeof u){if(t=(0,a.createElement)(u,n),(0,a.isValidElement)(t))return t}else t=hp(n);var m=hb(h),b=pt(n,!0);if(m&&("insideStart"===o||"insideEnd"===o||"end"===o))return hv(n,t,b);var x=m?hg(n):hm(n,h);return a.createElement(ho,hd({ref:d,className:(0,y.$)("recharts-label",s)},b,x,{breakAll:f}),t)}hx.displayName="Label";var hw=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:l,innerRadius:c,outerRadius:u,x:s,y:f,top:d,left:p,width:h,height:y,clockWise:v,labelViewBox:g}=e;if(g)return g;if(N(h)&&N(y)){if(N(s)&&N(f))return{x:s,y:f,width:h,height:y};if(N(d)&&N(p))return{x:d,y:p,width:h,height:y}}return N(s)&&N(f)?{x:s,y:f,width:0,height:0}:N(t)&&N(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:u||l||o||0,clockWise:v}:e.viewBox?e.viewBox:void 0},hO=(e,t,r)=>{if(!e)return null;var n={viewBox:t,labelRef:r};return!0===e?a.createElement(hx,hd({key:"label-implicit"},n)):D(e)?a.createElement(hx,hd({key:"label-implicit",value:e},n)):(0,a.isValidElement)(e)?e.type===hx?(0,a.cloneElement)(e,hf({key:"label-implicit"},n)):a.createElement(hx,hd({key:"label-implicit",content:e},n)):hh(e)?a.createElement(hx,hd({key:"label-implicit",content:e},n)):e&&"object"==typeof e?a.createElement(hx,hd({},e,{key:"label-implicit"},n)):null};hx.parseViewBox=hw,hx.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:n,labelRef:i}=e,o=hw(e),l=d7(n,hx).map((e,r)=>(0,a.cloneElement)(e,{viewBox:t||o,key:"label-".concat(r)}));return r?[hO(e.label,t||o,i),...l]:l};var hj=["viewBox"],hP=["viewBox"];function hE(){return(hE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function hS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hS(Object(r),!0).forEach(function(t){hk(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hS(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h_(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function hk(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class hM extends a.Component{constructor(e){super(e),this.tickRefs=a.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=h_(e,hj),i=this.props,{viewBox:a}=i,o=h_(i,hP);return!pH(r,a)||!pH(n,o)||!pH(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:c,width:u,height:s,orientation:f,tickSize:d,mirror:p,tickMargin:h}=this.props,y=p?-1:1,v=e.tickSize||d,g=N(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,o=(n=(i=c+!p*s)-y*v)-y*h,a=g;break;case"left":n=i=e.coordinate,a=(t=(r=l+!p*u)-y*v)-y*h,o=g;break;case"right":n=i=e.coordinate,a=(t=(r=l+p*u)+y*v)+y*h,o=g;break;default:t=r=e.coordinate,o=(n=(i=c+p*s)+y*v)+y*h,a=g}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:n,orientation:i,mirror:o,axisLine:l}=this.props,c=hA(hA(hA({},pt(this.props,!1)),pt(l,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var u=+("top"===i&&!o||"bottom"===i&&o);c=hA(hA({},c),{},{x1:e,y1:t+u*n,x2:e+r,y2:t+u*n})}else{var s=+("left"===i&&!o||"right"===i&&o);c=hA(hA({},c),{},{x1:e+s*r,y1:t,x2:e+s*r,y2:t+n})}return a.createElement("line",hE({},c,{className:(0,y.$)("recharts-cartesian-axis-line",k()(l,"className"))}))}static renderTickItem(e,t,r){var n,i=(0,y.$)(t.className,"recharts-cartesian-axis-tick-value");if(a.isValidElement(e))n=a.cloneElement(e,hA(hA({},t),{},{className:i}));else if("function"==typeof e)n=e(hA(hA({},t),{},{className:i}));else{var o="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(o=(0,y.$)(o,e.className)),n=a.createElement(ho,hE({},t,{className:o}),r)}return n}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:n,stroke:i,tick:o,tickFormatter:l,unit:c}=this.props,u=pW(hA(hA({},this.props),{},{ticks:r}),e,t),s=this.getTickTextAnchor(),f=this.getTickVerticalAnchor(),d=pt(this.props,!1),p=pt(o,!1),h=hA(hA({},d),{},{fill:"none"},pt(n,!1)),v=u.map((e,t)=>{var{line:r,tick:v}=this.getTickLineCoord(e),g=hA(hA(hA(hA({textAnchor:s,verticalAnchor:f},d),{},{stroke:"none",fill:i},p),v),{},{index:t,payload:e,visibleTicksCount:u.length,tickFormatter:l});return a.createElement(pX,hE({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},d3(this.props,e,t)),n&&a.createElement("line",hE({},h,r,{className:(0,y.$)("recharts-cartesian-axis-tick-line",k()(n,"className"))})),o&&hM.renderTickItem(o,g,"".concat("function"==typeof l?l(e.value,t):e.value).concat(c||"")))});return v.length>0?a.createElement("g",{className:"recharts-cartesian-axis-ticks"},v):null}render(){var{axisLine:e,width:t,height:r,className:n,hide:i}=this.props;if(i)return null;var{ticks:o}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:a.createElement(pX,{className:(0,y.$)("recharts-cartesian-axis",n),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,o),hx.renderCallByParent(this.props))}}hk(hM,"displayName","CartesianAxis"),hk(hM,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var hT=["x1","y1","x2","y2","key"],hC=["offset"],hN=["xAxisId","yAxisId"],hD=["xAxisId","yAxisId"];function hI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hR(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hI(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h$(){return(h$=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function hL(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var hU=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:n,y:i,width:o,height:l,ry:c}=e;return a.createElement("rect",{x:n,y:i,ry:c,width:o,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function hz(e,t){var r;if(a.isValidElement(e))r=a.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:n,y1:i,x2:o,y2:l,key:c}=t,u=pt(hL(t,hT),!1),{offset:s}=u,f=hL(u,hC);r=a.createElement("line",h$({},f,{x1:n,y1:i,x2:o,y2:l,fill:"none",key:c}))}return r}function hF(e){var{x:t,width:r,horizontal:n=!0,horizontalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:o,yAxisId:l}=e,c=hL(e,hN),u=i.map((e,i)=>hz(n,hR(hR({},c),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(i),index:i})));return a.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function hB(e){var{y:t,height:r,vertical:n=!0,verticalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:o,yAxisId:l}=e,c=hL(e,hD),u=i.map((e,i)=>hz(n,hR(hR({},c),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(i),index:i})));return a.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function hK(e){var{horizontalFill:t,fillOpacity:r,x:n,y:i,width:o,height:l,horizontalPoints:c,horizontal:u=!0}=e;if(!u||!t||!t.length)return null;var s=c.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==s[0]&&s.unshift(0);var f=s.map((e,c)=>{var u=s[c+1]?s[c+1]-e:i+l-e;if(u<=0)return null;var f=c%t.length;return a.createElement("rect",{key:"react-".concat(c),y:e,x:n,height:u,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function hq(e){var{vertical:t=!0,verticalFill:r,fillOpacity:n,x:i,y:o,width:l,height:c,verticalPoints:u}=e;if(!t||!r||!r.length)return null;var s=u.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==s[0]&&s.unshift(0);var f=s.map((e,t)=>{var u=s[t+1]?s[t+1]-e:i+l-e;if(u<=0)return null;var f=t%r.length;return a.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:u,height:c,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var hW=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return nE(pW(hR(hR(hR({},hM.defaultProps),r),{},{ticks:nS(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},hH=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return nE(pW(hR(hR(hR({},hM.defaultProps),r),{},{ticks:nS(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},hV={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function hY(e){var t=n6(),r=n4(),n=n5(),i=hR(hR({},pj(e,hV)),{},{x:N(e.x)?e.x:n.left,y:N(e.y)?e.y:n.top,width:N(e.width)?e.width:n.width,height:N(e.height)?e.height:n.height}),{xAxisId:o,yAxisId:l,x:c,y:u,width:s,height:f,syncWithTicks:d,horizontalValues:p,verticalValues:h}=i,y=nJ(),v=r9(e=>sA(e,"xAxis",o,y)),g=r9(e=>sA(e,"yAxis",l,y));if(!N(s)||s<=0||!N(f)||f<=0||!N(c)||c!==+c||!N(u)||u!==+u)return null;var m=i.verticalCoordinatesGenerator||hW,b=i.horizontalCoordinatesGenerator||hH,{horizontalPoints:x,verticalPoints:w}=i;if((!x||!x.length)&&"function"==typeof b){var O=p&&p.length,j=b({yAxis:g?hR(hR({},g),{},{ticks:O?p:g.ticks}):void 0,width:t,height:r,offset:n},!!O||d);K(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof j,"]")),Array.isArray(j)&&(x=j)}if((!w||!w.length)&&"function"==typeof m){var P=h&&h.length,E=m({xAxis:v?hR(hR({},v),{},{ticks:P?h:v.ticks}):void 0,width:t,height:r,offset:n},!!P||d);K(Array.isArray(E),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof E,"]")),Array.isArray(E)&&(w=E)}return a.createElement("g",{className:"recharts-cartesian-grid"},a.createElement(hU,{fill:i.fill,fillOpacity:i.fillOpacity,x:i.x,y:i.y,width:i.width,height:i.height,ry:i.ry}),a.createElement(hK,h$({},i,{horizontalPoints:x})),a.createElement(hq,h$({},i,{verticalPoints:w})),a.createElement(hF,h$({},i,{offset:n,horizontalPoints:x,xAxis:v,yAxis:g})),a.createElement(hB,h$({},i,{offset:n,verticalPoints:w,xAxis:v,yAxis:g})))}hY.displayName="CartesianGrid";var hX=["children"],hG=["dangerouslySetInnerHTML","ticks"];function hZ(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hQ(){return(hQ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function hJ(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function h0(e){r6();var t=(0,a.useMemo)(()=>{var{children:t}=e;return hJ(e,hX)},[e]),r=r9(e=>ur(e,t.id));return t===r?e.children:null}var h1=e=>{var{xAxisId:t,className:r}=e,n=r9(nZ),i=nJ(),o="xAxis",l=r9(e=>sl(e,o,t,i)),c=r9(e=>s_(e,o,t,i)),u=r9(e=>sy(e,t)),s=r9(e=>sx(e,t));if(null==u||null==s)return null;var{dangerouslySetInnerHTML:f,ticks:d}=e,p=hJ(e,hG);return a.createElement(hM,hQ({},p,{scale:l,x:s.x,y:s.y,width:u.width,height:u.height,className:(0,y.$)("recharts-".concat(o," ").concat(o),r),viewBox:n,ticks:c}))},h2=e=>{var t,r,n,i,o;return a.createElement(h0,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(n=e.angle)?n:0,minTickGap:null!=(i=e.minTickGap)?i:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter},a.createElement(h1,e))};class h3 extends a.Component{render(){return a.createElement(h2,this.props)}}hZ(h3,"displayName","XAxis"),hZ(h3,"defaultProps",{allowDataOverflow:ut.allowDataOverflow,allowDecimals:ut.allowDecimals,allowDuplicatedCategory:ut.allowDuplicatedCategory,height:ut.height,hide:!1,mirror:ut.mirror,orientation:ut.orientation,padding:ut.padding,reversed:ut.reversed,scale:ut.scale,tickCount:ut.tickCount,type:ut.type,xAxisId:0});var h5=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0},h6=["dangerouslySetInnerHTML","ticks"];function h4(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h8(){return(h8=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h7(e){return r6(),null}var h9=e=>{var t,{yAxisId:r,className:n,width:i,label:o}=e,l=(0,a.useRef)(null),c=(0,a.useRef)(null),u=r9(nZ),s=nJ(),f=r6(),d="yAxis",p=r9(e=>sl(e,d,r,s)),h=r9(e=>sO(e,r)),v=r9(e=>sw(e,r)),g=r9(e=>s_(e,d,r,s));if((0,a.useLayoutEffect)(()=>{if(!("auto"!==i||!h||hh(o)||(0,a.isValidElement)(o))){var e,t=l.current,n=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:u,tickMargin:s}=t.props,d=h5({ticks:n,label:c.current,labelGapWithTick:5,tickSize:u,tickMargin:s});Math.round(h.width)!==Math.round(d)&&f(f3({id:r,width:d}))}},[l,null==l||null==(t=l.current)||null==(t=t.tickRefs)?void 0:t.current,null==h?void 0:h.width,h,f,o,r,i]),null==h||null==v)return null;var{dangerouslySetInnerHTML:m,ticks:b}=e,x=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,h6);return a.createElement(hM,h8({},x,{ref:l,labelRef:c,scale:p,x:v.x,y:v.y,width:h.width,height:h.height,className:(0,y.$)("recharts-".concat(d," ").concat(d),n),viewBox:u,ticks:g}))},ye=e=>{var t,r,n,i,o;return a.createElement(a.Fragment,null,a.createElement(h7,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(n=e.angle)?n:0,minTickGap:null!=(i=e.minTickGap)?i:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter}),a.createElement(h9,e))},yt={allowDataOverflow:un.allowDataOverflow,allowDecimals:un.allowDecimals,allowDuplicatedCategory:un.allowDuplicatedCategory,hide:!1,mirror:un.mirror,orientation:un.orientation,padding:un.padding,reversed:un.reversed,scale:un.scale,tickCount:un.tickCount,type:un.type,width:un.width,yAxisId:0};class yr extends a.Component{render(){return a.createElement(ye,this.props)}}h4(yr,"displayName","YAxis"),h4(yr,"defaultProps",yt);var yn=r(1215);function yi(){return(yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ya(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ya(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ya(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yl(e){return Array.isArray(e)&&D(e[0])&&D(e[1])?e.join(" ~ "):e}var yc=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:n={},labelStyle:i={},payload:o,formatter:l,itemSorter:c,wrapperClassName:u,labelClassName:s,label:f,labelFormatter:d,accessibilityLayer:p=!1}=e,h=yo({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),v=yo({margin:0},i),g=!F(f),m=g?f:"",b=(0,y.$)("recharts-default-tooltip",u),x=(0,y.$)("recharts-tooltip-label",s);return g&&d&&null!=o&&(m=d(f,o)),a.createElement("div",yi({className:b,style:h},p?{role:"status","aria-live":"assertive"}:{}),a.createElement("p",{className:x,style:v},a.isValidElement(m)?m:"".concat(m)),(()=>{if(o&&o.length){var e=(c?nt()(o,c):o).map((e,r)=>{if("none"===e.type)return null;var i=e.formatter||l||yl,{value:c,name:u}=e,s=c,f=u;if(i){var d=i(c,u,e,r,o);if(Array.isArray(d))[s,f]=d;else{if(null==d)return null;s=d}}var p=yo({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},n);return a.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:p},D(f)?a.createElement("span",{className:"recharts-tooltip-item-name"},f):null,D(f)?a.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,a.createElement("span",{className:"recharts-tooltip-item-value"},s),a.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return a.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},yu="recharts-tooltip-wrapper",ys={visibility:"hidden"};function yf(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:c,viewBoxDimension:u}=e;if(a&&N(a[n]))return a[n];var s=r[n]-l-(i>0?i:0),f=r[n]+i;if(t[n])return o[n]?s:f;var d=c[n];return null==d?0:o[n]?s<d?Math.max(f,d):Math.max(s,d):null==u?0:f+l>d+u?Math.max(s,d):Math.max(f,d)}function yd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yp(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yd(Object(r),!0).forEach(function(t){yh(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yd(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yh(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class yy extends a.PureComponent{constructor(){super(...arguments),yh(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),yh(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:n,children:i,coordinate:o,hasPayload:l,isAnimationActive:c,offset:u,position:s,reverseDirection:f,useTranslate3d:d,viewBox:p,wrapperStyle:h,lastBoundingBox:v,innerRef:g,hasPortalFromProps:m}=this.props,{cssClasses:b,cssProperties:x}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:l,reverseDirection:c,tooltipBox:u,useTranslate3d:s,viewBox:f}=e;return{cssProperties:t=u.height>0&&u.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=yf({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:u.width,viewBox:f,viewBoxDimension:f.width}),translateY:n=yf({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:u.height,viewBox:f,viewBoxDimension:f.height}),useTranslate3d:s}):ys,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,y.$)(yu,{["".concat(yu,"-right")]:N(r)&&t&&N(t.x)&&r>=t.x,["".concat(yu,"-left")]:N(r)&&t&&N(t.x)&&r<t.x,["".concat(yu,"-bottom")]:N(n)&&t&&N(t.y)&&n>=t.y,["".concat(yu,"-top")]:N(n)&&t&&N(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:u,position:s,reverseDirection:f,tooltipBox:{height:v.height,width:v.width},useTranslate3d:d,viewBox:p}),w=m?{}:yp(yp({transition:c&&e?"transform ".concat(r,"ms ").concat(n):void 0},x),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&l?"visible":"hidden",position:"absolute",top:0,left:0}),O=yp(yp({},w),{},{visibility:!this.state.dismissed&&e&&l?"visible":"hidden"},h);return a.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:b,style:O,ref:g},i)}}var yv=r(3854),yg=r.n(yv);function ym(){}function yb(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function yx(e){this._context=e}function yw(e){this._context=e}function yO(e){this._context=e}yx.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:yb(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:yb(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},yw.prototype={areaStart:ym,areaEnd:ym,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:yb(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},yO.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:yb(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class yj{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function yP(e){this._context=e}function yE(e){this._context=e}function yS(e){return new yE(e)}yP.prototype={areaStart:ym,areaEnd:ym,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function yA(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function y_(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function yk(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function yM(e){this._context=e}function yT(e){this._context=new yC(e)}function yC(e){this._context=e}function yN(e){this._context=e}function yD(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function yI(e,t){this._context=e,this._t=t}yE.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},yM.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:yk(this,this._t0,y_(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,yk(this,y_(this,r=yA(this,e,t)),r);break;default:yk(this,this._t0,r=yA(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(yT.prototype=Object.create(yM.prototype)).point=function(e,t){yM.prototype.point.call(this,t,e)},yC.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},yN.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=yD(e),i=yD(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},yI.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};let yR=Math.PI,y$=2*yR,yL=y$-1e-6;function yU(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class yz{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?yU:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return yU;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,n,i){if(e*=1,t*=1,r*=1,n*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let a=this._x1,o=this._y1,l=r-e,c=n-t,u=a-e,s=o-t,f=u*u+s*s;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6)if(Math.abs(s*l-c*u)>1e-6&&i){let d=r-a,p=n-o,h=l*l+c*c,y=Math.sqrt(h),v=Math.sqrt(f),g=i*Math.tan((yR-Math.acos((h+f-(d*d+p*p))/(2*y*v)))/2),m=g/v,b=g/y;Math.abs(m-1)>1e-6&&this._append`L${e+m*u},${t+m*s}`,this._append`A${i},${i},0,0,${+(s*d>u*p)},${this._x1=e+b*l},${this._y1=t+b*c}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,n,i,a){if(e*=1,t*=1,r*=1,a=!!a,r<0)throw Error(`negative radius: ${r}`);let o=r*Math.cos(n),l=r*Math.sin(n),c=e+o,u=t+l,s=1^a,f=a?n-i:i-n;null===this._x1?this._append`M${c},${u}`:(Math.abs(this._x1-c)>1e-6||Math.abs(this._y1-u)>1e-6)&&this._append`L${c},${u}`,r&&(f<0&&(f=f%y$+y$),f>yL?this._append`A${r},${r},0,1,${s},${e-o},${t-l}A${r},${r},0,1,${s},${this._x1=c},${this._y1=u}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=yR)},${s},${this._x1=e+r*Math.cos(i)},${this._y1=t+r*Math.sin(i)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function yF(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new yz(t)}function yB(e){return e[0]}function yK(e){return e[1]}function yq(e,t){var r=na(!0),n=null,i=yS,a=null,o=yF(l);function l(l){var c,u,s,f=(l=ni(l)).length,d=!1;for(null==n&&(a=i(s=o())),c=0;c<=f;++c)!(c<f&&r(u=l[c],c,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(u,c,l),+t(u,c,l));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?yB:na(e),t="function"==typeof t?t:void 0===t?yK:na(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:na(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:na(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:na(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function yW(e,t,r){var n=null,i=na(!0),a=null,o=yS,l=null,c=yF(u);function u(u){var s,f,d,p,h,y=(u=ni(u)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(h=c())),s=0;s<=y;++s){if(!(s<y&&i(p=u[s],s,u))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=s-1;d>=f;--d)l.point(g[d],m[d]);l.lineEnd(),l.areaEnd()}v&&(g[s]=+e(p,s,u),m[s]=+t(p,s,u),l.point(n?+n(p,s,u):g[s],r?+r(p,s,u):m[s]))}if(h)return l=null,h+""||null}function s(){return yq().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?yB:na(+e),t="function"==typeof t?t:void 0===t?na(0):na(+t),r="function"==typeof r?r:void 0===r?yK:na(+r),u.x=function(t){return arguments.length?(e="function"==typeof t?t:na(+t),n=null,u):e},u.x0=function(t){return arguments.length?(e="function"==typeof t?t:na(+t),u):e},u.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:na(+e),u):n},u.y=function(e){return arguments.length?(t="function"==typeof e?e:na(+e),r=null,u):t},u.y0=function(e){return arguments.length?(t="function"==typeof e?e:na(+e),u):t},u.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:na(+e),u):r},u.lineX0=u.lineY0=function(){return s().x(e).y(t)},u.lineY1=function(){return s().x(e).y(r)},u.lineX1=function(){return s().x(n).y(t)},u.defined=function(e){return arguments.length?(i="function"==typeof e?e:na(!!e),u):i},u.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),u):o},u.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),u):a},u}function yH(){return(yH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function yV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yV(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}yz.prototype;var yX={curveBasisClosed:function(e){return new yw(e)},curveBasisOpen:function(e){return new yO(e)},curveBasis:function(e){return new yx(e)},curveBumpX:function(e){return new yj(e,!0)},curveBumpY:function(e){return new yj(e,!1)},curveLinearClosed:function(e){return new yP(e)},curveLinear:yS,curveMonotoneX:function(e){return new yM(e)},curveMonotoneY:function(e){return new yT(e)},curveNatural:function(e){return new yN(e)},curveStep:function(e){return new yI(e,.5)},curveStepAfter:function(e){return new yI(e,1)},curveStepBefore:function(e){return new yI(e,0)}},yG=e=>lW(e.x)&&lW(e.y),yZ=e=>e.x,yQ=e=>e.y,yJ=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat(B(e));return("curveMonotone"===r||"curveBump"===r)&&t?yX["".concat(r).concat("vertical"===t?"Y":"X")]:yX[r]||yS},y0=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=yJ(r,a),c=o?n.filter(yG):n;if(Array.isArray(i)){var u=o?i.filter(e=>yG(e)):i,s=c.map((e,t)=>yY(yY({},e),{},{base:u[t]}));return(t="vertical"===a?yW().y(yQ).x1(yZ).x0(e=>e.base.x):yW().x(yZ).y1(yQ).y0(e=>e.base.y)).defined(yG).curve(l),t(s)}return(t="vertical"===a&&N(i)?yW().y(yQ).x1(yZ).x0(i):N(i)?yW().x(yZ).y1(yQ).y0(i):yq().x(yZ).y(yQ)).defined(yG).curve(l),t(c)},y1=e=>{var{className:t,points:r,path:n,pathRef:i}=e;if((!r||!r.length)&&!n)return null;var o=r&&r.length?y0(e):n;return a.createElement("path",yH({},pt(e,!1),d1(e),{className:(0,y.$)("recharts-curve",t),d:null===o?void 0:o,ref:i}))},y2=["x","y","top","left","width","height","className"];function y3(){return(y3=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y5(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var y6=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),y4=e=>{var{x:t=0,y:r=0,top:n=0,left:i=0,width:o=0,height:l=0,className:c}=e,u=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y5(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y5(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:n,left:i,width:o,height:l},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y2));return N(t)&&N(r)&&N(o)&&N(l)&&N(n)&&N(i)?a.createElement("path",y3({},pt(u,!0),{className:(0,y.$)("recharts-cross",c),d:y6(t,r,o,l,n,i)})):null},y8=r(2728),y7=r.n(y8),y9=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],ve=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),vt=(e,t)=>r=>ve(y9(e,t),r),vr=(e,t)=>r=>ve([...y9(e,t).map((e,t)=>e*t).slice(1),0],r),vn=function(){for(var e,t,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var c=vt(e,t),u=vt(r,n),s=vr(e,t),f=e=>e>1?1:e<0?0:e,d=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=c(r)-t,a=s(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=f(r-i/a)}return u(r)};return d.isStepper=!1,d},vi=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},va=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return vn(e);case"spring":return vi();default:if("cubic-bezier"===e.split("(")[0])return vn(e)}return"function"==typeof e?e:null};function vo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vo(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var vc=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),vu=(e,t,r)=>e.map(e=>"".concat(vc(e)," ").concat(t,"ms ").concat(r)).join(","),vs=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),vf=(e,t)=>Object.keys(t).reduce((r,n)=>vl(vl({},r),{},{[n]:e(n,t[n])}),{});function vd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vp(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vd(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vd(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var vh=(e,t,r)=>e+(t-e)*r,vy=e=>{var{from:t,to:r}=e;return t!==r},vv=(e,t,r)=>{var n=vf((t,r)=>{if(vy(r)){var[n,i]=e(r.from,r.to,r.velocity);return vp(vp({},r),{},{from:n,velocity:i})}return r},t);return r<1?vf((e,t)=>vy(t)?vp(vp({},t),{},{velocity:vh(t.velocity,n[e].velocity,r),from:vh(t.from,n[e].from,r)}):t,t):vv(e,n,r-1)};let vg=(e,t,r,n,i,a)=>{var o=vs(e,t);return!0===r.isStepper?function(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>vp(vp({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),c=()=>vf((e,t)=>t.from,l),u=()=>!Object.values(l).filter(vy).length,s=null,f=n=>{o||(o=n);var d=(n-o)/r.dt;l=vv(r,l,d),i(vp(vp(vp({},e),t),c())),o=n,u()||(s=a.setTimeout(f))};return()=>(s=a.setTimeout(f),()=>{s()})}(e,t,r,o,i,a):function(e,t,r,n,i,a,o){var l,c=null,u=i.reduce((r,n)=>vp(vp({},r),{},{[n]:[e[n],t[n]]}),{}),s=i=>{l||(l=i);var f=(i-l)/n,d=vf((e,t)=>vh(...t,r(f)),u);if(a(vp(vp(vp({},e),t),d)),f<1)c=o.setTimeout(s);else{var p=vf((e,t)=>vh(...t,r(1)),u);a(vp(vp(vp({},e),t),p))}};return()=>(c=o.setTimeout(s),()=>{c()})}(e,t,r,n,o,i,a)};class vm{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var vb=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function vx(){return(vx=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vw(Object(r),!0).forEach(function(t){vj(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vw(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vj(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class vP extends a.PureComponent{constructor(e,t){super(e,t),vj(this,"mounted",!1),vj(this,"manager",null),vj(this,"stopJSAnimation",null),vj(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:o}=this.props,{style:l}=this.state;if(r){if(!t){this.state&&l&&(n&&l[n]!==a||!n&&l!==a)&&this.setState({style:n?{[n]:a}:a});return}if(!y7()(e.to,a)||!e.canBegin||!e.isActive){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var u=c||i?o:e.to;this.state&&l&&(n&&l[n]!==u||!n&&l!==u)&&this.setState({style:n?{[n]:u}:u}),this.runAnimation(vO(vO({},this.props),{},{from:u,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,c=vg(t,r,va(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=c()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:c}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof c||"spring"===a)return void this.runJSAnimation(e);var u=n?{[n]:i}:i,s=vu(Object.keys(u),r,a);this.manager.start([o,t,vO(vO({},u),{},{transition:s}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:n,attributeName:i,easing:o,isActive:l,from:c,to:u,canBegin:s,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:p,animationManager:h}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,vb),v=a.Children.count(t),g=this.state.style;if("function"==typeof t)return t(g);if(!l||0===v||n<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,a.cloneElement)(e,vO(vO({},y),{},{style:vO(vO({},t),g),className:r}))};return 1===v?m(a.Children.only(t)):a.createElement("div",null,a.Children.map(t,e=>m(e)))}}vj(vP,"displayName","Animate"),vj(vP,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var vE=(0,a.createContext)(null);function vS(e){var t,r,n,i,o,l,c,u,s=(0,a.useContext)(vE);return a.createElement(vP,vx({},e,{animationManager:null!=(c=null!=(u=e.animationManager)?u:s)?c:(t=new vm,n=()=>null,i=!1,o=null,l=e=>{if(!i){if(Array.isArray(e)){if(!e.length)return;var[r,...a]=e;if("number"==typeof r){o=t.setTimeout(l.bind(null,a),r);return}l(r),o=t.setTimeout(l.bind(null,a));return}"object"==typeof e&&n(e),"function"==typeof e&&e()}},{stop:()=>{i=!0},start:e=>{i=!1,o&&(o(),o=null),l(e)},subscribe:e=>(n=e,()=>{n=()=>null}),getTimeoutController:()=>t})}))}function vA(){return(vA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v_=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,u=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(u,",").concat(e+c*s[0],",").concat(t)),a+="L ".concat(e+r-c*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(u,",\n        ").concat(e+r,",").concat(t+l*s[1])),a+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(u,",\n        ").concat(e+r-c*s[2],",").concat(t+n)),a+="L ".concat(e+c*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(u,",\n        ").concat(e,",").concat(t+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+c*d,",").concat(t,"\n            L ").concat(e+r-c*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+r-c*d,",").concat(t+n,"\n            L ").concat(e+c*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},vk={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},vM=e=>{var t=pj(e,vk),r=(0,a.useRef)(null),[n,i]=(0,a.useState)(-1);(0,a.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:o,y:l,width:c,height:u,radius:s,className:f}=t,{animationEasing:d,animationDuration:p,animationBegin:h,isAnimationActive:v,isUpdateAnimationActive:g}=t;if(o!==+o||l!==+l||c!==+c||u!==+u||0===c||0===u)return null;var m=(0,y.$)("recharts-rectangle",f);return g?a.createElement(vS,{canBegin:n>0,from:{width:c,height:u,x:o,y:l},to:{width:c,height:u,x:o,y:l},duration:p,animationEasing:d,isActive:g},e=>{var{width:i,height:o,x:l,y:c}=e;return a.createElement(vS,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:h,duration:p,isActive:v,easing:d},a.createElement("path",vA({},pt(t,!0),{className:m,d:v_(l,c,i,o,s),ref:r})))}):a.createElement("path",vA({},pt(t,!0),{className:m,d:v_(o,l,c,u,s)}))};function vT(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[np(t,r,n,i),np(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function vC(){return(vC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var vN=(e,t)=>M(t-e)*Math.min(Math.abs(t-e),359.999),vD=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:l,cornerIsExternal:c}=e,u=l*(o?1:-1)+n,s=Math.asin(l/u)/nf,f=c?i:i+a*s,d=np(t,r,u,f);return{center:d,circleTangency:np(t,r,n,f),lineTangency:np(t,r,u*Math.cos(s*nf),c?i-a*s:i),theta:s}},vI=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,l=vN(a,o),c=a+l,u=np(t,r,i,a),s=np(t,r,i,c),f="M ".concat(u.x,",").concat(u.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>c),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var d=np(t,r,n,a),p=np(t,r,n,c);f+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=c),",\n            ").concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},vR=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:c,endAngle:u}=e,s=M(u-c),{circleTangency:f,lineTangency:d,theta:p}=vD({cx:t,cy:r,radius:i,angle:c,sign:s,cornerRadius:a,cornerIsExternal:l}),{circleTangency:h,lineTangency:y,theta:v}=vD({cx:t,cy:r,radius:i,angle:u,sign:-s,cornerRadius:a,cornerIsExternal:l}),g=l?Math.abs(c-u):Math.abs(c-u)-p-v;if(g<0)return o?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):vI({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:u});var m="M ".concat(d.x,",").concat(d.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(f.x,",").concat(f.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(y.x,",").concat(y.y,"\n  ");if(n>0){var{circleTangency:b,lineTangency:x,theta:w}=vD({cx:t,cy:r,radius:n,angle:c,sign:s,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:O,lineTangency:j,theta:P}=vD({cx:t,cy:r,radius:n,angle:u,sign:-s,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),E=l?Math.abs(c-u):Math.abs(c-u)-w-P;if(E<0&&0===a)return"".concat(m,"L").concat(t,",").concat(r,"Z");m+="L".concat(j.x,",").concat(j.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(O.x,",").concat(O.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(E>180),",").concat(+(s>0),",").concat(b.x,",").concat(b.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(x.x,",").concat(x.y,"Z")}else m+="L".concat(t,",").concat(r,"Z");return m},v$={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},vL=e=>{var t,r=pj(e,v$),{cx:n,cy:i,innerRadius:o,outerRadius:l,cornerRadius:c,forceCornerRadius:u,cornerIsExternal:s,startAngle:f,endAngle:d,className:p}=r;if(l<o||f===d)return null;var h=(0,y.$)("recharts-sector",p),v=l-o,g=$(c,v,0,!0);return t=g>0&&360>Math.abs(f-d)?vR({cx:n,cy:i,innerRadius:o,outerRadius:l,cornerRadius:Math.min(g,v/2),forceCornerRadius:u,cornerIsExternal:s,startAngle:f,endAngle:d}):vI({cx:n,cy:i,innerRadius:o,outerRadius:l,startAngle:f,endAngle:d}),a.createElement("path",vC({},pt(r,!0),{className:h,d:t}))};function vU(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vU(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vU(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var vF=()=>r9(sY),vB=()=>{var e=vF(),t=r9(fp),r=r9(fs);return nR(vz(vz({},e),{},{scale:r}),t)};function vK(){return(vK=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vW(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vq(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vH(e){var t,r,n,{coordinate:i,payload:o,index:l,offset:c,tooltipAxisBandSize:u,layout:s,cursor:f,tooltipEventType:d,chartName:p}=e;if(!f||!i||"ScatterChart"!==p&&"axis"!==d)return null;if("ScatterChart"===p)r=i,n=y4;else if("BarChart"===p)t=u/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===s?i.x-t:c.left+.5,y:"horizontal"===s?c.top+.5:i.y-t,width:"horizontal"===s?u:c.width-1,height:"horizontal"===s?c.height-1:u},n=vM;else if("radial"===s){var{cx:h,cy:v,radius:g,startAngle:m,endAngle:b}=vT(i);r={cx:h,cy:v,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},n=vL}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return vT(t);else{var{cx:l,cy:c,innerRadius:u,outerRadius:s,angle:f}=t,d=np(l,c,u,f),p=np(l,c,s,f);n=d.x,i=d.y,a=p.x,o=p.y}return[{x:n,y:i},{x:a,y:o}]}(s,i,c)},n=y1;var x="object"==typeof f&&"className"in f?f.className:void 0,w=vW(vW(vW(vW({stroke:"#ccc",pointerEvents:"none"},c),r),pt(f,!1)),{},{payload:o,payloadIndex:l,className:(0,y.$)("recharts-tooltip-cursor",x)});return(0,a.isValidElement)(f)?(0,a.cloneElement)(f,w):(0,a.createElement)(n,w)}function vV(e){var t=vB(),r=n5(),n=n7(),i=fA();return a.createElement(vH,vK({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:n,tooltipAxisBandSize:t,chartName:i}))}function vY(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vY(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vY(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vG(e){return e.dataKey}var vZ=[],vQ={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!pM.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function vJ(e){var t,r,n,i=pj(e,vQ),{active:o,allowEscapeViewBox:l,animationDuration:c,animationEasing:u,content:s,filterNull:f,isAnimationActive:d,offset:p,payloadUniqBy:h,position:y,reverseDirection:v,useTranslate3d:g,wrapperStyle:m,cursor:b,shared:x,trigger:w,defaultIndex:O,portal:j,axisId:P}=i;r6();var E="number"==typeof O?String(O):O,S=n2(),A=pr(),_=r9(e=>sR(e,x)),{activeIndex:k,isActive:M}=r9(e=>fz(e,_,w,E)),T=r9(e=>fU(e,_,w,E)),C=r9(e=>fL(e,_,w,E)),N=r9(e=>f$(e,_,w,E)),D=ph(),I=null!=o?o:M,[R,$]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,a.useState)({height:0,left:0,top:0,width:0}),n=(0,a.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,n]}([T,I]),L="axis"===_?C:void 0;r9(e=>fD(e,_,w)),r9(cK),r9(cF),r9(cB),null==(t=r9(pd))||t.active;var U=null!=j?j:D;if(null==U)return null;var z=null!=T?T:vZ;I||(z=vZ),f&&z.length&&(r=T.filter(e=>null!=e.value&&(!0!==e.hide||i.includeHidden)),z=!0===h?yg()(r,vG):"function"==typeof h?yg()(r,h):r);var F=z.length>0,B=a.createElement(yy,{allowEscapeViewBox:l,animationDuration:c,animationEasing:u,isAnimationActive:d,active:I,coordinate:N,hasPayload:F,offset:p,position:y,reverseDirection:v,useTranslate3d:g,viewBox:S,wrapperStyle:m,lastBoundingBox:R,innerRef:$,hasPortalFromProps:!!j},(n=vX(vX({},i),{},{payload:z,label:L,active:I,coordinate:N,accessibilityLayer:A}),a.isValidElement(s)?a.cloneElement(s,n):"function"==typeof s?a.createElement(s,n):a.createElement(yc,n)));return a.createElement(a.Fragment,null,(0,yn.createPortal)(B,U),I&&a.createElement(vV,{cursor:b,tooltipEventType:_,coordinate:N,payload:T,index:k}))}function v0(){return(v0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v1=e=>{var{cx:t,cy:r,r:n,className:i}=e,o=(0,y.$)("recharts-dot",i);return t===+t&&r===+r&&n===+n?a.createElement("circle",v0({},pt(e,!1),d1(e),{className:o,cx:t,cy:r,r:n})):null},v2=r(9474),v3=r.n(v2),v5=["valueAccessor"],v6=["data","dataKey","clockWise","id","textBreakAll"];function v4(){return(v4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v8(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v7(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v8(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v8(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v9(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var ge=e=>Array.isArray(e.value)?v3()(e.value):e.value;function gt(e){var{valueAccessor:t=ge}=e,r=v9(e,v5),{data:n,dataKey:i,clockWise:o,id:l,textBreakAll:c}=r,u=v9(r,v6);return n&&n.length?a.createElement(pX,{className:"recharts-label-list"},n.map((e,r)=>{var n=F(i)?t(e,r):nw(e&&e.payload,i),s=F(l)?{}:{id:"".concat(l,"-").concat(r)};return a.createElement(hx,v4({},pt(e,!0),u,s,{parentViewBox:e.parentViewBox,value:n,textBreakAll:c,viewBox:hx.parseViewBox(F(o)?e:v7(v7({},e),{},{clockWise:o})),key:"label-".concat(r),index:r}))})):null}gt.displayName="LabelList";function gr(e){return r6(),null}gt.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var{children:i}=e,o=d7(i,gt).map((e,r)=>(0,a.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return n?[(r=e.label,r?!0===r?a.createElement(gt,{key:"labelList-implicit",data:t}):a.isValidElement(r)||hh(r)?a.createElement(gt,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?a.createElement(gt,v4({data:t},r,{key:"labelList-implicit"})):null:null),...o]:o};var gn=["children"],gi=()=>{},ga=(0,a.createContext)({addErrorBar:gi,removeErrorBar:gi}),go=(0,a.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function gl(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,gn);return a.createElement(go.Provider,{value:r},t)}var gc=()=>(0,a.useContext)(go),gu=e=>{var{children:t,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:o,data:l,stackId:c,hide:u,type:s,barSize:f}=e,[d,p]=a.useState([]),h=(0,a.useCallback)(e=>{p(t=>[...t,e])},[p]),y=(0,a.useCallback)(e=>{p(t=>t.filter(t=>t!==e))},[p]),v=nJ();return a.createElement(ga.Provider,{value:{addErrorBar:h,removeErrorBar:y}},a.createElement(gr,{type:s,data:l,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:o,errorBars:d,stackId:c,hide:u,barSize:f,isPanorama:v}),t)};function gs(e){var{addErrorBar:t,removeErrorBar:r}=(0,a.useContext)(ga);return null}var gf=e=>{var t=nJ();return r9(r=>sM(r,"xAxis",e,t))},gd=e=>{var t=nJ();return r9(r=>sM(r,"yAxis",e,t))},gp=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function gh(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gy(){return(gy=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gv(e){var{direction:t,width:r,dataKey:n,isAnimationActive:i,animationBegin:o,animationDuration:l,animationEasing:c}=e,u=pt(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,gp),!1),{data:s,dataPointFormatter:f,xAxisId:d,yAxisId:p,errorBarOffset:h}=gc(),y=gf(d),v=gd(p);if((null==y?void 0:y.scale)==null||(null==v?void 0:v.scale)==null||null==s||"x"===t&&"number"!==y.type)return null;var g=s.map(e=>{var s,d,{x:p,y:g,value:m,errorVal:b}=f(e,n,t);if(!b)return null;var x=[];if(Array.isArray(b)?[s,d]=b:s=d=b,"x"===t){var{scale:w}=y,O=g+h,j=O+r,P=O-r,E=w(m-s),S=w(m+d);x.push({x1:S,y1:j,x2:S,y2:P}),x.push({x1:E,y1:O,x2:S,y2:O}),x.push({x1:E,y1:j,x2:E,y2:P})}else if("y"===t){var{scale:A}=v,_=p+h,k=_-r,M=_+r,T=A(m-s),C=A(m+d);x.push({x1:k,y1:C,x2:M,y2:C}),x.push({x1:_,y1:T,x2:_,y2:C}),x.push({x1:k,y1:T,x2:M,y2:T})}var N="".concat(p+h,"px ").concat(g+h,"px");return a.createElement(pX,gy({className:"recharts-errorBar",key:"bar-".concat(x.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},u),x.map(e=>{var t=i?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return a.createElement(vS,{from:{transform:"scaleY(0)",transformOrigin:N},to:{transform:"scaleY(1)",transformOrigin:N},begin:o,easing:c,isActive:i,duration:l,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:N}},a.createElement("line",gy({},e,{style:t})))}))});return a.createElement(pX,{className:"recharts-errorBars"},g)}var gg=(0,a.createContext)(void 0);function gm(e){var{direction:t,children:r}=e;return a.createElement(gg.Provider,{value:t},r)}var gb={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function gx(e){var t,r,n=(t=e.direction,r=(0,a.useContext)(gg),null!=t?t:null!=r?r:"x"),{width:i,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:u}=pj(e,gb);return a.createElement(a.Fragment,null,a.createElement(gs,{dataKey:e.dataKey,direction:n}),a.createElement(gv,gy({},e,{direction:n,width:i,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:u})))}class gw extends a.Component{render(){return a.createElement(gx,this.props)}}function gO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gO(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}gh(gw,"defaultProps",gb),gh(gw,"displayName","ErrorBar");var gP=e=>{var t,{point:r,childIndex:n,mainColor:i,activeDot:o,dataKey:l}=e;if(!1===o||null==r.x||null==r.y)return null;var c=gj(gj({index:n,dataKey:l,cx:r.x,cy:r.y,r:4,fill:null!=i?i:"none",strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},pt(o,!1)),d1(o));return t=(0,a.isValidElement)(o)?(0,a.cloneElement)(o,c):"function"==typeof o?o(c):a.createElement(v1,c),a.createElement(pX,{className:"recharts-active-dot"},t)};function gE(e){var t,{points:r,mainColor:n,activeDot:i,itemDataKey:a}=e,o=vF(),l=r9(fm),c=r9(fb);if(!l)return null;var u=o.dataKey;return F(t=u&&!o.allowDuplicatedCategory?z(r,"function"==typeof u?e=>u(e.payload):"payload.".concat(u),c):null==r?void 0:r[Number(l)])?null:gP({point:t,childIndex:Number(l),mainColor:n,dataKey:a,activeDot:i})}function gS(e){var{fn:t,args:r}=e;return r6(),nJ(),null}function gA(e,t){var r,n,i=r9(t=>ur(t,e)),a=r9(e=>ui(e,t)),o=null!=(r=null==i?void 0:i.allowDataOverflow)?r:ut.allowDataOverflow,l=null!=(n=null==a?void 0:a.allowDataOverflow)?n:un.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function g_(e){var{xAxisId:t,yAxisId:r,clipPathId:n}=e,i=n5(),{needClipX:o,needClipY:l,needClip:c}=gA(t,r);if(!c)return null;var{left:u,top:s,width:f,height:d}=i;return a.createElement("clipPath",{id:"clipPath-".concat(n)},a.createElement("rect",{x:o?u:u-f/2,y:l?s:s-d/2,width:o?f:2*f,height:l?d:2*d}))}var gk=(e,t,r,n)=>sM(e,"xAxis",t,n),gM=(e,t,r,n)=>sk(e,"xAxis",t,n),gT=(e,t,r,n)=>sM(e,"yAxis",r,n),gC=(e,t,r,n)=>sk(e,"yAxis",r,n),gN=r0([n8,gk,gT,gM,gC],(e,t,r,n,i)=>nP(e,"xAxis")?nR(t,n,!1):nR(r,i,!1)),gD=r0([uf,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"line"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),gI=r0([n8,gk,gT,gM,gC,gD,gN,lq],(e,t,r,n,i,a,o,l)=>{var c,{chartData:u,dataStartIndex:s,dataEndIndex:f}=l;if(null!=a&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=o){var{dataKey:d,data:p}=a;if(null!=(c=null!=p&&p.length>0?p:null==u?void 0:u.slice(s,f+1)))return function(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:l,displayedData:c}=e;return c.map((e,c)=>{var u=nw(e,o);return"horizontal"===t?{x:nM({axis:r,ticks:i,bandSize:l,entry:e,index:c}),y:F(u)?null:n.scale(u),value:u,payload:e}:{x:F(u)?null:r.scale(u),y:nM({axis:n,ticks:a,bandSize:l,entry:e,index:c}),value:u,payload:e}})}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:d,bandSize:o,displayedData:c})}});function gR(e){var{legendPayload:t}=e;return r6(),nJ(),null}var g$=["type","layout","connectNulls","needClip"],gL=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function gU(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function gz(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gF(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gz(Object(r),!0).forEach(function(t){gB(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gz(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gB(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gK(){return(gK=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var gq=e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:nL(r,t),payload:e}]};function gW(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:nL(o,t),hide:l,type:e.tooltipType,color:e.stroke,unit:c}}}var gH=(e,t)=>"".concat(t,"px ").concat(e-t,"px"),gV=(e,t,r)=>{var n=r.reduce((e,t)=>e+t);if(!n)return gH(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,l=[],c=0,u=0;c<r.length;u+=r[c],++c)if(u+r[c]>a){l=[...r.slice(0,c),a-u];break}var s=l.length%2==0?[0,o]:[o];return[...function(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}(r,i),...l,...s].map(e=>"".concat(e,"px")).join(", ")};function gY(e){var{clipPathId:t,points:r,props:n}=e,{dot:i,dataKey:o,needClip:l}=n;if(null==r||!i&&1!==r.length)return null;var c=d9(i),u=pt(n,!1),s=pt(i,!0),f=r.map((e,t)=>{var n,l=gF(gF(gF({key:"dot-".concat(t),r:3},u),s),{},{index:t,cx:e.x,cy:e.y,dataKey:o,value:e.value,payload:e.payload,points:r});if(a.isValidElement(i))n=a.cloneElement(i,l);else if("function"==typeof i)n=i(l);else{var c=(0,y.$)("recharts-line-dot","boolean"!=typeof i?i.className:"");n=a.createElement(v1,gK({},l,{className:c}))}return n}),d={clipPath:l?"url(#clipPath-".concat(c?"":"dots-").concat(t,")"):null};return a.createElement(pX,gK({className:"recharts-line-dots",key:"dots"},d),f)}function gX(e){var{clipPathId:t,pathRef:r,points:n,strokeDasharray:i,props:o,showLabels:l}=e,{type:c,layout:u,connectNulls:s,needClip:f}=o,d=gF(gF({},pt(gU(o,g$),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:f?"url(#clipPath-".concat(t,")"):null,points:n,type:c,layout:u,connectNulls:s,strokeDasharray:null!=i?i:o.strokeDasharray});return a.createElement(a.Fragment,null,(null==n?void 0:n.length)>1&&a.createElement(y1,gK({},d,{pathRef:r})),a.createElement(gY,{points:n,clipPathId:t,props:o}),l&&gt.renderCallByParent(o,n))}function gG(e){var{clipPathId:t,props:r,pathRef:n,previousPointsRef:i,longestAnimatedLengthRef:o}=e,{points:l,strokeDasharray:c,isAnimationActive:u,animationBegin:s,animationDuration:f,animationEasing:d,animateNewValues:p,width:h,height:y,onAnimationEnd:v,onAnimationStart:g}=r,m=i.current,b=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,a.useRef)(R(t)),n=(0,a.useRef)(e);return n.current!==e&&(r.current=R(t),n.current=e),r.current}(r,"recharts-line-"),[x,w]=(0,a.useState)(!1),O=(0,a.useCallback)(()=>{"function"==typeof v&&v(),w(!1)},[v]),j=(0,a.useCallback)(()=>{"function"==typeof g&&g(),w(!0)},[g]),P=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}(n.current),E=o.current;return a.createElement(vS,{begin:s,duration:f,isActive:u,easing:d,from:{t:0},to:{t:1},onAnimationEnd:O,onAnimationStart:j,key:b},e=>{var u,{t:s}=e,f=Math.min(U(E,P+E)(s),P);if(u=c?gV(f,P,"".concat(c).split(/[,\s]+/gim).map(e=>parseFloat(e))):gH(P,f),m){var d=m.length/l.length,v=1===s?l:l.map((e,t)=>{var r=Math.floor(t*d);if(m[r]){var n=m[r],i=U(n.x,e.x),a=U(n.y,e.y);return gF(gF({},e),{},{x:i(s),y:a(s)})}if(p){var o=U(2*h,e.x),l=U(y/2,e.y);return gF(gF({},e),{},{x:o(s),y:l(s)})}return gF(gF({},e),{},{x:e.x,y:e.y})});return i.current=v,a.createElement(gX,{props:r,points:v,clipPathId:t,pathRef:n,showLabels:!x,strokeDasharray:u})}return s>0&&P>0&&(i.current=l,o.current=f),a.createElement(gX,{props:r,points:l,clipPathId:t,pathRef:n,showLabels:!x,strokeDasharray:u})})}function gZ(e){var{clipPathId:t,props:r}=e,{points:n,isAnimationActive:i}=r,o=(0,a.useRef)(null),l=(0,a.useRef)(0),c=(0,a.useRef)(null),u=o.current;return i&&n&&n.length&&u!==n?a.createElement(gG,{props:r,clipPathId:t,previousPointsRef:o,longestAnimatedLengthRef:l,pathRef:c}):a.createElement(gX,{props:r,points:n,clipPathId:t,pathRef:c,showLabels:!0})}var gQ=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:nw(e.payload,t)});class gJ extends a.Component{constructor(){super(...arguments),gB(this,"id",R("recharts-line-"))}render(){var e,{hide:t,dot:r,points:n,className:i,xAxisId:o,yAxisId:l,top:c,left:u,width:s,height:f,id:d,needClip:p,layout:h}=this.props;if(t)return null;var v=(0,y.$)("recharts-line",i),g=F(d)?this.id:d,{r:m=3,strokeWidth:b=2}=null!=(e=pt(r,!1))?e:{r:3,strokeWidth:2},x=d9(r),w=2*m+b;return a.createElement(a.Fragment,null,a.createElement(pX,{className:v},p&&a.createElement("defs",null,a.createElement(g_,{clipPathId:g,xAxisId:o,yAxisId:l}),!x&&a.createElement("clipPath",{id:"clipPath-dots-".concat(g)},a.createElement("rect",{x:u-w/2,y:c-w/2,width:s+w,height:f+w}))),a.createElement(gZ,{props:this.props,clipPathId:g}),a.createElement(gm,{direction:"horizontal"===h?"y":"x"},a.createElement(gl,{xAxisId:o,yAxisId:l,data:n,dataPointFormatter:gQ,errorBarOffset:0},this.props.children))),a.createElement(gE,{activeDot:this.props.activeDot,points:n,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var g0={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!pM.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function g1(e){var t=pj(e,g0),{activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:o,animationEasing:l,connectNulls:c,dot:u,hide:s,isAnimationActive:f,label:d,legendType:p,xAxisId:h,yAxisId:y}=t,v=gU(t,gL),{needClip:g}=gA(h,y),{height:m,width:b,left:x,top:w}=n5(),O=n7(),j=nJ(),P=(0,a.useMemo)(()=>({dataKey:e.dataKey,data:e.data}),[e.dataKey,e.data]),E=r9(e=>gI(e,h,y,j,P));return"horizontal"!==O&&"vertical"!==O?null:a.createElement(gJ,gK({},v,{connectNulls:c,dot:u,activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:o,animationEasing:l,isAnimationActive:f,hide:s,label:d,legendType:p,xAxisId:h,yAxisId:y,points:E,layout:O,height:m,width:b,left:x,top:w,needClip:g}))}class g2 extends a.PureComponent{render(){return a.createElement(gu,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},a.createElement(gR,{legendPayload:gq(this.props)}),a.createElement(gS,{fn:gW,args:this.props}),a.createElement(g1,this.props))}}gB(g2,"displayName","Line"),gB(g2,"defaultProps",g0);var g3=r(1312),g5=r(22),g6=r(2688);let g4=(0,g6.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),g8=(0,g6.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var g7=r(8559);let g9=(0,g6.A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),me=(0,g6.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);function mt(){let e=(0,o._8)(),t=(0,o.tK)(),r=[{title:"Total Patients",value:e.totalPatients.toLocaleString(),icon:g3.A,color:"bg-blue-100 border-blue-600 text-blue-800",change:"+12% from last month"},{title:"Active Cases",value:e.activeCases.toString(),icon:g5.A,color:"bg-yellow-100 border-yellow-600 text-yellow-800",change:"3 critical cases"},{title:"Diagnoses This Week",value:e.diagnosesThisWeek.toString(),icon:g4,color:"bg-green-100 border-green-600 text-green-800",change:"+8% from last week"},{title:"Alerts",value:e.alerts.toString(),icon:g8,color:"bg-red-100 border-red-600 text-red-800",change:"2 require immediate attention"}];return(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"heading-brutal",children:"Dashboard Overview"}),(0,i.jsx)("p",{className:"text-medical mt-2",children:"Welcome to the VetAI Diagnostic System. Monitor your practice's key metrics and recent activity."})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-green-100 border-2 border-green-600 rounded-xl",children:[(0,i.jsx)(g7.A,{className:"h-5 w-5 text-green-600"}),(0,i.jsx)("span",{className:"font-bold text-green-800",children:"AI System Active"})]})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:r.map((e,t)=>{let r=e.icon;return(0,i.jsxs)(l,{className:"card-brutal",children:[(0,i.jsx)(c,{className:"pb-3",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)(u,{className:"text-lg font-bold text-black",children:e.title}),(0,i.jsx)("div",{className:`p-2 rounded-xl border-2 ${e.color}`,children:(0,i.jsx)(r,{className:"h-5 w-5"})})]})}),(0,i.jsxs)(s,{children:[(0,i.jsx)("div",{className:"text-3xl font-black text-black mb-2",children:e.value}),(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.change})]})]},t)})}),(0,i.jsxs)(l,{className:"card-brutal",children:[(0,i.jsxs)(c,{children:[(0,i.jsx)(u,{className:"subheading-brutal",children:"Disease Frequency Over Time"}),(0,i.jsx)("p",{className:"text-medical",children:"Track the most common conditions diagnosed in your practice"})]}),(0,i.jsx)(s,{children:(0,i.jsx)("div",{className:"h-80",children:(0,i.jsx)(H,{width:"100%",height:"100%",children:(0,i.jsxs)(pk,{data:e.diseaseFrequency,children:[(0,i.jsx)(hY,{strokeDasharray:"3 3",stroke:"#e5e7eb"}),(0,i.jsx)(h3,{dataKey:"date",stroke:"#374151",fontSize:12,fontWeight:"600"}),(0,i.jsx)(yr,{stroke:"#374151",fontSize:12,fontWeight:"600"}),(0,i.jsx)(vJ,{contentStyle:{backgroundColor:"white",border:"4px solid black",borderRadius:"12px",boxShadow:"4px 4px 0px 0px rgba(0,0,0,1)",fontWeight:"bold"}}),(0,i.jsx)(g2,{type:"monotone",dataKey:"count",stroke:"#3B82F6",strokeWidth:4,dot:{fill:"#3B82F6",strokeWidth:2,r:6},activeDot:{r:8,stroke:"#1D4ED8",strokeWidth:2}})]})})})})]}),(0,i.jsxs)(l,{className:"card-brutal",children:[(0,i.jsxs)(c,{children:[(0,i.jsx)(u,{className:"subheading-brutal",children:"Recent Cases"}),(0,i.jsx)("p",{className:"text-medical",children:"Latest patient cases and their diagnostic status"})]}),(0,i.jsx)(s,{children:(0,i.jsxs)(x,{children:[(0,i.jsx)(w,{children:(0,i.jsxs)(j,{className:"border-b-2 border-black",children:[(0,i.jsx)(P,{className:"font-black text-black",children:"Animal ID"}),(0,i.jsx)(P,{className:"font-black text-black",children:"Species"}),(0,i.jsx)(P,{className:"font-black text-black",children:"Age"}),(0,i.jsx)(P,{className:"font-black text-black",children:"Symptoms"}),(0,i.jsx)(P,{className:"font-black text-black",children:"Diagnosis"}),(0,i.jsx)(P,{className:"font-black text-black",children:"Status"}),(0,i.jsx)(P,{className:"font-black text-black",children:"Date"})]})}),(0,i.jsx)(O,{children:t.map(e=>(0,i.jsxs)(j,{className:"border-b border-gray-200",children:[(0,i.jsx)(E,{className:"font-bold text-black",children:e.animalId}),(0,i.jsx)(E,{className:"font-medium",children:e.species}),(0,i.jsxs)(E,{className:"font-medium",children:[e.age," years"]}),(0,i.jsx)(E,{children:(0,i.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.symptoms.slice(0,2).map((e,t)=>(0,i.jsx)(b,{variant:"outline",className:"text-xs border-2 border-gray-400",children:e},t)),e.symptoms.length>2&&(0,i.jsxs)(b,{variant:"outline",className:"text-xs border-2 border-gray-400",children:["+",e.symptoms.length-2," more"]})]})}),(0,i.jsx)(E,{children:e.diagnosis?(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-black",children:e.diagnosis}),e.confidence&&(0,i.jsxs)("div",{className:"text-xs text-gray-600",children:[e.confidence,"% confidence"]})]}):(0,i.jsx)("span",{className:"text-gray-500 font-medium",children:"Pending"})}),(0,i.jsx)(E,{children:(0,i.jsxs)(b,{className:`${(0,o.qY)(e.status)} border-2 font-bold`,children:["pending"===e.status&&(0,i.jsx)(g9,{className:"h-3 w-3 mr-1"}),"diagnosed"===e.status&&(0,i.jsx)(me,{className:"h-3 w-3 mr-1"}),"treated"===e.status&&(0,i.jsx)(me,{className:"h-3 w-3 mr-1"}),"follow-up"===e.status&&(0,i.jsx)(g7.A,{className:"h-3 w-3 mr-1"}),e.status.charAt(0).toUpperCase()+e.status.slice(1)]})}),(0,i.jsx)(E,{className:"font-medium text-gray-700",children:(0,o.Yq)(e.createdAt)})]},e.id))})]})})]})]})}},1337:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(6777),i=r(2750);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},1424:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},1428:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return o},isInterceptionRouteAppPath:function(){return a}});let n=r(4722),i=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function o(e){let t,r,a;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=o.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},1640:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},1653:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return h},normalizeMetadataRoute:function(){return p}});let n=r(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),a=r(6341),o=r(4396),l=r(660),c=r(4722),u=r(2958),s=r(5499);function f(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,s.isGroupSegment)(e)||(0,s.isParallelRouteSegment)(e))&&(r=(0,l.djb2Hash)(t).toString(36).slice(0,6)),r}function d(e,t,r){let n=(0,c.normalizeAppPath)(e),l=(0,o.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),s=(0,a.interpolateDynamicPath)(n,t,l),{name:d,ext:p}=i.default.parse(r),h=f(i.default.posix.join(e,d)),y=h?`-${h}`:"";return(0,u.normalizePathSep)(i.default.join(s,`${d}${y}${p}`))}function p(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=f(e),!t.endsWith("/route")){let{dir:e,name:n,ext:a}=i.default.parse(t);t=i.default.posix.join(e,`${n}${r?`-${r}`:""}${a}`,"route")}return t}function h(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,i=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${i}`)+(r?"/route":"")}},1706:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},2066:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4454);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},2371:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2923);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(5362);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},2457:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2066),i=r(2371);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},2681:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},2728:(e,t,r)=>{e.exports=r(9911).isEqual},2750:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5708);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},2923:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(1653),i=r(1428),a=r(7469),o=r(3457),l=r(1251);function c(e,t,r,n=new Map,s){let f=s?.(e,t,r,n);if(null!=f)return f;if(o.isPrimitive(e))return e;if(n.has(e))return n.get(e);if(Array.isArray(e)){let t=Array(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,s);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[i,a]of(n.set(e,t),e))t.set(i,c(a,i,r,n,s));return t}if(e instanceof Set){let t=new Set;for(let i of(n.set(e,t),e))t.add(c(i,void 0,r,n,s));return t}if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return e.subarray();if(l.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,s);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return n.set(e,t),u(t,e,r,n,s),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return n.set(e,t),u(t,e,r,n,s),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return n.set(e,t),u(t,e,r,n,s),t}if(e instanceof Error){let t=new e.constructor;return n.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,u(t,e,r,n,s),t}if("object"==typeof e&&function(e){switch(i.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return n.set(e,t),u(t,e,r,n,s),t}return e}function u(e,t,r=e,i,a){let o=[...Object.keys(t),...n.getSymbols(t)];for(let n=0;n<o.length;n++){let l=o[n],u=Object.getOwnPropertyDescriptor(e,l);(null==u||u.writable)&&(e[l]=c(t[l],l,r,i,a))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=u},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3068:(e,t,r)=>{e.exports=r(5446).sortBy},3084:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(911);t.throttle=function(e,t=0,r={}){"object"!=typeof r&&(r={});let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,trailing:a,maxWait:t})}},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3332:(e,t,r)=>{"use strict";var n=r(3210),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,c=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return l(function(){i.value=r,i.getSnapshot=t,u(i)&&s({inst:i})},[e,r,t]),o(function(){return u(i)&&s({inst:i}),e(function(){u(i)&&s({inst:i})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},3457:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},3567:(e,t,r)=>{"use strict";var n=r(3210);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},3574:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},3731:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5385),i=r(1424),a=r(6349);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(4827);let n=r(2785);function i(e,t,r){void 0===r&&(r=!0);let i=new URL("http://n"),a=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:o,searchParams:l,search:c,hash:u,href:s,origin:f}=new URL(e,a);if(f!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:o,query:r?(0,n.searchParamsToUrlQuery)(l):void 0,search:c,hash:u,href:s.slice(f.length)}}},3854:(e,t,r)=>{e.exports=r(5263).uniqBy},3873:e=>{"use strict";e.exports=require("path")},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return y},getNamedRouteRegex:function(){return h},getRouteRegex:function(){return f},parseParameter:function(){return c}});let n=r(6143),i=r(1437),a=r(3293),o=r(2887),l=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function c(e){let t=e.match(l);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function s(e,t,r){let n={},c=1,s=[];for(let f of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>f.startsWith(e)),o=f.match(l);if(e&&o&&o[2]){let{key:t,optional:r,repeat:i}=u(o[2]);n[t]={pos:c++,repeat:i,optional:r},s.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(o&&o[2]){let{key:e,repeat:t,optional:i}=u(o[2]);n[e]={pos:c++,repeat:t,optional:i},r&&o[1]&&s.push("/"+(0,a.escapeStringRegexp)(o[1]));let l=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&o[1]&&(l=l.substring(1)),s.push(l)}else s.push("/"+(0,a.escapeStringRegexp)(f));t&&o&&o[3]&&s.push((0,a.escapeStringRegexp)(o[3]))}return{parameterizedRoute:s.join(""),groups:n}}function f(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:o}=s(e,r,n),l=a;return i||(l+="(?:/)?"),{re:RegExp("^"+l+"$"),groups:o}}function d(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:o,keyPrefix:l,backreferenceDuplicateKeys:c}=e,{key:s,optional:f,repeat:d}=u(i),p=s.replace(/\W/g,"");l&&(p=""+l+p);let h=!1;(0===p.length||p.length>30)&&(h=!0),isNaN(parseInt(p.slice(0,1)))||(h=!0),h&&(p=n());let y=p in o;l?o[p]=""+l+s:o[p]=s;let v=r?(0,a.escapeStringRegexp)(r):"";return t=y&&c?"\\k<"+p+">":d?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",f?"(?:/"+v+t+")?":"/"+v+t}function p(e,t,r,c,u){let s,f=(s=0,()=>{let e="",t=++s;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},h=[];for(let s of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>s.startsWith(e)),o=s.match(l);if(e&&o&&o[2])h.push(d({getSafeRouteKey:f,interceptionMarker:o[1],segment:o[2],routeKeys:p,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(o&&o[2]){c&&o[1]&&h.push("/"+(0,a.escapeStringRegexp)(o[1]));let e=d({getSafeRouteKey:f,segment:o[2],routeKeys:p,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});c&&o[1]&&(e=e.substring(1)),h.push(e)}else h.push("/"+(0,a.escapeStringRegexp)(s));r&&o&&o[3]&&h.push((0,a.escapeStringRegexp)(o[3]))}return{namedParameterizedRoute:h.join(""),routeKeys:p}}function h(e,t){var r,n,i;let a=p(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),o=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(o+="(?:/)?"),{...f(e,t),namedRegex:"^"+o+"$",routeKeys:a.routeKeys}}function y(e,t){let{parameterizedRoute:r}=s(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},4454:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2066),i=r(1640),a=r(3457),o=r(1706);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return c(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,s))return!1;return!0}if(t instanceof Set)return u(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function c(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let c=0;c<e.length;c++){if(i.has(c))continue;let u=e[c],s=!1;if(r(u,o,a,e,t,n)&&(s=!0),s){i.add(c),l=!0;break}}if(!l)return!1}return!0}function u(e,t,r,n){return 0===t.size||e instanceof Set&&c([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,c){let u=r(t,n,i,a,o,c);return void 0!==u?!!u:l(t,n,e,c)},new Map)},t.isSetMatch=u},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return o}});let n=r(5531),i=r(5499);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return m},MissingStaticPage:function(){return g},NormalizeError:function(){return y},PageNotFoundError:function(){return v},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return c},getLocationOrigin:function(){return o},getURL:function(){return l},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function l(){let{href:e}=window.location,t=o();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class y extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class m extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},5100:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let i,a=null,o=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),c=()=>{null!==a&&(e.apply(i,a),i=void 0,a=null)},u=()=>{l&&c(),p()},s=null,f=()=>{null!=s&&clearTimeout(s),s=setTimeout(()=>{s=null,u()},t)},d=()=>{null!==s&&(clearTimeout(s),s=null)},p=()=>{d(),i=void 0,a=null},h=function(...e){if(r?.aborted)return;i=this,a=e;let t=null==s;f(),o&&t&&c()};return h.schedule=f,h.cancel=p,h.flush=()=>{d(),c()},r?.addEventListener("abort",p,{once:!0}),h}},5263:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9618),i=r(2681),a=r(830),o=r(7617);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},5314:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",a=r+1;a<e.length;){var o=e.charCodeAt(a);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||95===o){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===n){var l=1,c="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){c+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--l){a++;break}}else if("("===e[a]&&(l++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);c+=e[a++]}if(l)throw TypeError("Unbalanced pattern at "+r);if(!c)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:c}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,a=void 0===n?"./":n,o="[^"+i(t.delimiter||"/#?")+"]+?",l=[],c=0,u=0,s="",f=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},d=function(e){var t=f(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=f("CHAR")||f("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var h=f("CHAR"),y=f("NAME"),v=f("PATTERN");if(y||v){var g=h||"";-1===a.indexOf(g)&&(s+=g,g=""),s&&(l.push(s),s=""),l.push({name:y||c++,prefix:g,suffix:"",pattern:v||o,modifier:f("MODIFIER")||""});continue}var m=h||f("ESCAPED_CHAR");if(m){s+=m;continue}if(s&&(l.push(s),s=""),f("OPEN")){var g=p(),b=f("NAME")||"",x=f("PATTERN")||"",w=p();d("CLOSE"),l.push({name:b||(x?c++:""),pattern:b&&!x?o:x,prefix:g,suffix:w,modifier:f("MODIFIER")||""});continue}d("END")}return l}function r(e,t){void 0===t&&(t={});var r=a(t),n=t.encode,i=void 0===n?function(e){return e}:n,o=t.validate,l=void 0===o||o,c=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var a=e[n];if("string"==typeof a){r+=a;continue}var o=t?t[a.name]:void 0,u="?"===a.modifier||"*"===a.modifier,s="*"===a.modifier||"+"===a.modifier;if(Array.isArray(o)){if(!s)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===o.length){if(u)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var f=0;f<o.length;f++){var d=i(o[f],a);if(l&&!c[n].test(d))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+d+'"');r+=a.prefix+d+a.suffix}continue}if("string"==typeof o||"number"==typeof o){var d=i(String(o),a);if(l&&!c[n].test(d))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+d+'"');r+=a.prefix+d+a.suffix;continue}if(!u){var p=s?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+p)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var a=n[0],o=n.index,l=Object.create(null),c=1;c<n.length;c++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?l[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):l[r.name]=i(n[e],r)}}(c);return{path:a,index:o,params:l}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function o(e,t,r){void 0===r&&(r={});for(var n=r.strict,o=void 0!==n&&n,l=r.start,c=r.end,u=r.encode,s=void 0===u?function(e){return e}:u,f="["+i(r.endsWith||"")+"]|$",d="["+i(r.delimiter||"/#?")+"]",p=void 0===l||l?"^":"",h=0;h<e.length;h++){var y=e[h];if("string"==typeof y)p+=i(s(y));else{var v=i(s(y.prefix)),g=i(s(y.suffix));if(y.pattern)if(t&&t.push(y),v||g)if("+"===y.modifier||"*"===y.modifier){var m="*"===y.modifier?"?":"";p+="(?:"+v+"((?:"+y.pattern+")(?:"+g+v+"(?:"+y.pattern+"))*)"+g+")"+m}else p+="(?:"+v+"("+y.pattern+")"+g+")"+y.modifier;else p+="("+y.pattern+")"+y.modifier;else p+="(?:"+v+g+")"+y.modifier}}if(void 0===c||c)o||(p+=d+"?"),p+=r.endsWith?"(?="+f+")":"$";else{var b=e[e.length-1],x="string"==typeof b?d.indexOf(b[b.length-1])>-1:void 0===b;o||(p+="(?:"+d+"(?="+f+"))?"),x||(p+="(?="+d+"|"+f+")")}return new RegExp(p,a(r))}function l(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var c=0;c<i.length;c++)r.push({name:c,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return l(e,r,n).source}).join("|")+")",a(n)):o(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(l(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=o,t.pathToRegexp=l})(),e.exports=t})()},5385:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},5446:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7586),i=r(8382),a=r(6777);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return s},matchHas:function(){return u},parseDestination:function(){return f},prepareDestination:function(){return d}});let n=r(5362),i=r(3293),a=r(6759),o=r(1437),l=r(8212);function c(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},a=r=>{let n,a=r.key;switch(r.type){case"header":a=a.toLowerCase(),n=e.headers[a];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,l.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>a(e))||n.some(e=>a(e)))&&i}function s(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function f(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,a.parseUrl)(t),n=r.pathname;n&&(n=c(n));let o=r.href;o&&(o=c(o));let l=r.hostname;l&&(l=c(l));let u=r.hash;return u&&(u=c(u)),{...r,pathname:n,hostname:l,href:o,hash:u}}function d(e){let t,r,i=Object.assign({},e.query),a=f(e),{hostname:l,query:u}=a,d=a.pathname;a.hash&&(d=""+d+a.hash);let p=[],h=[];for(let e of((0,n.pathToRegexp)(d,h),h))p.push(e.name);if(l){let e=[];for(let t of((0,n.pathToRegexp)(l,e),e))p.push(t.name)}let y=(0,n.compile)(d,{validate:!1});for(let[r,i]of(l&&(t=(0,n.compile)(l,{validate:!1})),Object.entries(u)))Array.isArray(i)?u[r]=i.map(t=>s(c(t),e.params)):"string"==typeof i&&(u[r]=s(c(i),e.params));let v=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!v.some(e=>p.includes(e)))for(let t of v)t in u||(u[t]=e.params[t]);if((0,o.isInterceptionRouteAppPath)(d))for(let t of d.split("/")){let r=o.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=y(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=n,a.hash=(i?"#":"")+(i||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...i,...a.query},{newUrl:r,destQuery:u,parsedDestination:a}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},5664:(e,t,r)=>{e.exports=r(7509).get},5708:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5819);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},5819:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},6021:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5819),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},6023:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return g},getUtils:function(){return v},interpolateDynamicPath:function(){return h},normalizeDynamicRouteParams:function(){return y},normalizeVercelUrl:function(){return p}});let n=r(9551),i=r(1959),a=r(2437),o=r(4396),l=r(8034),c=r(5526),u=r(2887),s=r(4722),f=r(6143),d=r(7912);function p(e,t,r){let i=(0,n.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let n=e!==f.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(f.NEXT_QUERY_PARAM_PREFIX),a=e!==f.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(f.NEXT_INTERCEPTION_MARKER_PREFIX);(n||a||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete i.query[e]}e.url=(0,n.format)(i)}function h(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let i,{optional:a,repeat:o}=r.groups[n],l=`[${o?"...":""}${n}]`;a&&(l=`[${l}]`);let c=t[n];i=Array.isArray(c)?c.map(e=>e&&encodeURIComponent(e)).join("/"):c?encodeURIComponent(c):"",e=e.replaceAll(l,i)}return e}function y(e,t,r,n){let i={};for(let a of Object.keys(t.groups)){let o=e[a];"string"==typeof o?o=(0,s.normalizeRscURL)(o):Array.isArray(o)&&(o=o.map(s.normalizeRscURL));let l=r[a],c=t.groups[a].optional;if((Array.isArray(l)?l.some(e=>Array.isArray(o)?o.some(t=>t.includes(e)):null==o?void 0:o.includes(e)):null==o?void 0:o.includes(l))||void 0===o&&!(c&&n))return{params:{},hasValidParams:!1};c&&(!o||Array.isArray(o)&&1===o.length&&("index"===o[0]||o[0]===`[[...${a}]]`))&&(o=void 0,delete e[a]),o&&"string"==typeof o&&t.groups[a].repeat&&(o=o.split("/")),o&&(i[a]=o)}return{params:i,hasValidParams:!0}}function v({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:s,trailingSlash:f,caseSensitive:v}){let g,m,b;return s&&(g=(0,o.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(m=(0,l.getRouteMatcher)(g))(e)),{handleRewrites:function(o,l){let d={},p=l.pathname,h=n=>{let u=(0,a.getPathMatch)(n.source+(f?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!v});if(!l.pathname)return!1;let h=u(l.pathname);if((n.has||n.missing)&&h){let e=(0,c.matchHas)(o,l.query,n.has,n.missing);e?Object.assign(h,e):h=!1}if(h){let{parsedDestination:a,destQuery:o}=(0,c.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:h,query:l.query});if(a.protocol)return!0;if(Object.assign(d,o,h),Object.assign(l.query,a.query),delete a.query,Object.assign(l,a),!(p=l.pathname))return!1;if(r&&(p=p.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(p,t.locales);p=e.pathname,l.query.nextInternalLocale=e.detectedLocale||h.nextInternalLocale}if(p===e)return!0;if(s&&m){let e=m(p);if(e)return l.query={...l.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])h(e);if(p!==e){let t=!1;for(let e of n.afterFiles||[])if(t=h(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(p||"");return t===(0,u.removeTrailingSlash)(e)||(null==m?void 0:m(t))})()){for(let e of n.fallback||[])if(t=h(e))break}}return d},defaultRouteRegex:g,dynamicRouteMatcher:m,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!g)return null;let{groups:t,routeKeys:r}=g,n=(0,l.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,d.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let i={};for(let e of Object.keys(r)){let a=r[e];if(!a)continue;let o=t[a],l=n[e];if(!o.optional&&!l)return null;i[o.pos]=l}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>g&&b?y(e,g,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,g),interpolateDynamicPath:(e,t)=>h(e,t,g)}}function g(e,t){return"string"==typeof e[f.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[f.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[f.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6349:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8130);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),o=(r||{}).decode||e,l=0;l<a.length;l++){var c=a[l],u=c.indexOf("=");if(!(u<0)){var s=c.substr(0,u).trim(),f=c.substr(++u,c.length).trim();'"'==f[0]&&(f=f.slice(1,-1)),void 0==i[s]&&(i[s]=function(e,t){try{return t(e)}catch(t){return e}}(f,o))}}return i},t.serialize=function(e,t,n){var a=n||{},o=a.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var l=o(t);if(l&&!i.test(l))throw TypeError("argument val is invalid");var c=e+"="+l;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(u)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");c+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");c+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");c+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(c+="; HttpOnly"),a.secure&&(c+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6431:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let n=r(2785),i=r(3736);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6777:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8150),i=r(6349),a=r(1640),o=r(1706);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},6895:(e,t,r)=>{"use strict";r(3567)},7379:(e,t,r)=>{"use strict";e.exports=r(3332)},7469:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},7509:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5314),i=r(6431),a=r(657),o=r(3574);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var c=t,u=r,s=l;if(0===u.length)return s;let e=c;for(let t=0;t<u.length;t++){if(null==e||n.isUnsafeProperty(u[t]))return s;e=e[u[t]]}return void 0===e?s:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},7586:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(6023),i=r(6021),a=r(3574);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},c=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t,u=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:u.map(t=>c(t,e))})).slice().sort((e,t)=>{for(let i=0;i<u.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},7617:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2681),i=r(144),a=r(2457),o=r(415);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},7668:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,a=r?Symbol.for("react.fragment"):60107,o=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,s=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,h=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),y=r?Symbol.for("react.lazy"):60116;function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case s:case f:case a:case l:case o:case p:return e;default:switch(e=e&&e.$$typeof){case u:case d:case y:case h:case c:return e;default:return t}}case i:return t}}}r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope");t.isFragment=function(e){return v(e)===a}},7766:(e,t,r)=>{e.exports=r(3084).throttle},7841:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(6431),i=r(8150),a=r(9243),o=r(3574);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(4827);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},o={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?o[e]=r.split("/").map(e=>a(e)):o[e]=a(r))}return o}}},8130:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},8150:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return l},STATIC_METADATA_IMAGES:function(){return o},getExtensionRegexString:function(){return c},isMetadataPage:function(){return f},isMetadataRoute:function(){return d},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return s}});let n=r(2958),i=r(4722),a=r(554),o={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},l=["js","jsx","ts","tsx"],c=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,r){let i=(r?"":"?")+"$",a=`\\d?${r?"":"(-\\w{6})?"}`,l=[RegExp(`^[\\\\/]robots${c(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${c(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${c(["xml"],t)}${i}`),RegExp(`[\\\\/]${o.icon.filename}${a}${c(o.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${o.apple.filename}${a}${c(o.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${o.openGraph.filename}${a}${c(o.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${o.twitter.filename}${a}${c(o.twitter.extensions,t)}${i}`)],u=(0,n.normalizePathSep)(e);return l.some(e=>e.test(u))}function s(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function f(e){return!(0,a.isAppRouteRoute)(e)&&u(e,[],!1)}function d(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&u(t,[],!1)}},8382:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9138:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9862);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},9243:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(1428);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9404:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(175),i=r(1653),a=r(1428),o=r(7469),l=r(1706);t.isEqualWith=function(e,t,r){return function e(t,r,c,u,s,f,d){let p=d(t,r,c,u,s,f);if(void 0!==p)return p;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,c,u,s){if(Object.is(r,c))return!0;let f=a.getTag(r),d=a.getTag(c);if(f===o.argumentsTag&&(f=o.objectTag),d===o.argumentsTag&&(d=o.objectTag),f!==d)return!1;switch(f){case o.stringTag:return r.toString()===c.toString();case o.numberTag:{let e=r.valueOf(),t=c.valueOf();return l.eq(e,t)}case o.booleanTag:case o.dateTag:case o.symbolTag:return Object.is(r.valueOf(),c.valueOf());case o.regexpTag:return r.source===c.source&&r.flags===c.flags;case o.functionTag:return r===c}let p=(u=u??new Map).get(r),h=u.get(c);if(null!=p&&null!=h)return p===c;u.set(r,c),u.set(c,r);try{switch(f){case o.mapTag:if(r.size!==c.size)return!1;for(let[t,n]of r.entries())if(!c.has(t)||!e(n,c.get(t),t,r,c,u,s))return!1;return!0;case o.setTag:{if(r.size!==c.size)return!1;let t=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,c,u,s));if(-1===o)return!1;n.splice(o,1)}return!0}case o.arrayTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:case o.bigUint64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.bigInt64ArrayTag:case o.float32ArrayTag:case o.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(r)!==Buffer.isBuffer(c)||r.length!==c.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],c[t],t,r,c,u,s))return!1;return!0;case o.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(c),u,s);case o.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(c),u,s);case o.errorTag:return r.name===c.name&&r.message===c.message;case o.objectTag:{if(!(t(r.constructor,c.constructor,u,s)||n.isPlainObject(r)&&n.isPlainObject(c)))return!1;let a=[...Object.keys(r),...i.getSymbols(r)],o=[...Object.keys(c),...i.getSymbols(c)];if(a.length!==o.length)return!1;for(let t=0;t<a.length;t++){let n=a[t],i=r[n];if(!Object.hasOwn(c,n))return!1;let o=c[n];if(!e(i,o,n,r,c,u,s))return!1}return!0}default:return!1}}finally{u.delete(r),u.delete(c)}}(t,r,f,d)}(e,t,void 0,void 0,void 0,void 0,r)}},9474:(e,t,r)=>{e.exports=r(3731).last},9551:e=>{"use strict";e.exports=require("url")},9618:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},9632:(e,t,r)=>{"use strict";e.exports=r(7668)},9733:(e,t,r)=>{"use strict";e.exports=r(907)},9862:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2923),i=r(7469);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let c=t?.(r,a,o,l);if(null!=c)return c;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},9899:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},9911:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9404),i=r(15);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,582,823],()=>r(1183));module.exports=n})();